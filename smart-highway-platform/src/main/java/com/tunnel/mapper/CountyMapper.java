package com.tunnel.mapper;

import com.tunnel.domain.County;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 农村项目信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Mapper
public interface CountyMapper {
    /**
     * 查询农村项目信息
     *
     * @param id 农村项目信息主键
     * @return 农村项目信息
     */
    public County selectCountyById(Long id);

    /**
     * 查询农村项目信息列表
     *
     * @param county 农村项目信息
     * @return 农村项目信息集合
     */
    public List<County> selectCountyList(County county);

    /**
     * 新增农村项目信息
     *
     * @param county 农村项目信息
     * @return 结果
     */
    public int insertCounty(County county);

    /**
     * 修改农村项目信息
     *
     * @param county 农村项目信息
     * @return 结果
     */
    public int updateCounty(County county);

    /**
     * 删除农村项目信息
     *
     * @param id 农村项目信息主键
     * @return 结果
     */
    public int deleteCountyById(Long id);

    /**
     * 批量删除农村项目信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCountyByIds(Long[] ids);

    /**
     * 根据报告编号查询农村项目信息
     *
     * @param reportNo 报告编号
     * @return 农村项目信息
     */
    public County selectCountyByReportNo(@Param("reportNo") String reportNo);

    /**
     * 检查报告编号是否唯一
     *
     * @param county 农村项目信息
     * @return 结果
     */
    public County checkReportNoUnique(County county);

    /**
     * 获取农村项目信息记录数
     *
     * @param county 查询条件
     * @return 记录数
     */
    public int countCounty(County county);
} 