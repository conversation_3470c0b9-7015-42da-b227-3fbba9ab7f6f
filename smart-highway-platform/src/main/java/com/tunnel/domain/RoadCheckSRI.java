package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 路面横向力系数检测信息对象 road_check_sfc
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RoadCheckSRI extends BaseRoadCheckEntity {
    private static final long serialVersionUID = 1L;

    // 基础字段已在BaseRoadCheckEntity中定义

    /** 测试速度(km/h) */
    @Excel(name = "测试速度(km/h)", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal testSpeed;

    /** 测试温度(℃) */
    @Excel(name = "测试温度(℃)", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal testTemp;

    /** SFC系数 */
    @Excel(name = "SFC实测", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal sfcValue;
    
    /** SFC(60km/h) */
    @Excel(name = "SFC(50km/h)", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal sfc50;
    
    /** SFC(50km/h, 20℃) */
    @Excel(name = "SFC(50km/h, 20℃)", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal sfc50At20;

    /** 备注 */
    @Excel(name = "备注", cellType = Excel.ColumnType.STRING)
    private String remark;
    
    // 路面类型、方向、时间字段等已在BaseRoadCheckEntity中定义

    /** 道路名称 - 非数据库字段，用于前端展示 */
    private String roadName;
} 