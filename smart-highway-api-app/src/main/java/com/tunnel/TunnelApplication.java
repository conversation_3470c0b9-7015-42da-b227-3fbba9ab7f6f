package com.tunnel;

import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 启动程序
 *
 * <AUTHOR>
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@EnableTransactionManagement
public class TunnelApplication {
    public static void main(String[] args) {
        // 解决POI安全限制：设置ZIP文件的最小压缩比率，避免Zip bomb检测误报
        // 将比率从默认的0.01降低到0.001，允许压缩比更低的文件
        ZipSecureFile.setMinInflateRatio(0.001);
        
        SpringApplication.run(TunnelApplication.class, args);
    }
}
