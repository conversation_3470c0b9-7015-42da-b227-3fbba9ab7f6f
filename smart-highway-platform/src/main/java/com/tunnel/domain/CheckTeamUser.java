package com.tunnel.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 检测分组用户关联对象 sc_check_team_user
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CheckTeamUser extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 分组ID */
    @Excel(name = "分组ID")
    @NotNull(message = "分组ID不能为空")
    private Long teamId;

    /** 用户ID */
    @Excel(name = "用户ID")
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /** 在分组内的排序 */
    @Excel(name = "排序")
    private Integer sortNum;

    /** 路线类型:1.高速,2.国省道 */
    @Excel(name = "路线类型", readConverterExp = "1=高速,2=国省道")
    private Integer type;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 分组名称 - 非数据库字段，用于前端展示 */
    private String teamName;

    /** 用户名称 - 非数据库字段，用于前端展示 */
    private String userName;

    /** 用户职位 - 非数据库字段，用于前端展示 */
    private String userPosition;

    /** 用户职称 - 非数据库字段，用于前端展示 */
    private String userTitle;

    /** 用户证书编号 - 非数据库字段，用于前端展示 */
    private String userCertificateNo;

    /** 用户联系电话 - 非数据库字段，用于前端展示 */
    private String userPhone;

    /** 用户邮箱 - 非数据库字段，用于前端展示 */
    private String userEmail;
} 