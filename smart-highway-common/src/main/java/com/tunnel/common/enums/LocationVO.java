package com.tunnel.common.enums;

import com.tunnel.common.exception.ServiceException;

/**
 * 检查位置枚举
 * <AUTHOR>
 */
public enum LocationVO {
//    位置编码
//    洞内	上行	001	上	风机、手报
//	/	/
//    下行	002	下
//    变电所	小桩号配电房	003	上	低压、高压
//    变电所	大桩号配电房	004	下
///	/	/		/
//    变电所	小桩号箱变	005	上
//    变电所	大桩号箱变	006	下
//    洞外	上行入口	007	上	洞外路灯
//    洞外	上行出口	008	上
//    洞外	下行入口	009	下
//    洞外	下行出口	010	下
//    洞外	洞外上行	011	上	可变标志、摄像机
//    洞外	洞外下行	012	下
//    洞室（埋地变）	上行1号洞室	101	上	低压、高压
//    下行1号洞室	201	下
//    上行2号洞室	102	上
//    下行2号洞室	202	下
//    上行3号洞室	103	上
//    下行3号洞室	203	下
//    上行4号洞室	104	上
//    下行4号洞室	204	下
//    上行36号洞室	136	上
//    下行36号洞室	236	下
//    轴流风机房		051	中
//    水泵房		013	中
//    总隧道		000	中
    TOTAL("000",0 ,"总隧道"),
    X("001",1 ,"洞内上行"),
    D("002", 2,"洞内下行"),
    XZHPDF("003", 1,"小桩号配电房"),
    DZHPDF("004", 2,"大桩号配电房"),
    XZHXB("005",1 ,"小桩号箱变"),
    DZHXB("006",2 ,"大桩号箱变"),
    SXRK("007", 1,"上行入口"),
    SXCK("008",1 ,"上行出口"),
    XXRK("009",2 ,"下行入口"),
    XXCK("010",2 ,"下行出口"),
    DWSX("011",1 ,"洞外上行"),
    DWXX("012",2 ,"洞外下行"),
//    ZLFJF("051",0, "轴流风机房"),
    SBF("013", 0,"水泵房"),
    ;

    //类型
    private String code;
    //0.中行,1.上行,2.下行
    private Integer direction;
    //描述
    private String desc;

    LocationVO(String code,Integer direction, String desc) {
        this.code = code;
        this.direction = direction;
        this.desc = desc;
    }

    public Integer getDirection() {
        return direction;
    }

    public void setDirection(Integer direction) {
        this.direction = direction;
    }

    public String getCode() {
        return code;
    }
    public String getDesc() {
        return desc;
    }

//    public static String getDescByType(String code) {
//        for (LocationVO ele : values()) {
//            if(ele.getCode().equals(code)) {
//                return ele.getDesc();
//            }
//        }
//        return null;
//    }

    public static Integer getDirectionByCode(String code) {
        for (LocationVO ele : values()) {
            if(ele.getCode().equals(code)) {
                return ele.getDirection();
            }
        }
        return null;
    }

    public static String getTypeByDesc(String desc) {
        for (LocationVO ele : values()) {
            if(ele.getDesc().equals(desc)) {
                return ele.getCode();
            }
        }
        throw new ServiceException("巡检状态无法对应");
    }

}
