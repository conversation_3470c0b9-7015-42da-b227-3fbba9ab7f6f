<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.RoadCheckRDMapper">

    <resultMap type="com.tunnel.domain.RoadCheckRDI" id="RoadCheckRDResult">
        <result property="id" column="id"/>
        <result property="roadId" column="road_id"/>
        <result property="startCode" column="start_code"/>
        <result property="endCode" column="end_code"/>
        <result property="hundredSection" column="hundred_section"/>
        <result property="thousandSection" column="thousand_section"/>
        <result property="direction" column="direction"/>
        <result property="leftRd" column="left_rd"/>
        <result property="rightRd" column="right_rd"/>
        <result property="maxRd" column="max_rd"/>
        <result property="roadType" column="road_type"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="creator" column="creator"/>
        <result property="modifier" column="modifier"/>
        <result property="roadName" column="road_name"/>
    </resultMap>

    <sql id="selectRoadCheckRDVo">
        select id, road_id, start_code, end_code, hundred_section, thousand_section, 
               direction, left_rd, right_rd, max_rd, road_type, create_time, update_time, creator, modifier
        from sc_road_check_rdi
    </sql>

    <select id="selectRoadCheckRDList" parameterType="com.tunnel.domain.RoadCheckRDI" resultMap="RoadCheckRDResult">
        <include refid="selectRoadCheckRDVo"/>
        <where>
            <if test="roadId != null ">and road_id = #{roadId}</if>
            <if test="startCode != null and startCode != ''">and start_code = #{startCode}</if>
            <if test="endCode != null and endCode != ''">and end_code = #{endCode}</if>
            <if test="direction != null">and direction = #{direction}</if>
        </where>
    </select>
    
    <select id="selectRoadCheckRDListByPage" resultMap="RoadCheckRDResult">
        <include refid="selectRoadCheckRDVo"/>
        <where>
            <if test="roadCheckRDI.roadId != null ">and road_id = #{roadCheckRDI.roadId}</if>
            <if test="roadCheckRDI.startCode != null and roadCheckRDI.startCode != ''">and start_code = #{roadCheckRDI.startCode}</if>
            <if test="roadCheckRDI.endCode != null and roadCheckRDI.endCode != ''">and end_code = #{roadCheckRDI.endCode}</if>
            <if test="roadCheckRDI.direction != null">and direction = #{roadCheckRDI.direction}</if>
        </where>
        limit #{offset}, #{limit}
    </select>

    <select id="selectRoadCheckRDById" parameterType="Long" resultMap="RoadCheckRDResult">
        <include refid="selectRoadCheckRDVo"/>
        where id = #{id}
    </select>

    <select id="selectRoadCheckRDByRoadId" parameterType="Long" resultMap="RoadCheckRDResult">
        <include refid="selectRoadCheckRDVo"/>
        where road_id = #{roadId}
    </select>

    <insert id="insertRoadCheckRD" parameterType="com.tunnel.domain.RoadCheckRDI" useGeneratedKeys="true" keyProperty="id">
        insert into sc_road_check_rdi
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="roadId != null">road_id,</if>
            <if test="startCode != null and startCode != ''">start_code,</if>
            <if test="endCode != null and endCode != ''">end_code,</if>
            <if test="hundredSection != null and hundredSection != ''">hundred_section,</if>
            <if test="thousandSection != null and thousandSection != ''">thousand_section,</if>
            <if test="direction != null">direction,</if>
            <if test="leftRd != null">left_rd,</if>
            <if test="rightRd != null">right_rd,</if>
            <if test="maxRd != null">max_rd,</if>
            <if test="roadType != null and roadType != ''">road_type,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="roadId != null">#{roadId},</if>
            <if test="startCode != null and startCode != ''">#{startCode},</if>
            <if test="endCode != null and endCode != ''">#{endCode},</if>
            <if test="hundredSection != null and hundredSection != ''">#{hundredSection},</if>
            <if test="thousandSection != null and thousandSection != ''">#{thousandSection},</if>
            <if test="direction != null">#{direction},</if>
            <if test="leftRd != null">#{leftRd},</if>
            <if test="rightRd != null">#{rightRd},</if>
            <if test="maxRd != null">#{maxRd},</if>
            <if test="roadType != null and roadType != ''">#{roadType},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
        </trim>
    </insert>

    <update id="updateRoadCheckRD" parameterType="com.tunnel.domain.RoadCheckRDI">
        update sc_road_check_rdi
        <trim prefix="SET" suffixOverrides=",">
            <if test="roadId != null">road_id = #{roadId},</if>
            <if test="startCode != null and startCode != ''">start_code = #{startCode},</if>
            <if test="endCode != null and endCode != ''">end_code = #{endCode},</if>
            <if test="hundredSection != null and hundredSection != ''">hundred_section = #{hundredSection},</if>
            <if test="thousandSection != null and thousandSection != ''">thousand_section = #{thousandSection},</if>
            <if test="direction != null">direction = #{direction},</if>
            <if test="leftRd != null">left_rd = #{leftRd},</if>
            <if test="rightRd != null">right_rd = #{rightRd},</if>
            <if test="maxRd != null">max_rd = #{maxRd},</if>
            <if test="roadType != null and roadType != ''">road_type = #{roadType},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRoadCheckRDById" parameterType="Long">
        delete from sc_road_check_rdi where id = #{id}
    </delete>

    <delete id="deleteRoadCheckRDByIds" parameterType="Long">
        delete from sc_road_check_rdi where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <delete id="deleteRoadCheckRDByRoadId" parameterType="Long">
        delete from sc_road_check_rdi where road_id = #{roadId}
    </delete>
    
    <insert id="batchInsert" parameterType="java.util.List">
        insert into sc_road_check_rdi(road_id, start_code, end_code, hundred_section, thousand_section, 
                                  direction, left_rd, right_rd, max_rd, road_type, creator)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.roadId}, #{item.startCode}, #{item.endCode}, #{item.hundredSection}, #{item.thousandSection}, 
             #{item.direction}, #{item.leftRd}, #{item.rightRd}, #{item.maxRd}, #{item.roadType}, #{item.creator})
        </foreach>
    </insert>
    
    <select id="countRoadCheckRD" parameterType="com.tunnel.domain.RoadCheckRDI" resultType="int">
        select count(1) from sc_road_check_rdi
        <where>
            <if test="roadId != null ">and road_id = #{roadId}</if>
            <if test="startCode != null and startCode != ''">and start_code = #{startCode}</if>
            <if test="endCode != null and endCode != ''">and end_code = #{endCode}</if>
            <if test="direction != null">and direction = #{direction}</if>
        </where>
    </select>
</mapper> 