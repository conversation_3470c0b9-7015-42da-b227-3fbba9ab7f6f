<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.CheckUserMapper">
    
    <resultMap type="com.tunnel.domain.CheckUser" id="CheckUserResult">
        <result property="id"            column="id"            />
        <result property="userName"      column="user_name"     />
        <result property="position"      column="position"      />
        <result property="certificateNo" column="certificate_no"/>
        <result property="title"         column="title"         />
        <result property="signature"     column="signature"     />
        <result property="phone"         column="phone"         />
        <result property="email"         column="email"         />
        <result property="sortNum"       column="sort_num"      />
        <result property="type"          column="type"          />
        <result property="creator"       column="creator"       />
        <result property="createTime"    column="create_time"   />
        <result property="updateTime"    column="update_time"   />
        <result property="teamName"      column="team_name"     />
    </resultMap>

    <sql id="selectCheckUserVo">
        select u.id, u.user_name, u.position, u.certificate_no, u.title, u.signature,
               u.phone, u.email, u.sort_num, u.type, u.creator, u.create_time, u.update_time
        from sc_check_user u
    </sql>

    <select id="selectCheckUserList" parameterType="com.tunnel.domain.CheckUser" resultMap="CheckUserResult">
        <include refid="selectCheckUserVo"/>
        <where>
            <if test="userName != null  and userName != ''"> and u.user_name like concat('%', #{userName}, '%')</if>
            <if test="position != null  and position != ''"> and u.position like concat('%', #{position}, '%')</if>
            <if test="phone != null  and phone != ''"> and u.phone like concat('%', #{phone}, '%')</if>
            <if test="type != null"> and u.type = #{type}</if>
        </where>
        order by u.sort_num asc, u.create_time desc
    </select>
    
    <select id="selectCheckUserById" parameterType="Long" resultMap="CheckUserResult">
        <include refid="selectCheckUserVo"/>
        where u.id = #{id}
    </select>
    
    <select id="selectCheckUserListByPosition" parameterType="String" resultMap="CheckUserResult">
        <include refid="selectCheckUserVo"/>
        where u.position = #{position}
        order by u.sort_num asc, u.create_time desc
    </select>
    
    <select id="getMaxSortNum" resultType="Integer">
        select MAX(sort_num) from sc_check_user
    </select>
        
    <insert id="insertCheckUser" parameterType="com.tunnel.domain.CheckUser" useGeneratedKeys="true" keyProperty="id">
        insert into sc_check_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="position != null and position != ''">position,</if>
            <if test="certificateNo != null">certificate_no,</if>
            <if test="title != null">title,</if>
            <if test="signature != null">signature,</if>
            <if test="phone != null">phone,</if>
            <if test="email != null">email,</if>
            <if test="sortNum != null">sort_num,</if>
            <if test="type != null">type,</if>
            <if test="creator != null">creator,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="position != null and position != ''">#{position},</if>
            <if test="certificateNo != null">#{certificateNo},</if>
            <if test="title != null">#{title},</if>
            <if test="signature != null">#{signature},</if>
            <if test="phone != null">#{phone},</if>
            <if test="email != null">#{email},</if>
            <if test="sortNum != null">#{sortNum},</if>
            <if test="type != null">#{type},</if>
            <if test="creator != null">#{creator},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateCheckUser" parameterType="com.tunnel.domain.CheckUser">
        update sc_check_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="position != null and position != ''">position = #{position},</if>
            <if test="certificateNo != null">certificate_no = #{certificateNo},</if>
            <if test="title != null">title = #{title},</if>
            <if test="signature != null">signature = #{signature},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="email != null">email = #{email},</if>
            <if test="sortNum != null">sort_num = #{sortNum},</if>
            <if test="type != null">type = #{type},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCheckUserById" parameterType="Long">
        delete from sc_check_user where id = #{id}
    </delete>

    <delete id="deleteCheckUserByIds" parameterType="String">
        delete from sc_check_user where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteCheckUserByTeamId" parameterType="Long">
        delete from sc_check_user where team_id = #{teamId}
    </delete>

    <update id="batchUpdateSort">
        <foreach collection="list" item="user" separator=";">
            update sc_check_user set sort_num = #{user.sortNum} where id = #{user.id}
        </foreach>
    </update>

</mapper> 