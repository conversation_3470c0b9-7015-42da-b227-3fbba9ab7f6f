<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.RoadEvaluationMapper">

    <resultMap type="com.tunnel.domain.RoadEvaluation" id="RoadEvaluationResult">
        <result property="id" column="id"/>
        <result property="projectId" column="project_id"/>
        <result property="roadCode" column="road_code"/>
        <result property="divisionCode" column="division_code"/>
        <result property="roadName" column="road_name"/>
        <result property="startCode" column="start_code"/>
        <result property="endCode" column="end_code"/>
        <result property="direction" column="direction"/>
        <result property="level" column="level"/>
        <result property="roadType" column="road_type"/>
        <result property="roadLength" column="road_length"/>
        <result property="roadWidth" column="road_width"/>
        <result property="mqi" column="mqi"/>
        <result property="pqi" column="pqi"/>
        <result property="pci" column="pci"/>
        <result property="rqi" column="rqi"/>
        <result property="rdi" column="rdi"/>
        <result property="pwi" column="pwi"/>
        <result property="sri" column="sri"/>
        <result property="pssi" column="pssi"/>
        <result property="sci" column="sci"/>
        <result property="bci" column="bci"/>
        <result property="tci" column="tci"/>
        <result property="detectionMethod" column="detection_method"/>
        <result property="evaluationStandard" column="evaluation_standard"/>
        <result property="evaluationYear" column="evaluation_year"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="creator" column="creator"/>
        <result property="modifier" column="modifier"/>
    </resultMap>

    <sql id="selectRoadEvaluationVo">
        select id, project_id, road_code, division_code, road_name, start_code, end_code, direction, 
               level, road_type, road_length, road_width, mqi, pqi, pci, rqi, rdi, pwi, 
               sri, pssi, sci, bci, tci, detection_method, evaluation_standard, 
               evaluation_year, remark, create_time, update_time, creator, modifier
        from sc_road_evaluation
    </sql>

    <select id="selectRoadEvaluationList" parameterType="com.tunnel.domain.RoadEvaluation" resultMap="RoadEvaluationResult">
        <include refid="selectRoadEvaluationVo"/>
        <where>
            <if test="projectId != null">and project_id = #{projectId}</if>
            <if test="roadCode != null and roadCode != ''">and road_code like concat('%', #{roadCode}, '%')</if>
            <if test="divisionCode != null and divisionCode != ''">and division_code like concat('%', #{divisionCode}, '%')</if>
            <if test="roadName != null and roadName != ''">and road_name like concat('%', #{roadName}, '%')</if>
            <if test="startCode != null and startCode != ''">and start_code like concat('%', #{startCode}, '%')</if>
            <if test="endCode != null and endCode != ''">and end_code like concat('%', #{endCode}, '%')</if>
            <if test="direction != null">and direction = #{direction}</if>
            <if test="level != null and level != ''">and level like concat('%', #{level}, '%')</if>
            <if test="roadType != null and roadType != ''">and road_type like concat('%', #{roadType}, '%')</if>
            <if test="detectionMethod != null and detectionMethod != ''">and detection_method like concat('%', #{detectionMethod}, '%')</if>
            <if test="evaluationStandard != null and evaluationStandard != ''">and evaluation_standard like concat('%', #{evaluationStandard}, '%')</if>
            <if test="evaluationYear != null">and evaluation_year = #{evaluationYear}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="selectRoadEvaluationById" parameterType="Long" resultMap="RoadEvaluationResult">
        <include refid="selectRoadEvaluationVo"/>
        where id = #{id}
    </select>

    <select id="checkRoadCodeUnique" parameterType="com.tunnel.domain.RoadEvaluation" resultMap="RoadEvaluationResult">
        <include refid="selectRoadEvaluationVo"/>
        where road_code = #{roadCode} and start_code = #{startCode} and end_code = #{endCode} and direction = #{direction}
        <if test="id != null">and id != #{id}</if>
        limit 1
    </select>

    <insert id="insertRoadEvaluation" parameterType="com.tunnel.domain.RoadEvaluation" useGeneratedKeys="true" keyProperty="id">
        insert into sc_road_evaluation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectId != null">project_id,</if>
            <if test="roadCode != null and roadCode != ''">road_code,</if>
            <if test="divisionCode != null and divisionCode != ''">division_code,</if>
            <if test="roadName != null">road_name,</if>
            <if test="startCode != null and startCode != ''">start_code,</if>
            <if test="endCode != null and endCode != ''">end_code,</if>
            <if test="direction != null">direction,</if>
            <if test="level != null">level,</if>
            <if test="roadType != null and roadType != ''">road_type,</if>
            <if test="roadLength != null">road_length,</if>
            <if test="roadWidth != null">road_width,</if>
            <if test="mqi != null">mqi,</if>
            <if test="pqi != null">pqi,</if>
            <if test="pci != null">pci,</if>
            <if test="rqi != null">rqi,</if>
            <if test="rdi != null">rdi,</if>
            <if test="pwi != null">pwi,</if>
            <if test="sri != null">sri,</if>
            <if test="pssi != null">pssi,</if>
            <if test="sci != null">sci,</if>
            <if test="bci != null">bci,</if>
            <if test="tci != null">tci,</if>
            <if test="detectionMethod != null">detection_method,</if>
            <if test="evaluationStandard != null">evaluation_standard,</if>
            <if test="evaluationYear != null">evaluation_year,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectId != null">#{projectId},</if>
            <if test="roadCode != null and roadCode != ''">#{roadCode},</if>
            <if test="divisionCode != null and divisionCode != ''">#{divisionCode},</if>
            <if test="roadName != null">#{roadName},</if>
            <if test="startCode != null and startCode != ''">#{startCode},</if>
            <if test="endCode != null and endCode != ''">#{endCode},</if>
            <if test="direction != null">#{direction},</if>
            <if test="level != null">#{level},</if>
            <if test="roadType != null and roadType != ''">#{roadType},</if>
            <if test="roadLength != null">#{roadLength},</if>
            <if test="roadWidth != null">#{roadWidth},</if>
            <if test="mqi != null">#{mqi},</if>
            <if test="pqi != null">#{pqi},</if>
            <if test="pci != null">#{pci},</if>
            <if test="rqi != null">#{rqi},</if>
            <if test="rdi != null">#{rdi},</if>
            <if test="pwi != null">#{pwi},</if>
            <if test="sri != null">#{sri},</if>
            <if test="pssi != null">#{pssi},</if>
            <if test="sci != null">#{sci},</if>
            <if test="bci != null">#{bci},</if>
            <if test="tci != null">#{tci},</if>
            <if test="detectionMethod != null">#{detectionMethod},</if>
            <if test="evaluationStandard != null">#{evaluationStandard},</if>
            <if test="evaluationYear != null">#{evaluationYear},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
        </trim>
    </insert>

    <update id="updateRoadEvaluation" parameterType="com.tunnel.domain.RoadEvaluation">
        update sc_road_evaluation
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="roadCode != null and roadCode != ''">road_code = #{roadCode},</if>
            <if test="divisionCode != null and divisionCode != ''">division_code = #{divisionCode},</if>
            <if test="roadName != null">road_name = #{roadName},</if>
            <if test="startCode != null and startCode != ''">start_code = #{startCode},</if>
            <if test="endCode != null and endCode != ''">end_code = #{endCode},</if>
            <if test="direction != null">direction = #{direction},</if>
            <if test="level != null">level = #{level},</if>
            <if test="roadType != null and roadType != ''">road_type = #{roadType},</if>
            <if test="roadLength != null">road_length = #{roadLength},</if>
            <if test="roadWidth != null">road_width = #{roadWidth},</if>
            <if test="mqi != null">mqi = #{mqi},</if>
            <if test="pqi != null">pqi = #{pqi},</if>
            <if test="pci != null">pci = #{pci},</if>
            <if test="rqi != null">rqi = #{rqi},</if>
            <if test="rdi != null">rdi = #{rdi},</if>
            <if test="pwi != null">pwi = #{pwi},</if>
            <if test="sri != null">sri = #{sri},</if>
            <if test="pssi != null">pssi = #{pssi},</if>
            <if test="sci != null">sci = #{sci},</if>
            <if test="bci != null">bci = #{bci},</if>
            <if test="tci != null">tci = #{tci},</if>
            <if test="detectionMethod != null">detection_method = #{detectionMethod},</if>
            <if test="evaluationStandard != null">evaluation_standard = #{evaluationStandard},</if>
            <if test="evaluationYear != null">evaluation_year = #{evaluationYear},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRoadEvaluationById" parameterType="Long">
        delete from sc_road_evaluation where id = #{id}
    </delete>

    <delete id="deleteRoadEvaluationByIds" parameterType="String">
        delete from sc_road_evaluation where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertRoadEvaluation" parameterType="java.util.List">
        insert into sc_road_evaluation (project_id, road_code, division_code, road_name, start_code, end_code, direction,
                                        level, road_type, road_length, road_width, mqi, pqi, pci, rqi, rdi, pwi,
                                        sri, pssi, sci, bci, tci, detection_method, evaluation_standard,
                                        evaluation_year, remark, create_time, creator)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.projectId}, #{item.roadCode}, #{item.divisionCode}, #{item.roadName}, #{item.startCode}, #{item.endCode}, #{item.direction},
             #{item.level}, #{item.roadType}, #{item.roadLength}, #{item.roadWidth}, #{item.mqi}, #{item.pqi}, #{item.pci}, 
             #{item.rqi}, #{item.rdi}, #{item.pwi}, #{item.sri}, #{item.pssi}, #{item.sci}, #{item.bci}, #{item.tci}, 
             #{item.detectionMethod}, #{item.evaluationStandard}, #{item.evaluationYear}, #{item.remark}, 
             #{item.createTime}, #{item.creator})
        </foreach>
    </insert>

</mapper> 