package com.tunnel.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 道路技术状况评定信息表-国省道对象 sc_road_evaluation
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RoadEvaluation extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 项目ID */
    @NotNull(message = "项目ID不能为空")
    private Long projectId;

    /** 路线编号 */
    @Excel(name = "路线编号")
    @NotBlank(message = "路线编号不能为空")
    @Size(max = 50, message = "路线编号长度不能超过50个字符")
    private String roadCode;

    /** 行政区划代码 */
    @Excel(name = "行政区划代码")
    @NotBlank(message = "行政区划代码不能为空")
    @Size(max = 50, message = "行政区划代码长度不能超过50个字符")
    private String divisionCode;

    /** 路线名称 */
    @Excel(name = "路线名称")
    @Size(max = 255, message = "路线名称长度不能超过255个字符")
    private String roadName;

    /** 起点桩号 */
    @Excel(name = "起点桩号")
    @NotBlank(message = "起点桩号不能为空")
    @Size(max = 50, message = "起点桩号长度不能超过50个字符")
    private String startCode;

    /** 终点桩号 */
    @Excel(name = "终点桩号")
    @NotBlank(message = "终点桩号不能为空")
    @Size(max = 50, message = "终点桩号长度不能超过50个字符")
    private String endCode;

    /** 检测方向：1.上行，2.下行 */
    @Excel(name = "检测方向", readConverterExp = "1=上行,2=下行")
    @NotNull(message = "检测方向不能为空")
    private Integer direction;

    /** 技术等级 */
    @Excel(name = "技术等级")
    @Size(max = 50, message = "技术等级长度不能超过50个字符")
    private String level;

    /** 路面类型 */
    @Excel(name = "路面类型")
    @NotBlank(message = "路面类型不能为空")
    @Size(max = 50, message = "路面类型长度不能超过50个字符")
    private String roadType;

    /** 路段长度(米) */
    @Excel(name = "路段长度(米)")
    private BigDecimal roadLength;

    /** 路段宽度(米) */
    @Excel(name = "路段宽度(米)")
    private BigDecimal roadWidth;

    /** MQI */
    @Excel(name = "MQI")
    private BigDecimal mqi;

    /** 路面PQI */
    @Excel(name = "路面PQI")
    private BigDecimal pqi;

    /** PCI */
    @Excel(name = "PCI")
    private BigDecimal pci;

    /** RQI */
    @Excel(name = "RQI")
    private BigDecimal rqi;

    /** RDI */
    @Excel(name = "RDI")
    private BigDecimal rdi;

    /** PWI */
    @Excel(name = "PWI")
    private BigDecimal pwi;

    /** SRI */
    @Excel(name = "SRI")
    private BigDecimal sri;

    /** PSSI */
    @Excel(name = "PSSI")
    private BigDecimal pssi;

    /** 路基SCI */
    @Excel(name = "路基SCI")
    private BigDecimal sci;

    /** 桥梁构造物BCI */
    @Excel(name = "桥梁构造物BCI")
    private BigDecimal bci;

    /** 沿线设施TCI */
    @Excel(name = "沿线设施TCI")
    private BigDecimal tci;

    /** 检测方式 */
    @Excel(name = "检测方式")
    @Size(max = 100, message = "检测方式长度不能超过100个字符")
    private String detectionMethod;

    /** 评定标准 */
    @Excel(name = "评定标准")
    @Size(max = 100, message = "评定标准长度不能超过100个字符")
    private String evaluationStandard;

    /** 评定年度 */
    @Excel(name = "评定年度")
    private Integer evaluationYear;

    /** 备注 */
    @Excel(name = "备注")
    @Size(max = 255, message = "备注长度不能超过255个字符")
    private String remark;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 创建人 */
    private Long creator;

    /** 更新人 */
    private Long modifier;
} 