package com.tunnel.mapper;

import com.tunnel.domain.CheckTeam;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 检测分组Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
@Mapper
public interface CheckTeamMapper {
    /**
     * 查询检测分组
     *
     * @param id 检测分组主键
     * @return 检测分组
     */
    public CheckTeam selectCheckTeamById(Long id);

    /**
     * 查询检测分组列表
     *
     * @param checkTeam 检测分组
     * @return 检测分组集合
     */
    public List<CheckTeam> selectCheckTeamList(CheckTeam checkTeam);

    /**
     * 新增检测分组
     *
     * @param checkTeam 检测分组
     * @return 结果
     */
    public int insertCheckTeam(CheckTeam checkTeam);

    /**
     * 修改检测分组
     *
     * @param checkTeam 检测分组
     * @return 结果
     */
    public int updateCheckTeam(CheckTeam checkTeam);

    /**
     * 删除检测分组
     *
     * @param id 检测分组主键
     * @return 结果
     */
    public int deleteCheckTeamById(Long id);

    /**
     * 批量删除检测分组
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCheckTeamByIds(Long[] ids);

    /**
     * 校验分组名称是否唯一
     *
     * @param checkTeam 检测分组信息
     * @return 结果
     */
    public CheckTeam checkTeamNameUnique(CheckTeam checkTeam);

}