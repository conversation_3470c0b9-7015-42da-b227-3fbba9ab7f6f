package com.tunnel.service;

import com.tunnel.domain.BatchAddResponse;
import com.tunnel.domain.RoadCheckRQI;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 路面平整度检测信息Service接口
 *
 * <AUTHOR>
 * @date 2024-12-01
 */
public interface RoadCheckRQIService {
    /**
     * 查询路面平整度检测信息
     *
     * @param id 路面平整度检测信息主键
     * @return 路面平整度检测信息
     */
    public RoadCheckRQI selectRoadRQIById(Long id);

    /**
     * 查询路面平整度检测信息列表
     *
     * @param roadCheckRQI 路面平整度检测信息
     * @return 路面平整度检测信息集合
     */
    public List<RoadCheckRQI> selectRoadRQIList(RoadCheckRQI roadCheckRQI);

    /**
     * 新增路面平整度检测信息
     *
     * @param roadCheckRQI 路面平整度检测信息
     * @return 结果
     */
    public int insertRoadRQI(RoadCheckRQI roadCheckRQI);

    /**
     * 修改路面平整度检测信息
     *
     * @param roadCheckRQI 路面平整度检测信息
     * @return 结果
     */
    public int updateRoadRQI(RoadCheckRQI roadCheckRQI);

    /**
     * 批量删除路面平整度检测信息
     *
     * @param ids 需要删除的路面平整度检测信息主键集合
     * @return 结果
     */
    public int deleteRoadRQIByIds(Long[] ids);

    /**
     * 删除路面平整度检测信息信息
     *
     * @param id 路面平整度检测信息主键
     * @return 结果
     */
    public int deleteRoadRQIById(Long id);

    /**
     * 根据道路ID获取路面平整度检测信息
     *
     * @param roadId 道路ID
     * @return 路面平整度检测信息集合
     */
    public List<RoadCheckRQI> selectRoadRQIByRoadId(Long roadId);

    /**
     * 批量导入路面平整度检测数据
     *
     * @param file 导入文件
     * @param roadId 道路ID
     * @return 导入结果
     */
    public BatchAddResponse batchImport(MultipartFile file, Long roadId);
    
    /**
     * 优化导出路面平整度检测信息
     *
     * @param response HTTP响应
     * @param roadCheckRQI 查询条件
     */
    public void exportOptimized(HttpServletResponse response, RoadCheckRQI roadCheckRQI);
    
    /**
     * 获取路面平整度检测信息记录数
     *
     * @param roadCheckRQI 查询条件
     * @return 记录数
     */
    public int countRoadRQI(RoadCheckRQI roadCheckRQI);

    /**
     * 导出RQI数据（分上行下行Sheet）
     *
     * @param response HTTP响应
     * @param roadId 道路ID
     */
    public void exportRQIByDirection(HttpServletResponse response, Long roadId);

    void exportWordRQIByDirection(HttpServletResponse response, Long roadId);

    /**
     * 导出Word版RQI报告（分上行下行）
     *
     * @param response HTTP响应
     * @param roadId 路线ID
     * @param teamId 检测分组ID
     * @param dateTime 检测日期时间
     * @param monthDate 月份日期
     * @param titleName 页眉标题名称
     * @param checkName 检测人员姓名
     * @param reviewName 审核人员姓名
     */
    void exportWordRQIByDirection(HttpServletResponse response, Long roadId, Long teamId, String dateTime, String monthDate, String titleName, String checkName, String reviewName);

    /**
     * 根据道路ID删除路面平整度检测信息
     *
     * @param roadId 道路ID
     * @return 结果
     */
    public int deleteRoadRQIByRoadId(Long roadId);
}