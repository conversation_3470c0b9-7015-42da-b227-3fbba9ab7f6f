package com.tunnel.mapper;

import com.tunnel.domain.RoadCheckSRI;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 路面横向力系数检测信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
public interface RoadCheckSRIMapper {
    /**
     * 查询路面横向力系数检测信息
     *
     * @param id 路面横向力系数检测信息主键
     * @return 路面横向力系数检测信息
     */
    public RoadCheckSRI selectRoadCheckSFCById(Long id);

    /**
     * 查询路面横向力系数检测信息列表
     *
     * @param roadCheckSRI 路面横向力系数检测信息
     * @return 路面横向力系数检测信息集合
     */
    public List<RoadCheckSRI> selectRoadCheckSFCList(RoadCheckSRI roadCheckSRI);

    /**
     * 分页查询路面横向力系数检测信息列表
     *
     * @param roadCheckSRI 路面横向力系数检测信息
     * @param offset 起始位置
     * @param limit 查询条数
     * @return 路面横向力系数检测信息集合
     */
    public List<RoadCheckSRI> selectRoadCheckSFCListByPage(@Param("roadCheckSFC") RoadCheckSRI roadCheckSRI, @Param("offset") int offset, @Param("limit") int limit);

    /**
     * 新增路面横向力系数检测信息
     *
     * @param roadCheckSRI 路面横向力系数检测信息
     * @return 结果
     */
    public int insertRoadCheckSFC(RoadCheckSRI roadCheckSRI);

    /**
     * 修改路面横向力系数检测信息
     *
     * @param roadCheckSRI 路面横向力系数检测信息
     * @return 结果
     */
    public int updateRoadCheckSFC(RoadCheckSRI roadCheckSRI);

    /**
     * 删除路面横向力系数检测信息
     *
     * @param id 路面横向力系数检测信息主键
     * @return 结果
     */
    public int deleteRoadCheckSFCById(Long id);

    /**
     * 批量删除路面横向力系数检测信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRoadCheckSFCByIds(Long[] ids);

    /**
     * 批量新增路面横向力系数检测数据
     *
     * @param list 路面横向力系数检测信息列表
     * @return 结果
     */
    public int batchInsert(List<RoadCheckSRI> list);
    
    /**
     * 根据道路ID获取路面横向力系数检测信息
     *
     * @param roadId 道路ID
     * @return 路面横向力系数检测信息集合
     */
    public List<RoadCheckSRI> selectRoadCheckSFCByRoadId(Long roadId);
    
    /**
     * 获取记录总数
     *
     * @param roadCheckSRI 查询条件
     * @return 总记录数
     */
    public int countRoadCheckSFC(RoadCheckSRI roadCheckSRI);

    /**
     * 根据道路ID删除路面横向力系数检测信息
     *
     * @param roadId 道路ID
     * @return 结果
     */
    public int deleteRoadCheckSFCByRoadId(Long roadId);
} 