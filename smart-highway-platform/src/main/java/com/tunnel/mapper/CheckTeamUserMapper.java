package com.tunnel.mapper;

import com.tunnel.domain.CheckTeamUser;
import com.tunnel.domain.CheckUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 检测分组用户关联Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
@Mapper
public interface CheckTeamUserMapper {
    /**
     * 查询检测分组用户关联
     *
     * @param id 检测分组用户关联主键
     * @return 检测分组用户关联
     */
    public CheckTeamUser selectCheckTeamUserById(Long id);

    /**
     * 查询检测分组用户关联列表
     *
     * @param checkTeamUser 检测分组用户关联
     * @return 检测分组用户关联集合
     */
    public List<CheckTeamUser> selectCheckTeamUserList(CheckTeamUser checkTeamUser);

    /**
     * 新增检测分组用户关联
     *
     * @param checkTeamUser 检测分组用户关联
     * @return 结果
     */
    public int insertCheckTeamUser(CheckTeamUser checkTeamUser);

    /**
     * 修改检测分组用户关联
     *
     * @param checkTeamUser 检测分组用户关联
     * @return 结果
     */
    public int updateCheckTeamUser(CheckTeamUser checkTeamUser);

    /**
     * 删除检测分组用户关联
     *
     * @param id 检测分组用户关联主键
     * @return 结果
     */
    public int deleteCheckTeamUserById(Long id);

    /**
     * 批量删除检测分组用户关联
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCheckTeamUserByIds(Long[] ids);

    /**
     * 根据分组ID查询关联的用户列表
     *
     * @param teamId 分组ID
     * @param type 路线类型
     * @return 用户关联集合
     */
    public List<CheckTeamUser> selectUsersByTeamId(@Param("teamId") Long teamId, @Param("type") Integer type);

    /**
     * 根据用户ID查询关联的分组列表
     *
     * @param userId 用户ID
     * @param type 路线类型
     * @return 分组关联集合
     */
    public List<CheckTeamUser> selectTeamsByUserId(@Param("userId") Long userId, @Param("type") Integer type);

    /**
     * 检查用户分组关联是否存在
     *
     * @param teamId 分组ID
     * @param userId 用户ID
     * @return 关联记录
     */
    public CheckTeamUser checkTeamUserExists(@Param("teamId") Long teamId, @Param("userId") Long userId);

    /**
     * 批量新增检测分组用户关联
     *
     * @param checkTeamUserList 关联列表
     * @return 结果
     */
    public int batchInsertCheckTeamUser(List<CheckTeamUser> checkTeamUserList);

    /**
     * 根据分组ID删除所有关联
     *
     * @param teamId 分组ID
     * @return 结果
     */
    public int deleteCheckTeamUserByTeamId(Long teamId);

    /**
     * 根据用户ID删除所有关联
     *
     * @param userId 用户ID
     * @return 结果
     */
    public int deleteCheckTeamUserByUserId(Long userId);

    /**
     * 批量更新排序
     *
     * @param checkTeamUserList 关联列表
     * @return 结果
     */
    public int batchUpdateSort(List<CheckTeamUser> checkTeamUserList);

    /**
     * 获取分组内下一个排序号
     *
     * @param teamId 分组ID
     * @return 下一个排序号
     */
    public Integer getNextSortNumByTeamId(Long teamId);

    /**
     * 根据分组ID查询关联的CheckUser对象列表
     *
     * @param teamId 分组ID
     * @param type 路线类型
     * @return CheckUser对象列表
     */
    public List<CheckUser> selectCheckUsersByTeamId(@Param("teamId") Long teamId, @Param("type") Integer type);

    /**
     * 根据分组ID和职位查询关联的CheckUser对象列表
     *
     * @param teamId 分组ID
     * @param position 职位
     * @param type 路线类型
     * @return CheckUser对象列表
     */
    public List<CheckUser> selectCheckUsersByTeamIdAndPosition(@Param("teamId") Long teamId, @Param("position") String position, @Param("type") Integer type);
} 