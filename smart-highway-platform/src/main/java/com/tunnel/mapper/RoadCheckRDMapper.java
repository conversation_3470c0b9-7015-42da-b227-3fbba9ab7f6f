package com.tunnel.mapper;

import com.tunnel.domain.RoadCheckRDI;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 路面车辙深度检测信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
public interface RoadCheckRDMapper {
    /**
     * 查询路面车辙深度检测信息
     *
     * @param id 路面车辙深度检测信息主键
     * @return 路面车辙深度检测信息
     */
    public RoadCheckRDI selectRoadCheckRDById(Long id);

    /**
     * 查询路面车辙深度检测信息列表
     *
     * @param RoadCheckRDI 路面车辙深度检测信息
     * @return 路面车辙深度检测信息集合
     */
    public List<RoadCheckRDI> selectRoadCheckRDList(RoadCheckRDI RoadCheckRDI);

    /**
     * 分页查询路面车辙深度检测信息列表
     *
     * @param RoadCheckRDI 路面车辙深度检测信息
     * @param offset 起始位置
     * @param limit 查询条数
     * @return 路面车辙深度检测信息集合
     */
    public List<RoadCheckRDI> selectRoadCheckRDListByPage(@Param("RoadCheckRD") RoadCheckRDI RoadCheckRDI, @Param("offset") int offset, @Param("limit") int limit);

    /**
     * 新增路面车辙深度检测信息
     *
     * @param RoadCheckRDI 路面车辙深度检测信息
     * @return 结果
     */
    public int insertRoadCheckRD(RoadCheckRDI RoadCheckRDI);

    /**
     * 修改路面车辙深度检测信息
     *
     * @param RoadCheckRDI 路面车辙深度检测信息
     * @return 结果
     */
    public int updateRoadCheckRD(RoadCheckRDI RoadCheckRDI);

    /**
     * 删除路面车辙深度检测信息
     *
     * @param id 路面车辙深度检测信息主键
     * @return 结果
     */
    public int deleteRoadCheckRDById(Long id);

    /**
     * 批量删除路面车辙深度检测信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRoadCheckRDByIds(Long[] ids);

    /**
     * 批量新增路面车辙深度检测数据
     *
     * @param list 路面车辙深度检测信息列表
     * @return 结果
     */
    public int batchInsert(List<RoadCheckRDI> list);
    
    /**
     * 根据道路ID获取路面车辙深度检测信息
     *
     * @param roadId 道路ID
     * @return 路面车辙深度检测信息集合
     */
    public List<RoadCheckRDI> selectRoadCheckRDByRoadId(Long roadId);
    
    /**
     * 获取记录总数
     *
     * @param RoadCheckRDI 查询条件
     * @return 总记录数
     */
    public int countRoadCheckRD(RoadCheckRDI RoadCheckRDI);

    /**
     * 根据道路ID删除路面车辙深度检测信息
     *
     * @param roadId 道路ID
     * @return 结果
     */
    public int deleteRoadCheckRDByRoadId(Long roadId);
} 