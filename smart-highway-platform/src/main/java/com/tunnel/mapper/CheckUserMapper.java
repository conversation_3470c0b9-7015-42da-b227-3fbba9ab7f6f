package com.tunnel.mapper;

import com.tunnel.domain.CheckUser;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 检测人员Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
@Mapper
public interface CheckUserMapper {
    /**
     * 查询检测人员
     *
     * @param id 检测人员主键
     * @return 检测人员
     */
    public CheckUser selectCheckUserById(Long id);

    /**
     * 查询检测人员列表
     *
     * @param checkUser 检测人员
     * @return 检测人员集合
     */
    public List<CheckUser> selectCheckUserList(CheckUser checkUser);

    /**
     * 新增检测人员
     *
     * @param checkUser 检测人员
     * @return 结果
     */
    public int insertCheckUser(CheckUser checkUser);

    /**
     * 修改检测人员
     *
     * @param checkUser 检测人员
     * @return 结果
     */
    public int updateCheckUser(CheckUser checkUser);

    /**
     * 删除检测人员
     *
     * @param id 检测人员主键
     * @return 结果
     */
    public int deleteCheckUserById(Long id);

    /**
     * 批量删除检测人员
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCheckUserByIds(Long[] ids);

    /**
     * 根据分组ID查询检测人员列表
     *
     * @param teamId 分组ID
     * @return 检测人员集合
     */
    public List<CheckUser> selectCheckUserListByTeamId(Long teamId);

    /**
     * 根据分组ID删除检测人员
     *
     * @param teamId 分组ID
     * @return 结果
     */
    public int deleteCheckUserByTeamId(Long teamId);

    /**
     * 根据职位查询检测人员列表
     *
     * @param position 职位
     * @return 检测人员集合
     */
    public List<CheckUser> selectCheckUserListByPosition(String position);

    /**
     * 根据分组ID和职位查询检测人员列表
     *
     * @param teamId 分组ID
     * @param position 职位
     * @return 检测人员集合
     */
    public List<CheckUser> selectCheckUserListByTeamIdAndPosition(Long teamId, String position);

    /**
     * 批量插入检测人员
     *
     * @param checkUserList 检测人员列表
     * @return 结果
     */
    public int batchInsertCheckUser(List<CheckUser> checkUserList);

    /**
     * 获取最大排序号
     *
     * @return 最大排序号
     */
    public Integer getMaxSortNum();

    /**
     * 批量更新检测人员排序号
     *
     * @param checkUserList 检测人员列表
     * @return 结果
     */
    public int batchUpdateSort(List<CheckUser> checkUserList);
} 