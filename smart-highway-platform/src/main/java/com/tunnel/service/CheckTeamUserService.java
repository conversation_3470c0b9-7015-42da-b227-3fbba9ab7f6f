package com.tunnel.service;

import com.tunnel.domain.CheckTeamUser;
import com.tunnel.domain.CheckUser;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 检测分组用户关联Service接口
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
public interface CheckTeamUserService {
    /**
     * 查询检测分组用户关联
     *
     * @param id 检测分组用户关联主键
     * @return 检测分组用户关联
     */
    public CheckTeamUser selectCheckTeamUserById(Long id);

    /**
     * 查询检测分组用户关联列表
     *
     * @param checkTeamUser 检测分组用户关联
     * @return 检测分组用户关联集合
     */
    public List<CheckTeamUser> selectCheckTeamUserList(CheckTeamUser checkTeamUser);

    /**
     * 新增检测分组用户关联
     *
     * @param checkTeamUser 检测分组用户关联
     * @return 结果
     */
    public int insertCheckTeamUser(CheckTeamUser checkTeamUser);

    /**
     * 修改检测分组用户关联
     *
     * @param checkTeamUser 检测分组用户关联
     * @return 结果
     */
    public int updateCheckTeamUser(CheckTeamUser checkTeamUser);

    /**
     * 批量删除检测分组用户关联
     *
     * @param ids 需要删除的检测分组用户关联主键集合
     * @return 结果
     */
    public int deleteCheckTeamUserByIds(Long[] ids);

    /**
     * 删除检测分组用户关联信息
     *
     * @param id 检测分组用户关联主键
     * @return 结果
     */
    public int deleteCheckTeamUserById(Long id);

    /**
     * 根据分组ID查询关联的用户列表
     *
     * @param teamId 分组ID
     * @param type 路线类型
     * @return 用户关联集合
     */
    public List<CheckTeamUser> selectUsersByTeamId(Long teamId, Integer type);

    /**
     * 根据分组ID查询关联的用户列表（包含用户详细信息）
     *
     * @param teamId 分组ID
     * @param type 路线类型
     * @return 用户关联集合（包含用户详细信息）
     */
    public List<CheckTeamUser> selectCheckTeamUserListByTeamId(Long teamId, Integer type);

    /**
     * 根据用户ID查询关联的分组列表
     *
     * @param userId 用户ID
     * @param type 路线类型
     * @return 分组关联集合
     */
    public List<CheckTeamUser> selectTeamsByUserId(Long userId, Integer type);

    /**
     * 检查用户分组关联是否存在
     *
     * @param teamId 分组ID
     * @param userId 用户ID
     * @return 是否存在
     */
    public boolean checkTeamUserExists(Long teamId, Long userId);

    /**
     * 批量新增用户分组关联
     *
     * @param teamId 分组ID
     * @param userIds 用户ID列表
     * @return 结果
     */
    public int batchAddUsersToTeam(Long teamId, Long[] userIds);

    /**
     * 批量新增分组用户关联
     *
     * @param userId 用户ID
     * @param teamIds 分组ID列表
     * @return 结果
     */
    public int batchAddUserToTeams(Long userId, Long[] teamIds);

    /**
     * 移除用户分组关联
     *
     * @param teamId 分组ID
     * @param userId 用户ID
     * @return 结果
     */
    public int removeUserFromTeam(Long teamId, Long userId);

    /**
     * 批量更新排序
     *
     * @param checkTeamUserList 关联列表
     * @return 结果
     */
    public int batchUpdateSort(List<CheckTeamUser> checkTeamUserList);

    /**
     * 获取分组内下一个排序号
     *
     * @param teamId 分组ID
     * @return 下一个排序号
     */
    public Integer getNextSortNumByTeamId(Long teamId);

    /**
     * 导出检测分组用户关联数据
     *
     * @param response 响应对象
     * @param checkTeamUser 查询条件
     */
    public void exportCheckTeamUser(HttpServletResponse response, CheckTeamUser checkTeamUser);

    /**
     * 根据分组ID查询关联的CheckUser对象列表
     *
     * @param teamId 分组ID
     * @param type 路线类型
     * @return CheckUser对象列表
     */
    public List<CheckUser> selectCheckUsersByTeamId(Long teamId, Integer type);

    /**
     * 根据分组ID和职位查询关联的CheckUser对象列表
     *
     * @param teamId 分组ID
     * @param position 职位
     * @param type 路线类型
     * @return CheckUser对象列表
     */
    public List<CheckUser> selectCheckUsersByTeamIdAndPosition(Long teamId, String position, Integer type);
} 