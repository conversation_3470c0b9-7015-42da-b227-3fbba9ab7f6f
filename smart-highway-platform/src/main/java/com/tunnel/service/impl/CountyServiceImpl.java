package com.tunnel.service.impl;

import com.tunnel.common.annotation.DataSource;
import com.tunnel.common.enums.DataSourceType;
import com.tunnel.common.exception.ServiceException;
import com.tunnel.common.utils.DateUtils;
import com.tunnel.common.utils.SecurityUtils;
import com.tunnel.common.utils.StringUtils;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.County;
import com.tunnel.mapper.CountyMapper;
import com.tunnel.service.CountyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 农村项目信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Service
@DataSource(value = DataSourceType.SLAVE)
public class CountyServiceImpl implements CountyService {
    private static final Logger log = LoggerFactory.getLogger(CountyServiceImpl.class);

    @Autowired
    private CountyMapper countyMapper;

    /**
     * 查询农村项目信息
     *
     * @param id 农村项目信息主键
     * @return 农村项目信息
     */
    @Override
    public County selectCountyById(Long id) {
        return countyMapper.selectCountyById(id);
    }

    /**
     * 查询农村项目信息列表
     *
     * @param county 农村项目信息
     * @return 农村项目信息
     */
    @Override
    public List<County> selectCountyList(County county) {
        return countyMapper.selectCountyList(county);
    }

    /**
     * 新增农村项目信息
     *
     * @param county 农村项目信息
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.MASTER)
    public int insertCounty(County county) {
        // 校验报告编号唯一性
        if (StringUtils.isNotEmpty(checkReportNoUnique(county))) {
            throw new ServiceException("新增农村项目'" + county.getProjectName() + "'失败，报告编号已存在");
        }
        
        county.setCreateTime(DateUtils.getNowDate());
        county.setCreator(SecurityUtils.getUserId());
        return countyMapper.insertCounty(county);
    }

    /**
     * 修改农村项目信息
     *
     * @param county 农村项目信息
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.MASTER)
    public int updateCounty(County county) {
        // 校验报告编号唯一性
        if (StringUtils.isNotEmpty(checkReportNoUnique(county))) {
            throw new ServiceException("修改农村项目'" + county.getProjectName() + "'失败，报告编号已存在");
        }
        
        county.setUpdateTime(DateUtils.getNowDate());
        county.setModifier(SecurityUtils.getUserId());
        return countyMapper.updateCounty(county);
    }

    /**
     * 批量删除农村项目信息
     *
     * @param ids 需要删除的农村项目信息主键
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.MASTER)
    public int deleteCountyByIds(Long[] ids) {
        return countyMapper.deleteCountyByIds(ids);
    }

    /**
     * 删除农村项目信息
     *
     * @param id 农村项目信息主键
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.MASTER)
    public int deleteCountyById(Long id) {
        return countyMapper.deleteCountyById(id);
    }

    /**
     * 根据报告编号查询农村项目信息
     *
     * @param reportNo 报告编号
     * @return 农村项目信息
     */
    @Override
    public County selectCountyByReportNo(String reportNo) {
        return countyMapper.selectCountyByReportNo(reportNo);
    }

    /**
     * 校验报告编号是否唯一
     *
     * @param county 农村项目信息
     * @return 结果
     */
    @Override
    public String checkReportNoUnique(County county) {
        Long countyId = StringUtils.isNull(county.getId()) ? -1L : county.getId();
        County info = countyMapper.checkReportNoUnique(county);
        if (StringUtils.isNotNull(info) && info.getId().longValue() != countyId.longValue()) {
            return "报告编号已存在";
        }
        return "";
    }

    /**
     * 导出农村项目信息列表
     *
     * @param response HTTP响应
     * @param county 查询条件
     */
    @Override
    public void exportCounty(HttpServletResponse response, County county) {
        List<County> list = countyMapper.selectCountyList(county);
        ExcelUtil<County> util = new ExcelUtil<County>(County.class);
        util.exportExcel(response, list, "农村项目信息数据");
    }

    /**
     * 批量导入农村项目数据
     *
     * @param file 导入文件
     * @return 导入结果
     */
    @Override
    @DataSource(DataSourceType.MASTER)
    public String importCounty(MultipartFile file) {
        ExcelUtil<County> util = new ExcelUtil<County>(County.class);
        try {
            List<County> countyList = util.importExcel(file.getInputStream());
            String operName = SecurityUtils.getUsername();
            String message = this.importCounty(countyList, true, operName);
            return message;
        } catch (Exception e) {
            log.error("导入农村项目数据失败", e);
            throw new ServiceException("导入农村项目数据失败：" + e.getMessage());
        }
    }

    /**
     * 下载导入模板
     *
     * @param response HTTP响应
     */
    @Override
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<County> util = new ExcelUtil<County>(County.class);
        util.importTemplateExcel(response, "农村项目数据");
    }

    /**
     * 导入农村项目数据
     *
     * @param countyList 农村项目数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    public String importCounty(List<County> countyList, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(countyList) || countyList.size() == 0) {
            throw new ServiceException("导入农村项目数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        
        for (County county : countyList) {
            try {
                // 验证是否存在这个农村项目
                County c = countyMapper.selectCountyByReportNo(county.getReportNo());
                if (StringUtils.isNull(c)) {
                    county.setCreateTime(DateUtils.getNowDate());
                    county.setCreator(SecurityUtils.getUserId());
                    this.insertCounty(county);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、农村项目 " + county.getProjectName() + " 导入成功");
                } else if (isUpdateSupport) {
                    county.setId(c.getId());
                    county.setUpdateTime(DateUtils.getNowDate());
                    county.setModifier(SecurityUtils.getUserId());
                    this.updateCounty(county);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、农村项目 " + county.getProjectName() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、农村项目 " + county.getProjectName() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、农村项目 " + county.getProjectName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 获取农村项目信息记录数
     *
     * @param county 查询条件
     * @return 记录数
     */
    @Override
    public int countCounty(County county) {
        return countyMapper.countCounty(county);
    }
} 