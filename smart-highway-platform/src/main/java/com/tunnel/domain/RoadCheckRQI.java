package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 路面平整度检测信息对象 road_rqi
 *
 * <AUTHOR>
 * @date 2024-12-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RoadCheckRQI extends BaseRoadCheckEntity {
    private static final long serialVersionUID = 1L;

    // 基础字段已在BaseRoadCheckEntity中定义

    /** 左车道IRI */
    @Excel(name = "左IRI", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal leftIri;

    /** 右车道IRI */
    @Excel(name = "右IRI", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal rightIri;

    /** 代表IRI */
    @Excel(name = "代表IRI", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal representIri;

    /** 路面质量指数RQI */
    @Excel(name = "RQI", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal rqi;

    /** 备注信息 */
    @Excel(name = "备注", cellType = Excel.ColumnType.STRING)
    private String remark;

    /** 道路名称 - 非数据库字段，用于前端展示 */
    private String roadName;
} 