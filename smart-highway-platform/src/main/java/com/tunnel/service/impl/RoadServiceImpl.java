package com.tunnel.service.impl;

import com.tunnel.common.annotation.DataSource;
import com.tunnel.common.enums.DataSourceType;
import com.tunnel.common.exception.ServiceException;
import com.tunnel.common.utils.DateUtils;
import com.tunnel.common.utils.SecurityUtils;
import com.tunnel.common.utils.StringUtils;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.Road;
import com.tunnel.mapper.RoadMapper;
import com.tunnel.service.RoadService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 基础项目路线信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Service
@DataSource(value = DataSourceType.SLAVE)
public class RoadServiceImpl implements RoadService {
    private static final Logger log = LoggerFactory.getLogger(RoadServiceImpl.class);

    @Autowired
    private RoadMapper roadMapper;

    /**
     * 查询基础项目路线信息
     *
     * @param id 基础项目路线信息主键
     * @return 基础项目路线信息
     */
    @Override
    public Road selectRoadById(Long id) {
        return roadMapper.selectRoadById(id);
    }

    /**
     * 查询基础项目路线信息列表
     *
     * @param road 基础项目路线信息
     * @return 基础项目路线信息
     */
    @Override
    public List<Road> selectRoadList(Road road) {
        return roadMapper.selectRoadList(road);
    }

    /**
     * 新增基础项目路线信息
     *
     * @param road 基础项目路线信息
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.MASTER)
    public int insertRoad(Road road) {
        // 校验路线编号唯一性
        if (StringUtils.isNotEmpty(checkRoadCodeUnique(road))) {
            throw new ServiceException("新增路线'" + road.getRoadName() + "'失败，路线编号已存在");
        }
        
        road.setCreateTime(DateUtils.getNowDate());
        road.setCreator(SecurityUtils.getUserId());
        road.setIsAvailable(1);
        road.setIsDeleted(0);
        road.setVersionNo(0);
        return roadMapper.insertRoad(road);
    }

    /**
     * 修改基础项目路线信息
     *
     * @param road 基础项目路线信息
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.MASTER)
    public int updateRoad(Road road) {
        // 校验路线编号唯一性
        if (StringUtils.isNotEmpty(checkRoadCodeUnique(road))) {
            throw new ServiceException("修改路线'" + road.getRoadName() + "'失败，路线编号已存在");
        }
        
        road.setUpdateTime(DateUtils.getNowDate());
        road.setModifier(SecurityUtils.getUserId());
        return roadMapper.updateRoad(road);
    }

    /**
     * 批量删除基础项目路线信息
     *
     * @param ids 需要删除的基础项目路线信息主键
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.MASTER)
    public int deleteRoadByIds(Long[] ids) {
        return roadMapper.deleteRoadByIds(ids);
    }

    /**
     * 删除基础项目路线信息信息
     *
     * @param id 基础项目路线信息主键
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.MASTER)
    public int deleteRoadById(Long id) {
        return roadMapper.deleteRoadById(id);
    }

    /**
     * 根据路线编号查询路线信息
     *
     * @param roadCode 路线编号
     * @return 路线信息
     */
    @Override
    public Road selectRoadByCode(String roadCode) {
        return roadMapper.selectRoadByCode(roadCode);
    }

    /**
     * 根据用户ID查询路线信息列表
     *
     * @param userId 用户ID
     * @return 路线信息集合
     */
    @Override
    public List<Road> selectRoadListByUserId(Long userId) {
        return roadMapper.selectRoadListByUserId(userId);
    }

    /**
     * 根据部门ID查询路线信息列表
     *
     * @param deptId 部门ID
     * @return 路线信息集合
     */
    @Override
    public List<Road> selectRoadListByDeptId(Long deptId) {
        return roadMapper.selectRoadListByDeptId(deptId);
    }

    /**
     * 校验路线编号是否唯一
     *
     * @param road 路线信息
     * @return 结果
     */
    @Override
    public String checkRoadCodeUnique(Road road) {
        Long roadId = StringUtils.isNull(road.getId()) ? -1L : road.getId();
        Road info = roadMapper.checkRoadCodeUnique(road);
        if (StringUtils.isNotNull(info) && info.getId().longValue() != roadId.longValue()) {
            return "路线编号已存在";
        }
        return "";
    }

    /**
     * 导出基础项目路线信息列表
     *
     * @param response HTTP响应
     * @param road 查询条件
     */
    @Override
    public void exportRoad(HttpServletResponse response, Road road) {
        List<Road> list = roadMapper.selectRoadList(road);
        ExcelUtil<Road> util = new ExcelUtil<Road>(Road.class);
        util.exportExcel(response, list, "基础项目路线信息数据");
    }

    /**
     * 批量导入路线数据
     *
     * @param file 导入文件
     * @return 导入结果
     */
    @Override
    @DataSource(DataSourceType.MASTER)
    public String importRoad(MultipartFile file) {
        ExcelUtil<Road> util = new ExcelUtil<Road>(Road.class);
        try {
            List<Road> roadList = util.importExcel(file.getInputStream());
            String operName = SecurityUtils.getUsername();
            String message = this.importRoad(roadList, true, operName);
            return message;
        } catch (Exception e) {
            log.error("导入路线数据失败", e);
            throw new ServiceException("导入路线数据失败：" + e.getMessage());
        }
    }

    /**
     * 下载导入模板
     *
     * @param response HTTP响应
     */
    @Override
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<Road> util = new ExcelUtil<Road>(Road.class);
        util.importTemplateExcel(response, "路线数据");
    }

    /**
     * 导入路线数据
     *
     * @param roadList 路线数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    public String importRoad(List<Road> roadList, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(roadList) || roadList.size() == 0) {
            throw new ServiceException("导入路线数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        
        for (Road road : roadList) {
            try {
                // 验证是否存在这个路线
                Road r = roadMapper.selectRoadByCode(road.getRoadCode());
                if (StringUtils.isNull(r)) {
                    road.setCreateTime(DateUtils.getNowDate());
                    road.setCreator(SecurityUtils.getUserId());
                    road.setIsAvailable(1);
                    road.setIsDeleted(0);
                    road.setVersionNo(0);
                    this.insertRoad(road);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、路线 " + road.getRoadName() + " 导入成功");
                } else if (isUpdateSupport) {
                    road.setId(r.getId());
                    road.setUpdateTime(DateUtils.getNowDate());
                    road.setModifier(SecurityUtils.getUserId());
                    this.updateRoad(road);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、路线 " + road.getRoadName() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、路线 " + road.getRoadName() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、路线 " + road.getRoadName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 获取路线信息记录数
     *
     * @param road 查询条件
     * @return 记录数
     */
    @Override
    public int countRoad(Road road) {
        return roadMapper.countRoad(road);
    }
} 