<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.RoadCheckSRIMapper">

    <resultMap type="com.tunnel.domain.RoadCheckSRI" id="RoadCheckSFCResult">
        <result property="id" column="id"/>
        <result property="roadId" column="road_id"/>
        <result property="startCode" column="start_code"/>
        <result property="endCode" column="end_code"/>
        <result property="hundredSection" column="hundred_section"/>
        <result property="thousandSection" column="thousand_section"/>
        <result property="testSpeed" column="test_speed"/>
        <result property="testTemp" column="test_temp"/>
        <result property="sfcValue" column="sfc_value"/>
        <result property="sfc50" column="sfc_50"/>
        <result property="sfc50At20" column="sfc_50_20"/>
        <result property="roadType" column="road_type"/>
        <result property="remark" column="remark"/>
        <result property="direction" column="direction"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="creator" column="creator"/>
        <result property="modifier" column="modifier"/>
        <result property="roadName" column="road_name"/>
    </resultMap>

    <sql id="selectRoadCheckSFCVo">
        select id, road_id, start_code, end_code, hundred_section, thousand_section, test_speed, test_temp, sfc_value, sfc_50, sfc_50_20, road_type, remark,
               direction, create_time, update_time, creator, modifier
        from sc_road_check_sri
    </sql>

    <select id="selectRoadCheckSFCList" parameterType="com.tunnel.domain.RoadCheckSRI" resultMap="RoadCheckSFCResult">
        <include refid="selectRoadCheckSFCVo"/>
        <where>
            <if test="roadId != null ">and road_id = #{roadId}</if>
            <if test="startCode != null and startCode != ''">and start_code = #{startCode}</if>
            <if test="endCode != null and endCode != ''">and end_code = #{endCode}</if>
            <if test="direction != null and direction != ''">and direction = #{direction}</if>
        </where>
    </select>
    
    <select id="selectRoadCheckSFCListByPage" resultMap="RoadCheckSFCResult">
        <include refid="selectRoadCheckSFCVo"/>
        <where>
            <if test="roadCheckSRI.roadId != null ">and road_id = #{roadCheckSRI.roadId}</if>
            <if test="roadCheckSRI.startCode != null and roadCheckSRI.startCode != ''">and start_code = #{roadCheckSRI.startCode}</if>
            <if test="roadCheckSRI.endCode != null and roadCheckSRI.endCode != ''">and end_code = #{roadCheckSRI.endCode}</if>
        </where>
        limit #{offset}, #{limit}
    </select>

    <select id="selectRoadCheckSFCById" parameterType="Long" resultMap="RoadCheckSFCResult">
        <include refid="selectRoadCheckSFCVo"/>
        where id = #{id}
    </select>

    <select id="selectRoadCheckSFCByRoadId" parameterType="Long" resultMap="RoadCheckSFCResult">
        <include refid="selectRoadCheckSFCVo"/>
        where road_id = #{roadId}
    </select>

    <insert id="insertRoadCheckSFC" parameterType="com.tunnel.domain.RoadCheckSRI" useGeneratedKeys="true" keyProperty="id">
        insert into sc_road_check_sri
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="roadId != null">road_id,</if>
            <if test="startCode != null and startCode != ''">start_code,</if>
            <if test="endCode != null and endCode != ''">end_code,</if>
            <if test="hundredSection != null and hundredSection != ''">hundred_section,</if>
            <if test="thousandSection != null and thousandSection != ''">thousand_section,</if>
            <if test="testSpeed != null">test_speed,</if>
            <if test="testTemp != null">test_temp,</if>
            <if test="sfcValue != null">sfc_value,</if>
            <if test="sfc50 != null">sfc_50,</if>
            <if test="sfc50At20 != null">sfc_50_20,</if>
            <if test="roadType != null">road_type,</if>
            <if test="remark != null">remark,</if>
            <if test="direction != null">direction,</if>
            <if test="creator != null">creator,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="roadId != null">#{roadId},</if>
            <if test="startCode != null and startCode != ''">#{startCode},</if>
            <if test="endCode != null and endCode != ''">#{endCode},</if>
            <if test="hundredSection != null and hundredSection != ''">#{hundredSection},</if>
            <if test="thousandSection != null and thousandSection != ''">#{thousandSection},</if>
            <if test="testSpeed != null">#{testSpeed},</if>
            <if test="testTemp != null">#{testTemp},</if>
            <if test="sfcValue != null">#{sfcValue},</if>
            <if test="sfc50 != null">#{sfc50},</if>
            <if test="sfc50At20 != null">#{sfc50At20},</if>
            <if test="roadType != null">#{roadType},</if>
            <if test="remark != null">#{remark},</if>
            <if test="direction != null">#{direction},</if>
            <if test="creator != null">#{creator},</if>
        </trim>
    </insert>

    <update id="updateRoadCheckSFC" parameterType="com.tunnel.domain.RoadCheckSRI">
        update sc_road_check_sri
        <trim prefix="SET" suffixOverrides=",">
            <if test="roadId != null">road_id = #{roadId},</if>
            <if test="startCode != null and startCode != ''">start_code = #{startCode},</if>
            <if test="endCode != null and endCode != ''">end_code = #{endCode},</if>
            <if test="hundredSection != null and hundredSection != ''">hundred_section = #{hundredSection},</if>
            <if test="thousandSection != null and thousandSection != ''">thousand_section = #{thousandSection},</if>
            <if test="testSpeed != null">test_speed = #{testSpeed},</if>
            <if test="testTemp != null">test_temp = #{testTemp},</if>
            <if test="sfcValue != null">sfc_value = #{sfcValue},</if>
            <if test="sfc50 != null">sfc_50 = #{sfc50},</if>
            <if test="sfc50At20 != null">sfc_50_20 = #{sfc50At20},</if>
            <if test="roadType != null">road_type = #{roadType},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="direction != null">direction = #{direction},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRoadCheckSFCById" parameterType="Long">
        delete from sc_road_check_sri where id = #{id}
    </delete>

    <delete id="deleteRoadCheckSFCByIds" parameterType="Long">
        delete from sc_road_check_sri where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <delete id="deleteRoadCheckSFCByRoadId" parameterType="Long">
        delete from sc_road_check_sri where road_id = #{roadId}
    </delete>
    
    <insert id="batchInsert" parameterType="java.util.List">
        insert into sc_road_check_sri(road_id, start_code, end_code, hundred_section, thousand_section, test_speed, test_temp, 
                                   sfc_value, sfc_50, sfc_50_20, road_type, remark, direction, creator)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.roadId}, #{item.startCode}, #{item.endCode}, #{item.hundredSection}, #{item.thousandSection}, 
             #{item.testSpeed}, #{item.testTemp}, #{item.sfcValue},
             #{item.sfc50}, #{item.sfc50At20}, #{item.roadType}, #{item.remark}, #{item.direction}, #{item.creator})
        </foreach>
    </insert>
    
    <select id="countRoadCheckSFC" parameterType="com.tunnel.domain.RoadCheckSRI" resultType="int">
        select count(1) from sc_road_check_sri
        <where>
            <if test="roadId != null ">and road_id = #{roadId}</if>
            <if test="startCode != null and startCode != ''">and start_code = #{startCode}</if>
            <if test="endCode != null and endCode != ''">and end_code = #{endCode}</if>
        </where>
    </select>
</mapper> 