package com.tunnel.web.controller;

import com.tunnel.common.annotation.Anonymous;
import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.utils.StakeCodeUtil;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.BatchAddResponse;
import com.tunnel.domain.RoadCheckRQI;
import com.tunnel.service.RoadCheckRQIService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.List;

/**
 * 路面平整度检测信息Controller
 *
 * <AUTHOR>
 * @date 2024-12-01
 */
@Api(tags = "路面平整度检测信息管理")
@RestController
@RequestMapping("/smart/road/check/rqi")
public class RoadCheckRQIController extends BaseController {
    @Autowired
    private RoadCheckRQIService roadCheckRQIService;

    /**
     * 查询路面平整度检测信息列表
     */
    @ApiOperation(value = "获取路面平整度检测信息列表", notes = "获取路面平整度检测信息列表")
//    @PreAuthorize("@ss.hasPermi('road:rqi:list')")
    @GetMapping("/list")
    public TableDataInfo list(RoadCheckRQI roadCheckRQI) {
        startPage();
        List<RoadCheckRQI> list = roadCheckRQIService.selectRoadRQIList(roadCheckRQI);
        return getDataTable(list);
    }

    /**
     * 大数据量导出路面平整度检测信息列表
     */
    @ApiOperation(value = "大数据量导出路面平整度检测信息", notes = "优化的导出方法，适用于几万条数据的导出")
//    @PreAuthorize("@ss.hasPermi('road:rqi:export')")
    @Log(title = "路面平整度检测信息", businessType = BusinessType.EXPORT)
    @PostMapping("/exportLarge")
    public void exportLarge(HttpServletResponse response, RoadCheckRQI roadCheckRQI) {
        roadCheckRQIService.exportOptimized(response, roadCheckRQI);
    }

    /**
     * 获取路面平整度检测信息详细信息
     */
    @ApiOperation(value = "获取路面平整度检测信息详细", notes = "根据ID获取路面平整度检测信息详细信息")
    @ApiImplicitParam(name = "id", value = "路面平整度检测信息ID", required = true, dataType = "Long", paramType = "path")
//    @PreAuthorize("@ss.hasPermi('road:rqi:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(roadCheckRQIService.selectRoadRQIById(id));
    }

    /**
     * 新增路面平整度检测信息
     */
    @ApiOperation(value = "新增路面平整度检测信息", notes = "新增路面平整度检测信息")
//    @PreAuthorize("@ss.hasPermi('road:rqi:add')")
    @Log(title = "路面平整度检测信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RoadCheckRQI roadCheckRQI) {
        return toAjax(roadCheckRQIService.insertRoadRQI(roadCheckRQI));
    }

    /**
     * 修改路面平整度检测信息
     */
    @ApiOperation(value = "修改路面平整度检测信息", notes = "修改路面平整度检测信息")
//    @PreAuthorize("@ss.hasPermi('road:rqi:edit')")
    @Log(title = "路面平整度检测信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RoadCheckRQI roadCheckRQI) {
        return toAjax(roadCheckRQIService.updateRoadRQI(roadCheckRQI));
    }

    /**
     * 删除路面平整度检测信息
     */
    @ApiOperation(value = "删除路面平整度检测信息", notes = "批量删除路面平整度检测信息")
    @ApiImplicitParam(name = "ids", value = "路面平整度检测信息ID数组", required = true, dataType = "Long[]", paramType = "path")
//    @PreAuthorize("@ss.hasPermi('road:rqi:remove')")
    @Log(title = "路面平整度检测信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(roadCheckRQIService.deleteRoadRQIByIds(ids));
    }

    /**
     * 根据道路ID获取路面平整度检测信息
     */
    @ApiOperation(value = "根据道路ID获取路面平整度检测信息", notes = "根据道路ID获取路面平整度检测信息")
    @ApiImplicitParam(name = "roadId", value = "道路ID", required = true, dataType = "Long", paramType = "path")
//    @PreAuthorize("@ss.hasPermi('road:rqi:list')")
    @GetMapping("/roadId/{roadId}")
    public AjaxResult getByRoadId(@PathVariable("roadId") Long roadId) {
        List<RoadCheckRQI> list = roadCheckRQIService.selectRoadRQIByRoadId(roadId);
        return AjaxResult.success(list);
    }

    /**
     * 导入路面平整度检测数据
     */
    @ApiOperation(value = "导入路面平整度检测数据", notes = "从Excel文件导入路面平整度检测数据")
    @Anonymous
    @Log(title = "路面平整度检测信息", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(@RequestPart("file") MultipartFile file, @RequestParam("roadId") Long roadId) throws Exception {
        BatchAddResponse response = roadCheckRQIService.batchImport(file, roadId);
        return AjaxResult.success("导入处理完成", response);
    }

    /**
     * 获取导入模板
     */
    @ApiOperation(value = "获取导入模板", notes = "下载路面平整度检测数据导入模板")
//    @PreAuthorize("@ss.hasPermi('road:rqi:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException {
        ClassPathResource resource = new ClassPathResource("static/rqi-template.xlsx");
        InputStream inputStream = resource.getInputStream();
        String fileName = "路面平整度检测数据导入模板.xlsx";
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
        FileCopyUtils.copy(inputStream, response.getOutputStream());
    }


    @ApiOperation(value = "导出RQI数据", notes = "导出RQI数据，分上行下行两个Sheet")
//    @PreAuthorize("@ss.hasPermi('road:check:export')")
    @PostMapping("/export")
    public void exportRQIByDirection(HttpServletResponse response, @RequestParam("roadId") Long roadId) {
        roadCheckRQIService.exportRQIByDirection(response, roadId);
    }


    @ApiOperation(value = "导出Word报告RQI", notes = "导出RQI-word报告，分上行下行两个Word")
//    @PreAuthorize("@ss.hasPermi('road:check:export')")
    @PostMapping("/exportWordRQI")
    public void exportWordRQI(HttpServletResponse response, 
                             @RequestParam("roadId") Long roadId,
                             @RequestParam(value = "teamId", required = false) Long teamId,
                             @RequestParam(value = "dateTime", required = false) String dateTime,
                             @RequestParam(value = "monthDate", required = false) String monthDate,
                             @RequestParam(value = "titleName", required = false) String titleName,
                             @RequestParam(value = "checkName", required = false) String checkName,
                             @RequestParam(value = "reviewName", required = false) String reviewName) {
        roadCheckRQIService.exportWordRQIByDirection(response, roadId, teamId, dateTime, monthDate, titleName, checkName, reviewName);
    }



} 