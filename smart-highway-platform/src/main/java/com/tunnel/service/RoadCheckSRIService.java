package com.tunnel.service;

import com.tunnel.domain.BatchAddResponse;
import com.tunnel.domain.RoadCheckSRI;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 路面横向力系数检测信息Service接口
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
public interface RoadCheckSRIService {
    /**
     * 查询路面横向力系数检测信息
     *
     * @param id 路面横向力系数检测信息主键
     * @return 路面横向力系数检测信息
     */
    public RoadCheckSRI selectRoadCheckSFCById(Long id);

    /**
     * 查询路面横向力系数检测信息列表
     *
     * @param roadCheckSRI 路面横向力系数检测信息
     * @return 路面横向力系数检测信息集合
     */
    public List<RoadCheckSRI> selectRoadCheckSFCList(RoadCheckSRI roadCheckSRI);

    /**
     * 新增路面横向力系数检测信息
     *
     * @param roadCheckSRI 路面横向力系数检测信息
     * @return 结果
     */
    public int insertRoadCheckSFC(RoadCheckSRI roadCheckSRI);

    /**
     * 修改路面横向力系数检测信息
     *
     * @param roadCheckSRI 路面横向力系数检测信息
     * @return 结果
     */
    public int updateRoadCheckSFC(RoadCheckSRI roadCheckSRI);

    /**
     * 批量删除路面横向力系数检测信息
     *
     * @param ids 需要删除的路面横向力系数检测信息主键集合
     * @return 结果
     */
    public int deleteRoadCheckSFCByIds(Long[] ids);

    /**
     * 删除路面横向力系数检测信息信息
     *
     * @param id 路面横向力系数检测信息主键
     * @return 结果
     */
    public int deleteRoadCheckSFCById(Long id);

    /**
     * 根据道路ID获取路面横向力系数检测信息
     *
     * @param roadId 道路ID
     * @return 路面横向力系数检测信息集合
     */
    public List<RoadCheckSRI> selectRoadCheckSFCByRoadId(Long roadId);

    /**
     * 批量导入路面横向力系数检测数据
     *
     * @param file 导入文件
     * @param roadId 道路ID
     * @return 导入结果
     */
    public BatchAddResponse batchImport(MultipartFile file, Long roadId);
    
    /**
     * 优化导出路面横向力系数检测信息
     *
     * @param response HTTP响应
     * @param roadCheckSRI 查询条件
     */
    public void exportOptimized(HttpServletResponse response, RoadCheckSRI roadCheckSRI);
    
    /**
     * 获取路面横向力系数检测信息记录数
     *
     * @param roadCheckSRI 查询条件
     * @return 记录数
     */
    public int countRoadCheckSFC(RoadCheckSRI roadCheckSRI);

    void exportSRIByDirection(HttpServletResponse response, Long roadId);

    void exportWordSRIByDirection(HttpServletResponse response, Long roadId);

    /**
     * 导出Word版SRI报告（分上行下行）
     *
     * @param response HTTP响应
     * @param roadId 路线ID
     * @param teamId 检测分组ID
     * @param dateTime 检测日期时间
     * @param monthDate 月份日期
     * @param titleName 页眉标题名称
     * @param checkName 检测人员姓名
     * @param reviewName 审核人员姓名
     */
    void exportWordSRIByDirection(HttpServletResponse response, Long roadId, Long teamId, String dateTime, String monthDate, String titleName, String checkName, String reviewName);

    /**
     * 根据道路ID删除路面横向力系数检测信息
     *
     * @param roadId 道路ID
     * @return 结果
     */
    public int deleteRoadCheckSFCByRoadId(Long roadId);

    void replaceSRITextInDocument(XWPFDocument document, String placeholder, String value);
}