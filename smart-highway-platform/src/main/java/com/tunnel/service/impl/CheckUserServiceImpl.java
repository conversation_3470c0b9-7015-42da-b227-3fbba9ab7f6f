package com.tunnel.service.impl;

import com.tunnel.common.core.domain.entity.SysUser;
import com.tunnel.common.exception.ServiceException;
import com.tunnel.common.utils.DateUtils;
import com.tunnel.common.utils.SecurityUtils;
import com.tunnel.common.utils.StringUtils;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.CheckUser;
import com.tunnel.mapper.CheckUserMapper;
import com.tunnel.service.CheckUserService;
import com.tunnel.service.CheckTeamUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 检测人员Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
@Service
public class CheckUserServiceImpl implements CheckUserService {
    private static final Logger log = LoggerFactory.getLogger(CheckUserServiceImpl.class);

    @Autowired
    private CheckUserMapper checkUserMapper;

    @Autowired
    private CheckTeamUserService checkTeamUserService;

    /**
     * 查询检测人员
     *
     * @param id 检测人员主键
     * @return 检测人员
     */
    @Override
    public CheckUser selectCheckUserById(Long id) {
        return checkUserMapper.selectCheckUserById(id);
    }

    /**
     * 查询检测人员列表
     *
     * @param checkUser 检测人员
     * @return 检测人员
     */
    @Override
    public List<CheckUser> selectCheckUserList(CheckUser checkUser) {
        return checkUserMapper.selectCheckUserList(checkUser);
    }

    /**
     * 新增检测人员
     *
     * @param checkUser 检测人员
     * @return 结果
     */
    @Override
    public int insertCheckUser(CheckUser checkUser) {
        checkUser.setCreateBy(SecurityUtils.getUsername());
        checkUser.setCreateTime(DateUtils.getNowDate());
        // 如果sortNum为空，设置默认值0
        if (checkUser.getSortNum() == null) {
            checkUser.setSortNum(0);
        }
        return checkUserMapper.insertCheckUser(checkUser);
    }

    /**
     * 修改检测人员
     *
     * @param checkUser 检测人员
     * @return 结果
     */
    @Override
    public int updateCheckUser(CheckUser checkUser) {
        checkUser.setUpdateBy(SecurityUtils.getUsername());
        checkUser.setUpdateTime(DateUtils.getNowDate());
        return checkUserMapper.updateCheckUser(checkUser);
    }

    /**
     * 批量删除检测人员
     *
     * @param ids 需要删除的检测人员主键
     * @return 结果
     */
    @Override
    public int deleteCheckUserByIds(Long[] ids) {
        return checkUserMapper.deleteCheckUserByIds(ids);
    }

    /**
     * 删除检测人员信息
     *
     * @param id 检测人员主键
     * @return 结果
     */
    @Override
    public int deleteCheckUserById(Long id) {
        return checkUserMapper.deleteCheckUserById(id);
    }

    /**
     * 根据分组ID查询检测人员列表
     *
     * @param teamId 分组ID
     * @return 检测人员集合
     */
    @Override
    public List<CheckUser> selectCheckUserListByTeamId(Long teamId) {
        return checkTeamUserService.selectCheckUsersByTeamId(teamId, null);
    }

    /**
     * 根据职位查询检测人员列表
     *
     * @param position 职位
     * @return 检测人员集合
     */
    @Override
    public List<CheckUser> selectCheckUserListByPosition(String position) {
        return checkUserMapper.selectCheckUserListByPosition(position);
    }

    /**
     * 根据分组ID和职位查询检测人员列表
     *
     * @param teamId 分组ID
     * @param position 职位
     * @return 检测人员集合
     */
    @Override
    public List<CheckUser> selectCheckUserListByTeamIdAndPosition(Long teamId, String position) {
        return checkTeamUserService.selectCheckUsersByTeamIdAndPosition(teamId, position, null);
    }

    /**
     * 导出检测人员数据
     *
     * @param response 响应对象
     * @param checkUser 查询条件
     */
    @Override
    public void exportCheckUser(HttpServletResponse response, CheckUser checkUser) {
        List<CheckUser> list = checkUserMapper.selectCheckUserList(checkUser);
        ExcelUtil<CheckUser> util = new ExcelUtil<CheckUser>(CheckUser.class);
        util.exportExcel(response, list, "检测人员数据");
    }

    /**
     * 批量导入检测人员数据
     *
     * @param file Excel文件
     * @return 导入结果信息
     */
    @Override
    public String importCheckUser(MultipartFile file) {
        try {
            ExcelUtil<CheckUser> util = new ExcelUtil<CheckUser>(CheckUser.class);
            List<CheckUser> checkUserList = util.importExcel(file.getInputStream());
            String operName = SecurityUtils.getUsername();
            
            if (StringUtils.isNull(checkUserList) || checkUserList.size() == 0) {
                throw new ServiceException("导入检测人员数据不能为空！");
            }
            
            int successNum = 0;
            int failureNum = 0;
            StringBuilder successMsg = new StringBuilder();
            StringBuilder failureMsg = new StringBuilder();
            
            for (CheckUser checkUser : checkUserList) {
                try {
                    // 验证必要字段
                    if (StringUtils.isEmpty(checkUser.getUserName())) {
                        failureNum++;
                        failureMsg.append("<br/>").append(failureNum).append("、姓名不能为空");
                        continue;
                    }
                    if (StringUtils.isEmpty(checkUser.getPosition())) {
                        failureNum++;
                        failureMsg.append("<br/>").append(failureNum).append("、职位不能为空");
                        continue;
                    }

                    checkUser.setCreateBy(operName);
                    checkUser.setCreateTime(DateUtils.getNowDate());
                    // 如果sortNum为空，设置默认值0
                    if (checkUser.getSortNum() == null) {
                        checkUser.setSortNum(0);
                    }
                    
                    this.insertCheckUser(checkUser);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、").append(checkUser.getUserName()).append(" 导入成功");
                } catch (Exception e) {
                    failureNum++;
                    String msg = "<br/>" + failureNum + "、" + checkUser.getUserName() + " 导入失败：";
                    failureMsg.append(msg).append(e.getMessage());
                    log.error(msg, e);
                }
            }
            
            if (failureNum > 0) {
                failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
                throw new ServiceException(failureMsg.toString());
            } else {
                successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
            }
            return successMsg.toString();
        } catch (Exception e) {
            throw new ServiceException("导入检测人员数据失败：" + e.getMessage());
        }
    }

    /**
     * 下载导入模板
     *
     * @param response 响应对象
     */
    @Override
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<CheckUser> util = new ExcelUtil<CheckUser>(CheckUser.class);
        util.importTemplateExcel(response, "检测人员数据");
    }

    /**
     * 获取下一个排序号
     *
     * @return 下一个排序号
     */
    @Override
    public Integer getNextSortNum() {
        Integer maxSortNum = checkUserMapper.getMaxSortNum();
        return maxSortNum == null ? 1 : maxSortNum + 1;
    }

    /**
     * 批量更新人员排序
     *
     * @param checkUserList 检测人员列表
     * @return 结果
     */
    @Override
    public int batchUpdateSort(List<CheckUser> checkUserList) {
        if (checkUserList == null || checkUserList.isEmpty()) {
            return 0;
        }
        return checkUserMapper.batchUpdateSort(checkUserList);
    }
} 