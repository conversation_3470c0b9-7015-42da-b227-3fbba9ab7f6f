<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.CountyMapper">

    <resultMap type="com.tunnel.domain.County" id="CountyResult">
        <result property="id" column="id"/>
        <result property="year" column="year"/>
        <result property="projectName" column="project_name"/>
        <result property="companyName" column="company_name"/>
        <result property="rank" column="rank"/>
        <result property="checkArea" column="check_area"/>
        <result property="startDate" column="start_date"/>
        <result property="endDate" column="end_date"/>
        <result property="reportNo" column="report_no"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="creator" column="creator"/>
        <result property="modifier" column="modifier"/>
    </resultMap>

    <sql id="selectCountyVo">
        select id, year, project_name, company_name, rank, check_area, start_date, end_date, report_no, remark,
               create_time, update_time, creator, modifier
        from sc_county_base
    </sql>

    <select id="selectCountyList" parameterType="com.tunnel.domain.County" resultMap="CountyResult">
        <include refid="selectCountyVo"/>
        <where>
            1=1
            <if test="year != null">and year = #{year}</if>
            <if test="projectName != null and projectName != ''">and project_name like concat('%', #{projectName}, '%')</if>
            <if test="companyName != null and companyName != ''">and company_name like concat('%', #{companyName}, '%')</if>
            <if test="rank != null and rank != ''">and rank like concat('%', #{rank}, '%')</if>
            <if test="checkArea != null and checkArea != ''">and check_area like concat('%', #{checkArea}, '%')</if>
            <if test="startDate != null and startDate != ''">and start_date = #{startDate}</if>
            <if test="endDate != null and endDate != ''">and end_date = #{endDate}</if>
            <if test="reportNo != null and reportNo != ''">and report_no like concat('%', #{reportNo}, '%')</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectCountyById" parameterType="Long" resultMap="CountyResult">
        <include refid="selectCountyVo"/>
        where id = #{id}
    </select>

    <select id="selectCountyByReportNo" parameterType="String" resultMap="CountyResult">
        <include refid="selectCountyVo"/>
        where report_no = #{reportNo}
    </select>

    <select id="checkReportNoUnique" parameterType="com.tunnel.domain.County" resultMap="CountyResult">
        <include refid="selectCountyVo"/>
        where report_no = #{reportNo}
        <if test="id != null">and id != #{id}</if>
        limit 1
    </select>

    <insert id="insertCounty" parameterType="com.tunnel.domain.County" useGeneratedKeys="true" keyProperty="id">
        insert into sc_county_base
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="year != null">year,</if>
            <if test="projectName != null and projectName != ''">project_name,</if>
            <if test="companyName != null and companyName != ''">company_name,</if>
            <if test="rank != null and rank != ''">rank,</if>
            <if test="checkArea != null and checkArea != ''">check_area,</if>
            <if test="startDate != null and startDate != ''">start_date,</if>
            <if test="endDate != null and endDate != ''">end_date,</if>
            <if test="reportNo != null and reportNo != ''">report_no,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="creator != null">creator,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="year != null">#{year},</if>
            <if test="projectName != null and projectName != ''">#{projectName},</if>
            <if test="companyName != null and companyName != ''">#{companyName},</if>
            <if test="rank != null and rank != ''">#{rank},</if>
            <if test="checkArea != null and checkArea != ''">#{checkArea},</if>
            <if test="startDate != null and startDate != ''">#{startDate},</if>
            <if test="endDate != null and endDate != ''">#{endDate},</if>
            <if test="reportNo != null and reportNo != ''">#{reportNo},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="creator != null">#{creator},</if>
        </trim>
    </insert>

    <update id="updateCounty" parameterType="com.tunnel.domain.County">
        update sc_county_base
        <trim prefix="SET" suffixOverrides=",">
            <if test="year != null">year = #{year},</if>
            <if test="projectName != null and projectName != ''">project_name = #{projectName},</if>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="rank != null and rank != ''">rank = #{rank},</if>
            <if test="checkArea != null">check_area = #{checkArea},</if>
            <if test="startDate != null and startDate != ''">start_date = #{startDate},</if>
            <if test="endDate != null and endDate != ''">end_date = #{endDate},</if>
            <if test="reportNo != null and reportNo != ''">report_no = #{reportNo},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCountyById" parameterType="Long">
        delete from sc_county_base where id = #{id}
    </delete>

    <delete id="deleteCountyByIds" parameterType="Long">
        delete from sc_county_base where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="countCounty" parameterType="com.tunnel.domain.County" resultType="int">
        select count(*) from sc_county_base
        <where>
            1=1
            <if test="year != null">and year = #{year}</if>
            <if test="projectName != null and projectName != ''">and project_name like concat('%', #{projectName}, '%')</if>
            <if test="companyName != null and companyName != ''">and company_name like concat('%', #{companyName}, '%')</if>
            <if test="rank != null and rank != ''">and rank like concat('%', #{rank}, '%')</if>
            <if test="checkArea != null and checkArea != ''">and check_area like concat('%', #{checkArea}, '%')</if>
            <if test="startDate != null and startDate != ''">and start_date = #{startDate}</if>
            <if test="endDate != null and endDate != ''">and end_date = #{endDate}</if>
            <if test="reportNo != null and reportNo != ''">and report_no like concat('%', #{reportNo}, '%')</if>
        </where>
    </select>
</mapper> 