<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.RoadCheckPCIMapper">

    <resultMap type="com.tunnel.domain.RoadCheckPCI" id="RoadCheckRecordResult">
        <result property="id" column="id"/>
        <result property="roadId" column="road_id"/>
        <result property="startCode" column="start_code"/>
        <result property="endCode" column="end_code"/>
        <result property="hundredSection" column="hundred_section"/>
        <result property="thousandSection" column="thousand_section"/>
        <result property="crackArea" column="crack_area"/>
        <result property="blockCrackArea" column="block_crack_area"/>
        <result property="longitudinalCrackArea" column="longitudinal_crack_area"/>
        <result property="transverseCrackArea" column="transverse_crack_area"/>
        <result property="sinkArea" column="sink_area"/>
        <result property="rutArea" column="rut_area"/>
        <result property="waveBumpArea" column="wave_bump_area"/>
        <result property="pitArea" column="pit_area"/>
        <result property="looseArea" column="loose_area"/>
        <result property="bleedingArea" column="bleeding_area"/>
        <result property="patchAreaPart" column="patch_area_part"/>
        <result property="patchAreaStrip" column="patch_area_strip"/>
        <result property="damageRate" column="damage_rate"/>
        <result property="pci" column="pci"/>
        <result property="roadType" column="road_type"/>
        <result property="remark" column="remark"/>
        <result property="direction" column="direction"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="creator" column="creator"/>
        <result property="modifier" column="modifier"/>
        <result property="roadName" column="road_name"/>
    </resultMap>

    <sql id="selectRoadCheckRecordVo">
        select id, road_id, start_code, end_code, hundred_section, thousand_section, crack_area, block_crack_area, longitudinal_crack_area, 
               transverse_crack_area, sink_area, rut_area, wave_bump_area, pit_area, loose_area, 
               bleeding_area, patch_area_part, patch_area_strip, damage_rate, pci, road_type, remark, direction, create_time, update_time, creator, modifier
        from sc_road_check_pci
    </sql>

    <select id="selectRoadCheckRecordList" parameterType="com.tunnel.domain.RoadCheckPCI" resultMap="RoadCheckRecordResult">
        select p.id, p.road_id, p.start_code, p.end_code, p.hundred_section, p.thousand_section, p.crack_area, p.block_crack_area, p.longitudinal_crack_area, 
               p.transverse_crack_area, p.sink_area, p.rut_area, p.wave_bump_area, p.pit_area, p.loose_area, 
               p.bleeding_area, p.patch_area_part, p.patch_area_strip, p.damage_rate, p.pci, p.road_type, p.remark, p.direction, p.create_time, p.update_time, p.creator, p.modifier,
               r.road_name
        from sc_road_check_pci p
        left join sc_road r on p.road_id = r.id
        <where>
            <if test="roadId != null ">and p.road_id = #{roadId}</if>
            <if test="startCode != null and startCode != ''">and p.start_code = #{startCode}</if>
            <if test="endCode != null and endCode != ''">and p.end_code = #{endCode}</if>
            and p.direction = #{direction}
        </where>
        order by p.start_code,p.end_code
    </select>

    <select id="selectRoadCheckRecordById" parameterType="Long" resultMap="RoadCheckRecordResult">
        <include refid="selectRoadCheckRecordVo"/>
        where id = #{id}
    </select>

    <select id="selectRoadCheckRecordByRoadId" parameterType="Long" resultMap="RoadCheckRecordResult">
        select p.id, p.road_id, p.start_code, p.end_code, p.hundred_section, p.thousand_section, p.crack_area, p.block_crack_area, p.longitudinal_crack_area, 
               p.transverse_crack_area, p.sink_area, p.rut_area, p.wave_bump_area, p.pit_area, p.loose_area, 
               p.bleeding_area, p.patch_area_part, p.patch_area_strip, p.damage_rate, p.pci, p.road_type, p.remark, p.direction, p.create_time, p.update_time, p.creator, p.modifier,
               r.road_name
        from sc_road_check_pci p
        left join sc_road r on p.road_id = r.id
        where p.road_id = #{roadId}
    </select>

    <insert id="insertRoadCheckRecord" parameterType="com.tunnel.domain.RoadCheckPCI" useGeneratedKeys="true" keyProperty="id">
        insert into sc_road_check_pci
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="roadId != null">road_id,</if>
            <if test="startCode != null and startCode != ''">start_code,</if>
            <if test="endCode != null and endCode != ''">end_code,</if>
            <if test="hundredSection != null and hundredSection != ''">hundred_section,</if>
            <if test="thousandSection != null and thousandSection != ''">thousand_section,</if>
            <if test="crackArea != null">crack_area,</if>
            <if test="blockCrackArea != null">block_crack_area,</if>
            <if test="longitudinalCrackArea != null">longitudinal_crack_area,</if>
            <if test="transverseCrackArea != null">transverse_crack_area,</if>
            <if test="sinkArea != null">sink_area,</if>
            <if test="rutArea != null">rut_area,</if>
            <if test="waveBumpArea != null">wave_bump_area,</if>
            <if test="pitArea != null">pit_area,</if>
            <if test="looseArea != null">loose_area,</if>
            <if test="bleedingArea != null">bleeding_area,</if>
            <if test="patchAreaPart != null">patch_area_part,</if>
            <if test="patchAreaStrip != null">patch_area_strip,</if>
            <if test="damageRate != null">damage_rate,</if>
            <if test="pci != null">pci,</if>
            <if test="roadType != null and roadType != ''">road_type,</if>
            <if test="remark != null">remark,</if>
            <if test="direction != null">direction,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="roadId != null">#{roadId},</if>
            <if test="startCode != null and startCode != ''">#{startCode},</if>
            <if test="endCode != null and endCode != ''">#{endCode},</if>
            <if test="hundredSection != null and hundredSection != ''">#{hundredSection},</if>
            <if test="thousandSection != null and thousandSection != ''">#{thousandSection},</if>
            <if test="crackArea != null">#{crackArea},</if>
            <if test="blockCrackArea != null">#{blockCrackArea},</if>
            <if test="longitudinalCrackArea != null">#{longitudinalCrackArea},</if>
            <if test="transverseCrackArea != null">#{transverseCrackArea},</if>
            <if test="sinkArea != null">#{sinkArea},</if>
            <if test="rutArea != null">#{rutArea},</if>
            <if test="waveBumpArea != null">#{waveBumpArea},</if>
            <if test="pitArea != null">#{pitArea},</if>
            <if test="looseArea != null">#{looseArea},</if>
            <if test="bleedingArea != null">#{bleedingArea},</if>
            <if test="patchAreaPart != null">#{patchAreaPart},</if>
            <if test="patchAreaStrip != null">#{patchAreaStrip},</if>
            <if test="damageRate != null">#{damageRate},</if>
            <if test="pci != null">#{pci},</if>
            <if test="roadType != null and roadType != ''">#{roadType},</if>
            <if test="remark != null">#{remark},</if>
            <if test="direction != null">#{direction},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
        </trim>
    </insert>

    <update id="updateRoadCheckRecord" parameterType="com.tunnel.domain.RoadCheckPCI">
        update sc_road_check_pci
        <trim prefix="SET" suffixOverrides=",">
            <if test="roadId != null">road_id = #{roadId},</if>
            <if test="startCode != null and startCode != ''">start_code = #{startCode},</if>
            <if test="endCode != null and endCode != ''">end_code = #{endCode},</if>
            <if test="hundredSection != null and hundredSection != ''">hundred_section = #{hundredSection},</if>
            <if test="thousandSection != null and thousandSection != ''">thousand_section = #{thousandSection},</if>
            <if test="crackArea != null">crack_area = #{crackArea},</if>
            <if test="blockCrackArea != null">block_crack_area = #{blockCrackArea},</if>
            <if test="longitudinalCrackArea != null">longitudinal_crack_area = #{longitudinalCrackArea},</if>
            <if test="transverseCrackArea != null">transverse_crack_area = #{transverseCrackArea},</if>
            <if test="sinkArea != null">sink_area = #{sinkArea},</if>
            <if test="rutArea != null">rut_area = #{rutArea},</if>
            <if test="waveBumpArea != null">wave_bump_area = #{waveBumpArea},</if>
            <if test="pitArea != null">pit_area = #{pitArea},</if>
            <if test="looseArea != null">loose_area = #{looseArea},</if>
            <if test="bleedingArea != null">bleeding_area = #{bleedingArea},</if>
            <if test="patchAreaPart != null">patch_area_part = #{patchAreaPart},</if>
            <if test="patchAreaStrip != null">patch_area_strip = #{patchAreaStrip},</if>
            <if test="damageRate != null">damage_rate = #{damageRate},</if>
            <if test="pci != null">pci = #{pci},</if>
            <if test="roadType != null and roadType != ''">road_type = #{roadType},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="direction != null">direction = #{direction},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRoadCheckRecordById" parameterType="Long">
        delete from sc_road_check_pci where id = #{id}
    </delete>

    <delete id="deleteRoadCheckRecordByIds" parameterType="String">
        delete from sc_road_check_pci where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteRoadCheckRecordByRoadId" parameterType="Long">
        delete from sc_road_check_pci where road_id = #{roadId}
    </delete>
    
    <insert id="batchInsert" parameterType="java.util.List">
        insert into sc_road_check_pci(road_id, start_code, end_code, hundred_section, thousand_section, 
                                   crack_area, block_crack_area, longitudinal_crack_area, transverse_crack_area, 
                                   sink_area, rut_area, wave_bump_area, pit_area, loose_area, bleeding_area, 
                                   patch_area_part, patch_area_strip, damage_rate, pci, road_type, remark, direction,
                                    creator)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.roadId}, #{item.startCode}, #{item.endCode}, #{item.hundredSection}, #{item.thousandSection}, 
             #{item.crackArea}, #{item.blockCrackArea}, #{item.longitudinalCrackArea}, #{item.transverseCrackArea}, 
             #{item.sinkArea}, #{item.rutArea}, #{item.waveBumpArea}, #{item.pitArea}, #{item.looseArea}, #{item.bleedingArea}, 
             #{item.patchAreaPart}, #{item.patchAreaStrip}, #{item.damageRate}, #{item.pci}, #{item.roadType}, #{item.remark}, #{item.direction},
             #{item.creator})
        </foreach>
    </insert>
    
    <!-- 添加分页查询方法，用于大数据量导出 -->
    <select id="selectRoadCheckRecordListByPage" resultMap="RoadCheckRecordResult">
        <include refid="selectRoadCheckRecordVo"/>
        <where>
            <if test="query.roadId != null ">and road_id = #{query.roadId}</if>
            <if test="query.startCode != null and query.startCode != ''">and start_code = #{query.startCode}</if>
            <if test="query.endCode != null and query.endCode != ''">and end_code = #{query.endCode}</if>
        </where>
        limit #{offset}, #{pageSize}
    </select>
    
    <!-- 获取符合条件的总记录数 -->
    <select id="countRoadCheckRecord" parameterType="com.tunnel.domain.RoadCheckPCI" resultType="int">
        select count(1) from sc_road_check_pci
        <where>
            <if test="roadId != null ">and road_id = #{roadId}</if>
            <if test="startCode != null and startCode != ''">and start_code = #{startCode}</if>
            <if test="endCode != null and endCode != ''">and end_code = #{endCode}</if>
        </where>
    </select>
</mapper> 