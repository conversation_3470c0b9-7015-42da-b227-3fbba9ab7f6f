package com.tunnel.service.impl;

import com.tunnel.common.utils.CollectUtil;
import com.tunnel.common.utils.DateUtils;
import com.tunnel.common.utils.FileUtil;
import com.tunnel.common.utils.SecurityUtils;
import com.tunnel.common.utils.StakeCodeUtil;
import com.tunnel.common.utils.WordDocumentUtils;
import com.tunnel.domain.BatchAddResponse;
import com.tunnel.domain.RoadCheckRQI;
import com.tunnel.domain.Road;
import com.tunnel.common.core.domain.entity.SysUser;
import com.tunnel.mapper.RoadCheckRQIMapper;
import com.tunnel.mapper.RoadMapper;
import com.tunnel.service.RoadCheckRQIService;
import com.tunnel.service.CheckTeamService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.HashMap;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import java.util.stream.Collectors;
import java.util.Optional;

import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.apache.poi.xwpf.usermodel.XWPFHeader;
import org.apache.poi.xwpf.usermodel.XWPFFooter;
import org.apache.poi.xwpf.usermodel.ParagraphAlignment;

/**
 * 路面平整度检测信息Service实现
 *
 * <AUTHOR>
 * @date 2024-12-01
 */
@Service
@Slf4j
public class RoadCheckRQIServiceImpl implements RoadCheckRQIService {
    @Autowired
    private RoadCheckRQIMapper roadCheckRQIMapper;

    @Autowired
    private RoadMapper roadMapper;

    @Autowired
    private CheckTeamService checkTeamService;

    // 每页导出数量
    private static final int PAGE_SIZE = 5000;

    // 导出线程池大小
    private static final int EXPORT_THREADS = 5;

    /**
     * 查询路面平整度检测信息
     *
     * @param id 路面平整度检测信息主键
     * @return 路面平整度检测信息
     */
    @Override
    public RoadCheckRQI selectRoadRQIById(Long id) {
        return roadCheckRQIMapper.selectRoadRQIById(id);
    }

    /**
     * 查询路面平整度检测信息列表
     *
     * @param roadCheckRQI 路面平整度检测信息
     * @return 路面平整度检测信息
     */
    @Override
    public List<RoadCheckRQI> selectRoadRQIList(RoadCheckRQI roadCheckRQI) {
        return roadCheckRQIMapper.selectRoadRQIList(roadCheckRQI);
    }

    /**
     * 新增路面平整度检测信息
     *
     * @param roadCheckRQI 路面平整度检测信息
     * @return 结果
     */
    @Override
    public int insertRoadRQI(RoadCheckRQI roadCheckRQI) {
        roadCheckRQI.setCreateTime(DateUtils.getNowDate());
        roadCheckRQI.setUpdateTime(DateUtils.getNowDate());

        // 使用统一的桩号格式化和段位计算逻辑
        if (roadCheckRQI.getStartCode() != null && !roadCheckRQI.getStartCode().isEmpty()) {
            String formattedStartCode = StakeCodeUtil.formatStakeCode(roadCheckRQI.getStartCode().trim());
            roadCheckRQI.setStartCode(formattedStartCode);
            roadCheckRQI.setHundredSection(StakeCodeUtil.calculateHundredSection(formattedStartCode));
            roadCheckRQI.setThousandSection(StakeCodeUtil.calculateThousandSection(formattedStartCode));
        }

        if (roadCheckRQI.getEndCode() != null && !roadCheckRQI.getEndCode().isEmpty()) {
            String formattedEndCode = StakeCodeUtil.formatStakeCode(roadCheckRQI.getEndCode().trim());
            roadCheckRQI.setEndCode(formattedEndCode);
        }

        return roadCheckRQIMapper.insertRoadRQI(roadCheckRQI);
    }

    /**
     * 修改路面平整度检测信息
     *
     * @param roadCheckRQI 路面平整度检测信息
     * @return 结果
     */
    @Override
    public int updateRoadRQI(RoadCheckRQI roadCheckRQI) {
        roadCheckRQI.setUpdateTime(DateUtils.getNowDate());

        // 使用统一的桩号格式化和段位计算逻辑
        if (roadCheckRQI.getStartCode() != null && !roadCheckRQI.getStartCode().isEmpty()) {
            String formattedStartCode = StakeCodeUtil.formatStakeCode(roadCheckRQI.getStartCode().trim());
            roadCheckRQI.setStartCode(formattedStartCode);
            roadCheckRQI.setHundredSection(StakeCodeUtil.calculateHundredSection(formattedStartCode));
            roadCheckRQI.setThousandSection(StakeCodeUtil.calculateThousandSection(formattedStartCode));
        }

        if (roadCheckRQI.getEndCode() != null && !roadCheckRQI.getEndCode().isEmpty()) {
            String formattedEndCode = StakeCodeUtil.formatStakeCode(roadCheckRQI.getEndCode().trim());
            roadCheckRQI.setEndCode(formattedEndCode);
        }

        return roadCheckRQIMapper.updateRoadRQI(roadCheckRQI);
    }

    /**
     * 批量删除路面平整度检测信息
     *
     * @param ids 需要删除的路面平整度检测信息主键
     * @return 结果
     */
    @Override
    public int deleteRoadRQIByIds(Long[] ids) {
        return roadCheckRQIMapper.deleteRoadRQIByIds(ids);
    }

    /**
     * 删除路面平整度检测信息
     *
     * @param id 路面平整度检测信息主键
     * @return 结果
     */
    @Override
    public int deleteRoadRQIById(Long id) {
        return roadCheckRQIMapper.deleteRoadRQIById(id);
    }

    /**
     * 根据道路ID获取路面平整度检测信息
     *
     * @param roadId 道路ID
     * @return 路面平整度检测信息集合
     */
    @Override
    public List<RoadCheckRQI> selectRoadRQIByRoadId(Long roadId) {
        return roadCheckRQIMapper.selectRoadRQIByRoadId(roadId);
    }

    /**
     * 批量导入路面平整度检测数据
     * 适配"路面平整度及响应曲线质量指数(RQI)检测记录"表格
     * 支持多工作表：Sheet1为上行数据，Sheet2为下行数据
     *
     * @param file 导入文件
     * @param roadId 道路ID
     * @return 导入结果
     */
    @Override
    @Transactional
    public BatchAddResponse batchImport(MultipartFile file, Long roadId) {
        BatchAddResponse addResponse = new BatchAddResponse();
        InputStream fileStream = null;
        try {
            // 导入前先删除该道路的所有RQI数据
            int deletedCount = deleteRoadRQIByRoadId(roadId);
            log.info("导入前删除道路ID {} 的RQI数据：{} 条", roadId, deletedCount);
            
            // 生成本地缓存路径
            String localFile = FileUtil.saveExcelFile(file);
            fileStream = new FileInputStream(localFile);
            BufferedInputStream bufferedInputStream = new BufferedInputStream(fileStream);

            // 读取Excel文件
            Workbook workbook;
            if (file.getOriginalFilename().endsWith(".xlsx")) {
                workbook = WorkbookFactory.create(bufferedInputStream);
            } else {
                addResponse.setStatus(1);
                addResponse.setMsg("请使用Excel 2007及以上版本的文件（.xlsx格式）！");
                addResponse.generateSummary();
                return addResponse;
            }

            List<RoadCheckRQI> resultList = new ArrayList<>();

            // 处理Sheet1（上行数据）
            Sheet sheet1 = workbook.getSheetAt(0);
            if (sheet1 != null) {
                List<RoadCheckRQI> upDirectionData = processSheetData(sheet1, roadId, "上行", "Sheet1", addResponse);
                resultList.addAll(upDirectionData);
                log.info("Sheet1(上行)处理数据：{} 条", upDirectionData.size());
            }

            // 处理Sheet2（下行数据）
            if (workbook.getNumberOfSheets() > 1) {
                Sheet sheet2 = workbook.getSheetAt(1);
                if (sheet2 != null) {
                    List<RoadCheckRQI> downDirectionData = processSheetData(sheet2, roadId, "下行", "Sheet2", addResponse);
                    resultList.addAll(downDirectionData);
                    log.info("Sheet2(下行)处理数据：{} 条", downDirectionData.size());
                }
            }

            // 根据roadType分组并处理段位信息
            if (!resultList.isEmpty()) {
                log.info("开始处理RQI数据的段位信息，总计 {} 条记录", resultList.size());
                StakeCodeUtil.processSectionsByRoadType(resultList);
            }

            // 只插入校验通过的数据
            if (!resultList.isEmpty()) {
                List<List<RoadCheckRQI>> splitList = CollectUtil.splitList(resultList, 1000);
                for (List<RoadCheckRQI> tempList : splitList) {
                    roadCheckRQIMapper.batchInsert(tempList);
                }
                addResponse.addSuccessCount(resultList.size());
                log.info("成功导入RQI数据：{} 条", resultList.size());

                // 输出最后一行数据的段位信息用于验证
                if (!resultList.isEmpty()) {
                    RoadCheckRQI lastRecord = resultList.get(resultList.size() - 1);
                    log.info("导入RQI数据最后一条记录段位信息：起始桩号={}, 结束桩号={}, 百米段={}, 公里段={}",
                            lastRecord.getStartCode(), lastRecord.getEndCode(),
                            lastRecord.getHundredSection(), lastRecord.getThousandSection());
                }
            }

            // 设置最终状态
            if (addResponse.getSuccessCount() > 0 && addResponse.getFailCount() == 0) {
                addResponse.setStatus(0);
                addResponse.setMsg("导入成功");
            } else if (addResponse.getSuccessCount() > 0 && addResponse.getFailCount() > 0) {
                addResponse.setStatus(0);
                addResponse.setMsg("部分导入成功");
            } else {
                addResponse.setStatus(1);
                addResponse.setMsg("导入失败");
            }

            addResponse.generateSummary();
            workbook.close();

        } catch (IOException e) {
            log.error("文件解析失败", e);
            addResponse.setStatus(1);
            addResponse.setMsg("文件解析失败: " + e.getMessage());
            addResponse.generateSummary();
        } catch (Exception e) {
            log.error("导入数据失败", e);
            addResponse.setStatus(1);
            addResponse.setMsg("导入数据失败: " + e.getMessage());
            addResponse.generateSummary();
        } finally {
            try {
                if (fileStream != null) {
                    fileStream.close();
                }
            } catch (IOException e) {
                log.error("excel文件读取失败, 失败原因：{}", e);
            }
        }
        return addResponse;
    }

    /**
     * 获取记录总数
     */
    @Override
    public int countRoadRQI(RoadCheckRQI roadCheckRQI) {
        return roadCheckRQIMapper.countRoadRQI(roadCheckRQI);
    }

    /**
     * 导出路面平整度检测信息（大数据量优化版）
     */
    @Override
    public void exportOptimized(HttpServletResponse response, RoadCheckRQI roadCheckRQI) {
        try {
            // 获取总记录数
            int total = roadCheckRQIMapper.countRoadRQI(roadCheckRQI);
            if (total <= 0) {
                response.setContentType("application/json");
                response.getWriter().write("{\"code\":500, \"msg\":\"没有数据可以导出\"}");
                return;
            }

            // 使用SXSSFWorkbook，提高大数据量导出性能
            SXSSFWorkbook workbook = new SXSSFWorkbook(100); // 内存中保留100行，其余写入临时文件
            Sheet sheet = workbook.createSheet("路面平整度检测数据");

            // 创建表头
            createHeader(workbook, sheet);

            // 计算总页数
            int totalPages = (total + PAGE_SIZE - 1) / PAGE_SIZE;

            // 使用线程池和CountDownLatch并行处理数据
            ExecutorService executorService = Executors.newFixedThreadPool(Math.min(EXPORT_THREADS, totalPages));
            CountDownLatch latch = new CountDownLatch(totalPages);

            // 创建行数据同步锁，确保多线程写入Excel时顺序正确
            Object rowLock = new Object();
            final int[] rowIndex = {1}; // 从第1行开始写数据（第0行是表头）

            // 分页查询并写入数据
            for (int pageNum = 0; pageNum < totalPages; pageNum++) {
                final int offset = pageNum * PAGE_SIZE;
                executorService.execute(() -> {
                    try {
                        List<RoadCheckRQI> list = roadCheckRQIMapper.selectRoadRQIListByPage(
                                roadCheckRQI, offset, PAGE_SIZE);
                        if (!list.isEmpty()) {
                            synchronized (rowLock) {
                                for (RoadCheckRQI record : list) {
                                    Row row = sheet.createRow(rowIndex[0]++);
                                    fillRowData(row, record);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("导出数据处理异常", e);
                    } finally {
                        latch.countDown();
                    }
                });
            }

            // 等待所有线程完成
            latch.await();
            executorService.shutdown();

            // 设置响应头
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String fileName = "路面平整度检测信息_" + sdf.format(new Date()) + ".xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes("gb2312"), "ISO8859-1"));

            // 写入响应流
            workbook.write(response.getOutputStream());

            // 清理临时文件
            workbook.dispose();

        } catch (Exception e) {
            log.error("导出路面平整度检测信息失败", e);
            try {
                response.setContentType("application/json");
                response.getWriter().write("{\"code\":500, \"msg\":\"导出失败：" + e.getMessage() + "\"}");
            } catch (IOException ioException) {
                log.error("导出错误响应失败", ioException);
            }
        }
    }

    /**
     * 创建Excel表头
     */
    private void createHeader(SXSSFWorkbook workbook, Sheet sheet) {
        // 设置列宽
        sheet.setColumnWidth(0, 15 * 256);
        sheet.setColumnWidth(1, 15 * 256);
        sheet.setColumnWidth(2, 15 * 256);
        sheet.setColumnWidth(3, 15 * 256);

        // 创建表头样式
        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);

        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);

        // 创建表头行
        Row headerRow = sheet.createRow(0);

        // 创建表头单元格
        String[] headers = {
                "起始桩号", "终止桩号", "左IRI", "右IRI",
                "代表IRI", "RQI", "路面类型", "备注"
        };

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
    }

    /**
     * 填充行数据
     */
    private void fillRowData(Row row, RoadCheckRQI record) {
        // 基本数据格式
        CellStyle dataStyle = row.getSheet().getWorkbook().createCellStyle();
        dataStyle.setAlignment(HorizontalAlignment.CENTER);
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);

        // 数值格式，保留2位小数
        CellStyle number2Style = row.getSheet().getWorkbook().createCellStyle();
        number2Style.cloneStyleFrom(dataStyle);
        DataFormat format = row.getSheet().getWorkbook().createDataFormat();
        number2Style.setDataFormat(format.getFormat("0.00"));

        // 填充数据
        int colIndex = 0;

        // 桩号信息
        Cell cell0 = row.createCell(colIndex++);
        cell0.setCellValue(record.getStartCode());
        cell0.setCellStyle(dataStyle);

        Cell cell1 = row.createCell(colIndex++);
        cell1.setCellValue(record.getEndCode());
        cell1.setCellStyle(dataStyle);

        // IRI和RQI数据
        setDecimalCell(row, colIndex++, record.getLeftIri(), number2Style);
        setDecimalCell(row, colIndex++, record.getRightIri(), number2Style);
        setDecimalCell(row, colIndex++, record.getRepresentIri(), number2Style);
        setDecimalCell(row, colIndex++, record.getRqi(), number2Style);

        // 路面类型和备注
        Cell cell6 = row.createCell(colIndex++);
        cell6.setCellValue(record.getRoadType());
        cell6.setCellStyle(dataStyle);

        Cell cell7 = row.createCell(colIndex);
        cell7.setCellValue(record.getRemark());
        cell7.setCellStyle(dataStyle);
    }

    /**
     * 设置小数单元格
     */
    private void setDecimalCell(Row row, int colIndex, java.math.BigDecimal value, CellStyle style) {
        Cell cell = row.createCell(colIndex);
        if (value != null) {
            cell.setCellValue(value.doubleValue());
        } else {
            cell.setCellValue(0.0);
        }
        cell.setCellStyle(style);
    }

    /**
     * 获取单元格的字符串值
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        try {
            switch (cell.getCellType()) {
                case STRING:
                    return cell.getStringCellValue().trim();
                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        return new SimpleDateFormat("yyyy-MM-dd").format(cell.getDateCellValue());
                    }
                    // 对于数字，转为字符串并去除小数点后的零
                    String value = String.valueOf(cell.getNumericCellValue());
                    if (value.endsWith(".0")) {
                        value = value.substring(0, value.length() - 2);
                    }
                    return value;
                case BOOLEAN:
                    return String.valueOf(cell.getBooleanCellValue());
                case FORMULA:
                    try {
                        return String.valueOf(cell.getNumericCellValue());
                    } catch (Exception e) {
                        return cell.getStringCellValue();
                    }
                default:
                    return "";
            }
        } catch (Exception e) {
            log.warn("获取单元格字符串值失败: {}", e.getMessage());
            return "";
        }
    }

    /**
     * 获取单元格的BigDecimal值
     */
    private BigDecimal getCellBigDecimalValue(Cell cell) {
        if (cell == null) {
            return BigDecimal.ZERO;
        }

        try {
            double value;
            switch (cell.getCellType()) {
                case NUMERIC:
                    value = cell.getNumericCellValue();
                    break;
                case STRING:
                    String str = cell.getStringCellValue().trim();
                    if (str.isEmpty() || "-".equals(str)) {
                        return BigDecimal.ZERO;
                    }
                    value = Double.parseDouble(str);
                    break;
                case FORMULA:
                    try {
                        value = cell.getNumericCellValue();
                    } catch (Exception e) {
                        String strValue = cell.getStringCellValue().trim();
                        if (strValue.isEmpty() || "-".equals(strValue)) {
                            return BigDecimal.ZERO;
                        }
                        value = Double.parseDouble(strValue);
                    }
                    break;
                default:
                    return BigDecimal.ZERO;
            }
            // 使用字符串构造BigDecimal以保持精度
            return new BigDecimal(String.valueOf(value));
        } catch (Exception e) {
            log.warn("获取单元格BigDecimal值失败: {}", e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    /**
     * 处理单个工作表的RQI数据
     *
     * @param sheet 工作表
     * @param roadId 道路ID
     * @param direction 行驶方向（上行/下行）
     * @param sheetName 工作表名称
     * @param addResponse 响应对象
     * @return 解析后的数据列表
     */
    private List<RoadCheckRQI> processSheetData(Sheet sheet, Long roadId, String direction,
                                                String sheetName, BatchAddResponse addResponse) {
        List<RoadCheckRQI> resultList = new ArrayList<>();

        // 从第3行开始读取数据（索引从0开始，第3行对应索引2），跳过表头和标题行
        int startRow = 2;
        int rowCount = sheet.getPhysicalNumberOfRows();

        if (rowCount <= startRow) {
            log.warn("工作表 {} 中没有找到有效数据行", sheetName);
            return resultList;
        }

        // 将方向字符串转换为Integer值
        Integer directionValue = "上行".equals(direction) ? 1 : 2;

        // 遍历数据行
        for (int i = startRow; i < rowCount; i++) {
            Row row = sheet.getRow(i);
            if (row == null) {
                continue;
            }

            // 检查第一列是否为空（桩号）
            Cell stakeCell = row.getCell(0);
            if (stakeCell == null || getCellStringValue(stakeCell).trim().isEmpty()) {
                // 检查是否为空行
                boolean isEmptyRow = true;
                for (int j = 0; j < 9; j++) { // RQI有9列数据
                    Cell cell = row.getCell(j);
                    if (cell != null && !getCellStringValue(cell).trim().isEmpty()) {
                        isEmptyRow = false;
                        break;
                    }
                }
                if (!isEmptyRow) {
                    // 跳过前4行的错误报告（表头行）
                    if ((i + 1) > 4) {
                        addResponse.addError(sheetName, i + 1, "起始桩号", "起始桩号不能为空");
                    }
                }
                continue;
            }

            RoadCheckRQI roadCheckRQI = new RoadCheckRQI();

            // 设置道路ID和方向
            roadCheckRQI.setRoadId(roadId);
            roadCheckRQI.setDirection(directionValue);

            // 设置起始桩号和终止桩号
            String startCode = getCellStringValue(row.getCell(0));
            String endCode = getCellStringValue(row.getCell(2));

            // 使用StakeCodeUtil格式化桩号
            if (startCode != null && !startCode.trim().isEmpty()) {
                String formattedStartCode = StakeCodeUtil.formatStakeCode(startCode.trim());
                roadCheckRQI.setStartCode(formattedStartCode);
            } else {
                // 跳过前4行的错误报告（表头行）
                if ((i + 1) > 4) {
                    addResponse.addError(sheetName, i + 1, "起始桩号", "起始桩号不能为空");
                }
                continue;
            }

            if (endCode != null && !endCode.trim().isEmpty()) {
                String formattedEndCode = StakeCodeUtil.formatStakeCode(endCode.trim());
                roadCheckRQI.setEndCode(formattedEndCode);
            } else {
                // 跳过前4行的错误报告（表头行）
                if ((i + 1) > 4) {
                    addResponse.addError(sheetName, i + 1, "终止桩号", "终止桩号不能为空");
                }
                continue;
            }

            // 设置左IRI、右IRI和代表IRI
            roadCheckRQI.setLeftIri(validateAndGetDecimalValue(row.getCell(3), sheetName, i + 1, "左IRI", addResponse));
            roadCheckRQI.setRightIri(validateAndGetDecimalValue(row.getCell(4), sheetName, i + 1, "右IRI", addResponse));
            roadCheckRQI.setRepresentIri(validateAndGetDecimalValue(row.getCell(5), sheetName, i + 1, "代表IRI", addResponse));

            // 设置RQI值
            roadCheckRQI.setRqi(validateAndGetDecimalValue(row.getCell(6), sheetName, i + 1, "RQI", addResponse));

            // 设置路面类型
            roadCheckRQI.setRoadType(getCellStringValue(row.getCell(7)));

            // 设置备注（可选）
            roadCheckRQI.setRemark(getCellStringValue(row.getCell(8)));

            // 设置创建时间和用户信息
            roadCheckRQI.setCreateTime(new Date());
            roadCheckRQI.setUpdateTime(new Date());

            // 设置创建者和修改者
            SysUser user = SecurityUtils.getLoginUser().getUser();
            roadCheckRQI.setCreator(user.getUserId());
            roadCheckRQI.setModifier(user.getUserId());

            resultList.add(roadCheckRQI);
        }

        return resultList;
    }

    /**
     * 校验并获取数值型字段值
     *
     * @param cell 单元格
     * @param sheetName 工作表名称
     * @param rowNum 行号
     * @param fieldName 字段名称
     * @param addResponse 响应对象
     * @return 数值或null
     */
    private BigDecimal validateAndGetDecimalValue(Cell cell, String sheetName, int rowNum,
                                                  String fieldName, BatchAddResponse addResponse) {
        if (cell == null) {
            // 跳过前4行的错误报告（表头行）
            if (rowNum > 4) {
                addResponse.addError(sheetName, rowNum, fieldName, fieldName + "不能为空");
            }
            return null;
        }

        String cellValue = getCellStringValue(cell);
        if (cellValue.trim().isEmpty()) {
            // 跳过前4行的错误报告（表头行）
            if (rowNum > 4) {
                addResponse.addError(sheetName, rowNum, fieldName, fieldName + "不能为空", cellValue);
            }
            return null;
        }

        try {
            BigDecimal value = getCellBigDecimalValue(cell);
            if (value.compareTo(BigDecimal.ZERO) < 0) {
                // 跳过前4行的错误报告（表头行）
                if (rowNum > 4) {
                    addResponse.addError(sheetName, rowNum, fieldName, fieldName + "不能为负数", cellValue);
                }
                return null;
            }
            return value;
        } catch (Exception e) {
            // 跳过前4行的错误报告（表头行）
            if (rowNum > 4) {
                addResponse.addError(sheetName, rowNum, fieldName, fieldName + "格式不正确，应为数值", cellValue);
            }
            return null;
        }
    }

    /**
     * 导出RQI数据（分上行下行Sheet）- 不使用模板文件
     */
    @Override
    public void exportRQIByDirection(HttpServletResponse response, Long roadId) {
        try {
            // 查询指定路线的RQI数据
            RoadCheckRQI query = new RoadCheckRQI();
            query.setRoadId(roadId);
            query.setDirection(1);
            List<RoadCheckRQI> upData = roadCheckRQIMapper.selectRoadRQIList(query);
            query.setDirection(2);
            List<RoadCheckRQI> downData = roadCheckRQIMapper.selectRoadRQIList(query);

            // 检查是否有数据可以导出
            if (CollectionUtils.isEmpty(upData) && CollectionUtils.isEmpty(downData)) {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500,\"msg\":\"没有找到对应的RQI数据\"}");
                return;
            }

            // 合并上行下行数据用于分组计算
            List<RoadCheckRQI> allData = new ArrayList<>();
            allData.addAll(upData);
            allData.addAll(downData);

            // 在Java中计算百米汇总数据
            List<Map<String, Object>> hundredSectionData = calculateSectionSummary(allData, "hundred");

            // 在Java中计算公里汇总数据
            List<Map<String, Object>> thousandSectionData = calculateSectionSummary(allData, "thousand");

            // 查询道路基本信息
            Road road = roadMapper.selectRoadById(roadId);
            if (road == null) {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500,\"msg\":\"路线信息不存在\"}");
                return;
            }

            String roadName = road.getRoadName();
            String companyName = road.getCompanyName() != null ? road.getCompanyName() : "湖北交投智能检测股份有限公司";
            String startCode = road.getStartCode();
            String endCode = road.getEndCode();

            // 创建新的工作簿用于导出
            SXSSFWorkbook workbook = new SXSSFWorkbook();

            try {
                // 创建上行十米数据Sheet
                Sheet upSheet = workbook.createSheet("上行十米");
                createNewRQISheetHeader(upSheet, workbook, roadName, companyName, startCode, endCode);
                fillRQIDetailDataToSheet(upSheet, upData);

                // 创建下行十米数据Sheet
                Sheet downSheet = workbook.createSheet("下行十米");
                createNewRQISheetHeader(downSheet, workbook, roadName, companyName, startCode, endCode);
                fillRQIDetailDataToSheet(downSheet, downData);

                // 创建百米汇总数据Sheet
                Sheet hundredSheet = workbook.createSheet("百米汇总");
                createRQISheetStructureForSummary(hundredSheet, workbook, 9); // 9列
                fillHundredSectionDataToTemplate(hundredSheet, hundredSectionData, roadName, companyName, startCode, endCode);

                // 创建公里汇总数据Sheet
                Sheet thousandSheet = workbook.createSheet("公里汇总");
                createRQISheetStructureForSummary(thousandSheet, workbook, 10); // 10列
                fillThousandSectionDataToTemplate(thousandSheet, thousandSectionData, roadName, companyName, startCode, endCode);

                // 设置响应头
                String fileName = URLEncoder.encode(
                        "路面平整度及路面行驶质量（RQI）检测记录_" + road.getRoadCode() + "_" +
                                DateUtils.dateTimeNow() + ".xlsx", "UTF-8"
                );
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

                // 输出文件
                workbook.write(response.getOutputStream());

            } finally {
                workbook.close();
            }

        } catch (Exception e) {
            log.error("导出RQI数据失败", e);
            try {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500,\"msg\":\"导出失败：" + e.getMessage() + "\"}");
            } catch (IOException ex) {
                log.error("写入错误响应失败", ex);
            }
        }
    }

    /**
     * 创建新的RQI Sheet表头（上行十米/下行十米专用）
     */
    private void createNewRQISheetHeader(Sheet sheet, SXSSFWorkbook workbook, String roadName, 
                                        String companyName, String startCode, String endCode) {
        // 设置列宽
        sheet.setColumnWidth(0, 15 * 256); // A列
        sheet.setColumnWidth(1, 15 * 256); // B列
        sheet.setColumnWidth(2, 15 * 256); // C列
        sheet.setColumnWidth(3, 12 * 256); // D列
        sheet.setColumnWidth(4, 12 * 256); // E列
        sheet.setColumnWidth(5, 12 * 256); // F列
        sheet.setColumnWidth(6, 12 * 256); // G列
        sheet.setColumnWidth(7, 15 * 256); // H列
        sheet.setColumnWidth(8, 15 * 256); // I列

        // 创建样式
        CellStyle titleStyle = createTitleStyle(workbook);
        CellStyle headerStyle = createHeaderStyle(workbook);
        CellStyle dataStyle = createDataStyle(workbook);

        // 第一行：合并A-I列，显示标题
        Row row0 = sheet.createRow(0);
        row0.setHeight((short) 600);
        Cell titleCell = row0.createCell(0);
        titleCell.setCellValue("路面平整度及路面行驶质量（RQI）检测记录");
        titleCell.setCellStyle(titleStyle);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 8)); // 合并A-I列
        // 为其他被合并的单元格创建相同的样式
        for (int i = 1; i <= 8; i++) {
            Cell cell = row0.createCell(i);
            cell.setCellStyle(titleStyle);
        }

        // 第二行：项目名称和检测单位
        Row row1 = sheet.createRow(1);
        row1.setHeight((short) 400);
        // 合并A-E列，显示项目名称
        Cell projectCell = row1.createCell(0);
        projectCell.setCellValue("项目名称：" + (roadName != null ? roadName : ""));
        projectCell.setCellStyle(dataStyle);
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, 4)); // 合并A-E列
        for (int i = 1; i <= 4; i++) {
            Cell cell = row1.createCell(i);
            cell.setCellStyle(dataStyle);
        }
        // 合并F-I列，显示检测单位
        Cell companyCell = row1.createCell(5);
        companyCell.setCellValue("检测单位：" + companyName);
        companyCell.setCellStyle(dataStyle);
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 5, 8)); // 合并F-I列
        for (int i = 6; i <= 8; i++) {
            Cell cell = row1.createCell(i);
            cell.setCellStyle(dataStyle);
        }

        // 第三行：检测项目和检测段落
        Row row2 = sheet.createRow(2);
        row2.setHeight((short) 400);
        // 合并A-E列，显示检测项目
        Cell projectTypeCell = row2.createCell(0);
        projectTypeCell.setCellValue("检测项目：路面平整度");
        projectTypeCell.setCellStyle(dataStyle);
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 0, 4)); // 合并A-E列
        for (int i = 1; i <= 4; i++) {
            Cell cell = row2.createCell(i);
            cell.setCellStyle(dataStyle);
        }
        // 合并F-I列，显示检测段落
        Cell sectionCell = row2.createCell(5);
        String checkSection = (startCode != null ? startCode : "") + "~" + (endCode != null ? endCode : "");
        sectionCell.setCellValue("检测段落：" + checkSection);
        sectionCell.setCellStyle(dataStyle);
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 5, 8)); // 合并F-I列
        for (int i = 6; i <= 8; i++) {
            Cell cell = row2.createCell(i);
            cell.setCellStyle(dataStyle);
        }

        // 第四行：表头
        Row row3 = sheet.createRow(3);
        row3.setHeight((short) 400);
        // 合并A-C列，显示"桩号"
        Cell stakeCell = row3.createCell(0);
        stakeCell.setCellValue("桩号");
        stakeCell.setCellStyle(headerStyle);
        sheet.addMergedRegion(new CellRangeAddress(3, 3, 0, 2)); // 合并A-C列
        for (int i = 1; i <= 2; i++) {
            Cell cell = row3.createCell(i);
            cell.setCellStyle(headerStyle);
        }

        // D-I列，依次显示表头
        String[] headers = {"左IRI", "右IRI", "代表IRI", "RQI", "路面类型", "备注"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = row3.createCell(3 + i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
    }

    /**
     * 创建标题样式
     */
    private CellStyle createTitleStyle(SXSSFWorkbook workbook) {
        CellStyle style = createCellStyleWithFont(workbook, true);
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 14);
        font.setFontName("宋体");
        style.setFont(font);
        
        return style;
    }

    /**
     * 创建表头样式
     */
    private CellStyle createHeaderStyle(SXSSFWorkbook workbook) {
        CellStyle style = createCellStyleWithFont(workbook, true);
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 11);
        font.setFontName("宋体");
        style.setFont(font);
        
        return style;
    }

    /**
     * 创建数据样式
     */
    private CellStyle createDataStyle(SXSSFWorkbook workbook) {
        return createCellStyleWithFont(workbook, true);
    }

    /**
     * 填充RQI详细数据到Sheet（新格式）
     */
    private void fillRQIDetailDataToSheet(Sheet sheet, List<RoadCheckRQI> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }

        SXSSFWorkbook workbook = (SXSSFWorkbook) sheet.getWorkbook();
        CellStyle dataStyle = createDataStyle(workbook);
        
        // 创建数值样式（Times New Roman字体）
        CellStyle numberStyle = createCellStyleWithFont(workbook, false, "0.00");

        // 从第5行开始填充数据（前4行是表头）
        int rowIndex = 4;
        for (RoadCheckRQI record : dataList) {
            Row row = sheet.createRow(rowIndex++);
            row.setHeight((short) 350);

            int colIndex = 0;

            // A列：起始桩号
            Cell cellA = row.createCell(colIndex++);
            cellA.setCellValue(record.getStartCode() != null ? record.getStartCode() : "");
            cellA.setCellStyle(dataStyle);

            // B列：~
            Cell cellB = row.createCell(colIndex++);
            cellB.setCellValue("~");
            cellB.setCellStyle(dataStyle);

            // C列：结束桩号
            Cell cellC = row.createCell(colIndex++);
            cellC.setCellValue(record.getEndCode() != null ? record.getEndCode() : "");
            cellC.setCellStyle(dataStyle);

            // D列：左IRI
            Cell cellD = row.createCell(colIndex++);
            if (record.getLeftIri() != null) {
                cellD.setCellValue(record.getLeftIri().doubleValue());
            } else {
                cellD.setCellValue(0.0);
            }
            cellD.setCellStyle(numberStyle);

            // E列：右IRI
            Cell cellE = row.createCell(colIndex++);
            if (record.getRightIri() != null) {
                cellE.setCellValue(record.getRightIri().doubleValue());
            } else {
                cellE.setCellValue(0.0);
            }
            cellE.setCellStyle(numberStyle);

            // F列：代表IRI
            Cell cellF = row.createCell(colIndex++);
            if (record.getRepresentIri() != null) {
                cellF.setCellValue(record.getRepresentIri().doubleValue());
            } else {
                cellF.setCellValue(0.0);
            }
            cellF.setCellStyle(numberStyle);

            // G列：RQI
            Cell cellG = row.createCell(colIndex++);
            if (record.getRqi() != null) {
                cellG.setCellValue(record.getRqi().doubleValue());
            } else {
                cellG.setCellValue(0.0);
            }
            cellG.setCellStyle(numberStyle);

            // H列：路面类型
            Cell cellH = row.createCell(colIndex++);
            cellH.setCellValue(record.getRoadType() != null ? record.getRoadType() : "沥青路面");
            cellH.setCellStyle(dataStyle);

            // I列：备注
            Cell cellI = row.createCell(colIndex);
            String remark = record.getRemark();
            if (remark == null || remark.trim().isEmpty()) {
                // 根据direction字段判断
                if (record.getDirection() != null && record.getDirection() == 2) {
                    remark = "下行行车道";
                } else {
                    remark = "上行行车道";
                }
            }
            cellI.setCellValue(remark);
            cellI.setCellStyle(dataStyle);
        }
    }

    /**
     * 创建RQI Sheet结构（汇总表专用）
     */
    private void createRQISheetStructureForSummary(Sheet sheet, SXSSFWorkbook workbook, int columnCount) {
        // 设置列宽
        for (int i = 0; i < columnCount; i++) {
            sheet.setColumnWidth(i, 15 * 256);
        }

        // 创建第一行标题
        Row titleRow = sheet.createRow(0);
        titleRow.setHeight((short) 600);

        CellStyle titleStyle = createTitleStyle(workbook);

        // 创建第一行的第一个单元格并设置标题文本
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue("路面平整度及路面行驶质量 (RQI) 检测记录");
        titleCell.setCellStyle(titleStyle);

        // 合并第一行的所有列
        int lastCol = columnCount - 1;
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, lastCol));

        // 为其他被合并的单元格也创建相同的样式
        for (int i = 1; i <= lastCol; i++) {
            Cell cell = titleRow.createCell(i);
            cell.setCellStyle(titleStyle);
        }
    }

    @Override
    public void exportWordRQIByDirection(HttpServletResponse response, Long roadId) {
        exportWordRQIByDirection(response, roadId, null, null, null, null, null, null);
    }

    @Override
    public void exportWordRQIByDirection(HttpServletResponse response, Long roadId, Long teamId, String dateTime, String monthDate, String titleName, String checkName, String reviewName) {
        try {
            // 查询指定路线的RQI数据
            RoadCheckRQI query = new RoadCheckRQI();
            query.setRoadId(roadId);
            query.setDirection(1);
            List<RoadCheckRQI> upData = roadCheckRQIMapper.selectRoadRQIList(query);
            query.setDirection(2);
            List<RoadCheckRQI> downData = roadCheckRQIMapper.selectRoadRQIList(query);

            // 检查是否有数据可以导出
            if (CollectionUtils.isEmpty(upData) && CollectionUtils.isEmpty(downData)) {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500, \"msg\":\"没有RQI数据可以导出\"}");
                return;
            }

            Road road = roadMapper.selectRoadById(roadId);
            if (Objects.isNull(road)) {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500, \"msg\":\"路线不存在\"}");
                return;
            }
            // 合并上行和下行数据来获取完整的桩号范围
            List<RoadCheckRQI> allData = new ArrayList<>();
            if (upData != null && !upData.isEmpty()) allData.addAll(upData);
            if (downData != null && !downData.isEmpty()) allData.addAll(downData);
            // 生成上行和下行两个独立的Word文档，并打包成ZIP
            generateSeparateRQIWordDocuments(response, upData, downData, road, teamId, dateTime, monthDate, titleName, checkName, reviewName);

        } catch (Exception e) {
            log.error("导出RQI Word报告失败", e);
            try {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500, \"msg\":\"导出失败：" + e.getMessage() + "\"}");
            } catch (IOException ioException) {
                log.error("导出错误响应失败", ioException);
            }
        }
    }

    /**
     * 生成上行和下行两个独立的RQI Word文档
     */
    private void generateSeparateRQIWordDocuments(HttpServletResponse response,
                                                  List<RoadCheckRQI> upData, List<RoadCheckRQI> downData,
                                                  Road road, Long teamId, String dateTime, String monthDate,
                                                  String titleName, String checkName, String reviewName) {
        try {
            // 创建ZIP输出流
            response.setContentType("application/zip");
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String zipFileName = "路面RQI检测报告_" + sdf.format(new Date()) + ".zip";
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(zipFileName, "UTF-8"));

            ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream());

            // 使用并发工具类
            ExecutorService executor = Executors.newFixedThreadPool(2);
            CountDownLatch latch = new CountDownLatch(2);

            // 用于存储生成的文档和文件名
            final XWPFDocument[] documents = new XWPFDocument[2];
            final String[] fileNames = new String[2];
            final Exception[] exceptions = new Exception[2];

            // 并行生成上行文档
            if (!CollectionUtils.isEmpty(upData)) {
                executor.submit(() -> {
                    try {
                        log.info("开始生成RQI上行Word文档...");
                        documents[0] = createSingleDirectionRQIDocument(upData, downData, road, "上行", teamId, dateTime, monthDate, titleName, checkName, reviewName);
                        fileNames[0] = "路面RQI检测报告_上行_" + sdf.format(new Date()) + ".docx";
                        log.info("RQI上行Word文档生成完成");
                    } catch (Exception e) {
                        log.error("生成RQI上行Word文档失败", e);
                        exceptions[0] = e;
                    } finally {
                        latch.countDown();
                    }
                });
            } else {
                latch.countDown();
            }

            // 并行生成下行文档
            if (!CollectionUtils.isEmpty(downData)) {
                executor.submit(() -> {
                    try {
                        log.info("开始生成RQI下行Word文档...");
                        documents[1] = createSingleDirectionRQIDocument(upData, downData, road, "下行", teamId, dateTime, monthDate, titleName, checkName, reviewName);
                        fileNames[1] = "路面RQI检测报告_下行_" + sdf.format(new Date()) + ".docx";
                        log.info("RQI下行Word文档生成完成");
                    } catch (Exception e) {
                        log.error("生成RQI下行Word文档失败", e);
                        exceptions[1] = e;
                    } finally {
                        latch.countDown();
                    }
                });
            } else {
                latch.countDown();
            }

            // 等待所有文档生成完成
            latch.await();
            executor.shutdown();

            // 检查是否有异常发生
            if (exceptions[0] != null) {
                throw new RuntimeException("生成RQI上行Word文档失败", exceptions[0]);
            }
            if (exceptions[1] != null) {
                throw new RuntimeException("生成RQI下行Word文档失败", exceptions[1]);
            }

            // 将生成的文档写入ZIP
            if (documents[0] != null && fileNames[0] != null) {
                ZipEntry upEntry = new ZipEntry(fileNames[0]);
                zipOut.putNextEntry(upEntry);
                documents[0].write(zipOut);
                zipOut.closeEntry();
                documents[0].close();
                log.info("RQI上行文档已添加到ZIP文件");
            }

            if (documents[1] != null && fileNames[1] != null) {
                ZipEntry downEntry = new ZipEntry(fileNames[1]);
                zipOut.putNextEntry(downEntry);
                documents[1].write(zipOut);
                zipOut.closeEntry();
                documents[1].close();
                log.info("RQI下行文档已添加到ZIP文件");
            }

            zipOut.close();
            log.info("RQI ZIP文件生成完成");

        } catch (Exception e) {
            log.error("生成分离的RQI Word文档失败", e);
            throw new RuntimeException("生成分离的RQI Word文档失败", e);
        }
    }

    /**
     * 创建单个方向的RQI Word文档
     */
    private XWPFDocument createSingleDirectionRQIDocument(List<RoadCheckRQI> upData, List<RoadCheckRQI> downData,
                                                          Road road, String direction,
                                                          Long teamId, String dateTime, String monthDate,
                                                          String titleName, String checkName, String reviewName) {
        InputStream templateStream = null;
        try {
            // 解决POI安全限制：设置ZIP文件的最小压缩比率，避免Zip bomb检测误报
            org.apache.poi.openxml4j.util.ZipSecureFile.setMinInflateRatio(0.001);
            
            // 加载模板
            ClassPathResource templateResource = new ClassPathResource("static/word/rqi-template.docx");
            templateStream = templateResource.getInputStream();
            XWPFDocument document = new XWPFDocument(templateStream);
            log.info("成功加载RQI Word模板文件");

            // 合并上行下行数据用于分组计算
            List<RoadCheckRQI> allData = new ArrayList<>();
            allData.addAll(upData);
            allData.addAll(downData);

            // 在Java中计算公里汇总数据
            List<Map<String, Object>> thousandSectionData = calculateSectionSummary(allData, "thousand");

            // 计算各等级的公里数
            double totalDistanceSum = 0.0;
            double upExcellentDistance = 0.0;
            double upGoodDistance = 0.0;
            double upMediumDistance = 0.0;
            double upFairDistance = 0.0;
            double upPoorDistance = 0.0;
            double downExcellentDistance = 0.0;
            double downGoodDistance = 0.0;
            double downMediumDistance = 0.0;
            double downFairDistance = 0.0;
            double downPoorDistance = 0.0;

            int dataRowCount = 0;
            double upIriSum = 0.0;
            double upRqiSum = 0.0;
            double downIriSum = 0.0;
            double downRqiSum = 0.0;

            for (Map<String, Object> record : thousandSectionData) {
                // 计算该段位的实际距离（米）
                String startCodeVal = getStringValue(record.get("start_code"));
                String endCodeVal = getStringValue(record.get("end_code"));
                double sectionDistance = calculateSectionDistanceFromCodes(startCodeVal, endCodeVal);
                totalDistanceSum += sectionDistance;
                dataRowCount++;

                // 计算上行数据
                Object upAvgIri = record.get("up_avg_iri");
                if (upAvgIri != null) {
                    double upIriValue = Double.parseDouble(upAvgIri.toString());
                    upIriSum += upIriValue;
                    double upRqiValue = calculateRQI(upIriValue);
                    upRqiSum += upRqiValue;

                    // 根据RQI值统计各等级公里数
                    String upGrade = getRQIGradeByValue(upRqiValue);
                    switch(upGrade) {
                        case "优": upExcellentDistance += sectionDistance; break;
                        case "良": upGoodDistance += sectionDistance; break;
                        case "中": upMediumDistance += sectionDistance; break;
                        case "次": upFairDistance += sectionDistance; break;
                        case "差": upPoorDistance += sectionDistance; break;
                    }
                }

                // 计算下行数据
                Object downAvgIri = record.get("down_avg_iri");
                if (downAvgIri != null) {
                    double downIriValue = Double.parseDouble(downAvgIri.toString());
                    downIriSum += downIriValue;
                    double downRqiValue = calculateRQI(downIriValue);
                    downRqiSum += downRqiValue;

                    // 根据RQI值统计各等级公里数
                    String downGrade = getRQIGradeByValue(downRqiValue);
                    switch(downGrade) {
                        case "优": downExcellentDistance += sectionDistance; break;
                        case "良": downGoodDistance += sectionDistance; break;
                        case "中": downMediumDistance += sectionDistance; break;
                        case "次": downFairDistance += sectionDistance; break;
                        case "差": downPoorDistance += sectionDistance; break;
                    }
                }
            }

            // 查找第8页的汇总表格
            XWPFTable summaryTable = findTableOnPage(document,3);
            if (summaryTable != null) {
                int originalRows = summaryTable.getRows().size();
                log.info("找到RQI汇总表格，原始行数: {}", originalRows);

                // 清空表格中第3行及以后的所有行（保留前2行表头）
                while (summaryTable.getRows().size() > 2) {
                    summaryTable.removeRow(2);
                }
                log.info("清空表格后剩余行数: {}", summaryTable.getRows().size());

                // 填充公里汇总数据（参考fillThousandSectionDataToTemplate的逻辑）
                fillRQISummaryTable(summaryTable, thousandSectionData, totalDistanceSum,
                        upExcellentDistance, upGoodDistance, upMediumDistance, upFairDistance, upPoorDistance,
                        downExcellentDistance, downGoodDistance, downMediumDistance, downFairDistance, downPoorDistance);

                log.info("成功填充RQI汇总表格，共 {} 行数据，填充后行数: {}", thousandSectionData.size(), summaryTable.getRows().size());
            } else {
                log.error("未找到RQI汇总表格，无法填充汇总数据！");
            }

            // 加载最后一页的详细数据表格并填充数据
            List<XWPFTable> tables = document.getTables();
            XWPFTable detailTable = findTableOnLastPage(document);
            if (detailTable != null) {
                log.info("找到详细数据表格，原始行数: {}", detailTable.getRows().size());

                // 清空第二行的内容（如果存在的话）
                if (detailTable.getRows().size() > 1) {
                    XWPFTableRow secondRow = detailTable.getRow(1);
                    if (secondRow != null) {
                        // 清空第二行的所有单元格内容
                        for (int i = 0; i < secondRow.getTableCells().size(); i++) {
                            XWPFTableCell cell = secondRow.getCell(i);
                            if (cell != null) {
                                // 清除现有段落
                                while (cell.getParagraphs().size() > 0) {
                                    cell.removeParagraph(0);
                                }
                                // 创建新的空段落
                                cell.addParagraph();
                            }
                        }
                        log.info("已清空第二行内容");
                    }
                }

                // 清空第三行及以后的所有行
                while (detailTable.getRows().size() > 2) {
                    detailTable.removeRow(2);
                }
                log.info("清空表格后剩余行数: {}", detailTable.getRows().size());

                // 根据当前文档方向选择对应的详细数据
                List<RoadCheckRQI> detailData = new ArrayList<>();
                if ("上行".equals(direction) && upData != null && !upData.isEmpty()) {
                    detailData.addAll(upData);
                } else if ("下行".equals(direction) && downData != null && !downData.isEmpty()) {
                    detailData.addAll(downData);
                }

                // 按起始桩号排序
                detailData.sort((a, b) -> a.getStartCode().compareTo(b.getStartCode()));

                // 从第二行开始填充详细数据到表格
                fillRQIDetailTable(detailTable, detailData);

                log.info("成功填充{}详细数据表格，共 {} 行数据，填充后行数: {}", direction, detailData.size(), detailTable.getRows().size());
            } else {
                log.warn("未找到最后一页的详细数据表格");
            }

            // 填充检测人员信息到第二个表格（如果存在）
            if (document.getTables().size() >= 2) {
                checkTeamService.fillCheckTeamUserTable(document.getTables().get(1), teamId);
                log.info("成功填充RQI检测人员信息表格，使用分组ID: {}", teamId);
            } else {
                log.warn("未找到RQI检测人员信息表格（第二个表格）");
            }

            // 替换文档中的占位符
            WordDocumentUtils.replaceTextInDocument(document, "${roadName}", road.getRoadName());
            WordDocumentUtils.replaceTextInDocument(document, "${startCode}", road.getStartCode());
            WordDocumentUtils.replaceTextInDocument(document, "${endCode}", road.getEndCode());
            WordDocumentUtils.replaceTextInDocumentWithFontStyle(document, "${roadNames}", road.getRoadName(),"黑体",24);
            WordDocumentUtils.replaceTextInDocumentWithFontStyle(document, "${startCodes}", road.getStartCode(),"黑体",24);
            WordDocumentUtils.replaceTextInDocumentWithFontStyle(document, "${endCodes}", road.getEndCode(),"黑体",24);
            WordDocumentUtils.replaceTextInDocumentWithFontStyle(document, "${year}", String.valueOf(road.getYear()),"黑体",24);
            WordDocumentUtils.replaceTextInDocument(document, "${direction}", direction);
            WordDocumentUtils.replaceTextInDocument(document, "${companyName}", road.getCompanyName());
            WordDocumentUtils.replaceTextInDocument(document, "${projectName}", road.getProjectName());
            WordDocumentUtils.replaceTextInDocument(document, "${roadNameTitle}", road.getRoadName());
            WordDocumentUtils.replaceTextInDocument(document, "${reportNo}", road.getReportNo());
            WordDocumentUtils.replaceTextInDocument(document, "${number}", "02");
            
            // 替换日期相关占位符
            if (dateTime != null && !dateTime.isEmpty()) {
                replaceRQITextInDocument(document, "${dateTime}", dateTime);
            }
            if (monthDate != null && !monthDate.isEmpty()) {
                replaceRQITextInDocument(document, "${monthDate}", monthDate);
            }

            // 根据dateTime参数生成dateTimeStr并替换
            if (dateTime != null && !dateTime.isEmpty()) {
                String dateTimeStr = dateTime.replace("年", ".").replace("月", ".").replace("日", "");
                replaceRQITextInDocument(document, "${dateTimeStr}", dateTimeStr);
            }

            // 计算文档页数并替换页数占位符
            int[] pageCalculations = calculateRQIDocumentPagesWithDetails(thousandSectionData, upData, downData, direction);
            int totalPages = pageCalculations[0];
            int pageOne = pageCalculations[1];
            int pageTwo = pageCalculations[2];
            
            WordDocumentUtils.replaceTextInDocument(document, "${pages}", String.valueOf(totalPages));
            WordDocumentUtils.replaceTextInDocument(document, "${pageOne}", String.valueOf(pageOne));
            WordDocumentUtils.replaceTextInDocument(document, "${pageTwo}", String.valueOf(pageTwo));
            log.info("{}方向RQI文档共 {} 页, pageOne: {}, pageTwo: {}", direction, totalPages, pageOne, pageTwo);

            // 生成对齐的目录内容并替换占位符
            String[] catalogLines = WordDocumentUtils.generateAlignedCatalogLines(road.getRoadName(),"公路路面公里平整度及行驶质量RQI评定汇总表","公路路面平整度及行驶质量RQI检测表", direction, pageOne, pageTwo, totalPages);
            WordDocumentUtils.replaceTextInDocumentWithMixedFont(document, "${mulu1}", catalogLines[0]);
            WordDocumentUtils.replaceTextInDocumentWithMixedFont(document, "${mulu2}", catalogLines[1]);

            // 获取路线信息用于报告编号
            Road roadInfo = roadMapper.selectRoadById(upData != null && !upData.isEmpty() ? upData.get(0).getRoadId() : 
                                                     (downData != null && !downData.isEmpty() ? downData.get(0).getRoadId() : null));
            
            // 使用公共工具类替换标准占位符（包括报告编号）
            WordDocumentUtils.replaceStandardPlaceholders(document, "RQI", titleName, checkName, reviewName, roadInfo);

            return document;

        } catch (Exception e) {
            log.error("创建{}方向RQI Word文档失败", direction, e);
            throw new RuntimeException("创建" + direction + "方向RQI Word文档失败", e);
        } finally {
            // 确保流被关闭
            if (templateStream != null) {
                try {
                    templateStream.close();
                } catch (IOException e) {
                    log.warn("关闭模板流失败: {}", e.getMessage());
                }
            }
        }
    }



    /**
     * 在段落中替换文本，完全保持原有格式
     * 这个方法逐个run进行处理，确保格式不被破坏
     */
    private void replaceTextInParagraphPreservingFormat(XWPFParagraph paragraph, String placeholder, String replacement) {
        try {
            List<XWPFRun> runs = paragraph.getRuns();
            if (runs == null || runs.isEmpty()) {
                return;
            }

            // 构建完整的段落文本，同时记录每个字符对应的run索引
            StringBuilder fullText = new StringBuilder();
            List<Integer> charToRunMap = new ArrayList<>();

            for (int runIndex = 0; runIndex < runs.size(); runIndex++) {
                XWPFRun run = runs.get(runIndex);
                String runText = run.getText(0);
                if (runText != null) {
                    fullText.append(runText);
                    for (int i = 0; i < runText.length(); i++) {
                        charToRunMap.add(runIndex);
                    }
                }
            }

            String originalText = fullText.toString();
            if (!originalText.contains(placeholder)) {
                return;
            }

            // 找到占位符的位置，并选择一个目标run用于继承样式
            int placeholderStart = originalText.indexOf(placeholder);
            int targetRunIndex = (placeholderStart >= 0 && placeholderStart < charToRunMap.size()) ? charToRunMap.get(placeholderStart) : 0;
            XWPFRun targetRun = runs.get(targetRunIndex);

            // 保存目标run的样式
            Integer fontSize = targetRun.getFontSize();
            Boolean isBold = targetRun.isBold();
            Boolean isItalic = targetRun.isItalic();
            String color = targetRun.getColor();

            // 替换文本
            String newText = originalText.replace(placeholder, replacement);

            // 清除所有run
            for (int i = runs.size() - 1; i >= 0; i--) {
                paragraph.removeRun(i);
            }

            // 分段写入，按内容应用中英文字体，尽量继承原样式
            appendTextWithFonts(paragraph, newText, (fontSize != null && fontSize > 0) ? fontSize : null,
                    isBold, isItalic, color);
        } catch (Exception e) {
            log.warn("保持格式的文本替换失败: {}", e.getMessage());
            // 回退方案：简单替换
            try {
                String text = paragraph.getText();
                if (text != null && text.contains(placeholder)) {
                    String fallback = text.replace(placeholder, replacement);
                    for (int i = paragraph.getRuns().size() - 1; i >= 0; i--) {
                        paragraph.removeRun(i);
                    }
                    appendTextWithFonts(paragraph, fallback, null, null, null, null);
                }
            } catch (Exception fallbackException) {
                log.warn("备用文本替换也失败: {}", fallbackException.getMessage());
            }
        }
    }

    /**
     * 填充RQI汇总表格
     */
    private void fillRQISummaryTable(XWPFTable summaryTable, List<Map<String, Object>> thousandSectionData,
                                     double totalDistanceSum, double upExcellentDistance, double upGoodDistance,
                                     double upMediumDistance, double upFairDistance, double upPoorDistance,
                                     double downExcellentDistance, double downGoodDistance, double downMediumDistance,
                                     double downFairDistance, double downPoorDistance) {
        try {
            log.info("开始填充RQI汇总数据，数据条数: {}, 表格当前行数: {}", thousandSectionData.size(), summaryTable.getRows().size());
            log.info("数据将从第3行开始填充（前2行为表头）");

            // 填充数据行（从第3行开始）
            int processedRows = 0;
            for (Map<String, Object> record : thousandSectionData) {
                processedRows++;
                XWPFTableRow dataRow = summaryTable.createRow();

                // 确保行有足够的单元格（10列）
                while (dataRow.getTableCells().size() < 10) {
                    dataRow.createCell();
                }

                // A列：起始桩号
                String startCodeVal = getStringValue(record.get("start_code"));
                safeSetRQICellText(dataRow, 0, startCodeVal);

                if (processedRows <= 3) { // 只输出前3行的详细信息，避免日志过多
                    log.info("填充第 {} 行数据: 起始桩号={}", processedRows, startCodeVal);
                }

                // B列：~
                WordDocumentUtils.setCellTextWithVerticalCenter(dataRow, 1, "~");

                // C列：结束桩号
                String endCodeVal = getStringValue(record.get("end_code"));
                safeSetRQICellText(dataRow, 2, endCodeVal);

                // 计算该段位的实际距离（米）
                double sectionDistance = calculateSectionDistanceFromCodes(startCodeVal, endCodeVal);

                // D列：距离
                safeSetRQICellText(dataRow, 3, String.valueOf((int)sectionDistance));

                // E列：上行平均IRI
                Object upAvgIri = record.get("up_avg_iri");
                double upIriValue = 0.0;
                if (upAvgIri != null) {
                    upIriValue = Double.parseDouble(upAvgIri.toString());
                    double truncatedUpIri = Math.floor(upIriValue * 100) / 100.0;
                    safeSetRQICellText(dataRow, 4, String.format("%.2f", truncatedUpIri));
                } else {
                    safeSetRQICellText(dataRow, 4, "");
                }

                // F列：上行平均RQI
                double upRqiValue = 0.0;
                if (upAvgIri != null) {
                    upRqiValue = calculateRQI(upIriValue);
                    double roundedUpRqi = Math.round(upRqiValue * 100.0) / 100.0;
                    safeSetRQICellText(dataRow, 5, String.format("%.2f", roundedUpRqi));
                } else {
                    safeSetRQICellText(dataRow, 5, "");
                }

                // G列：上行RQI评分
                String upGrade = getRQIGradeByValue(upRqiValue);
                safeSetRQICellText(dataRow, 6, upGrade);

                // H列：下行平均IRI
                Object downAvgIri = record.get("down_avg_iri");
                double downIriValue = 0.0;
                if (downAvgIri != null) {
                    downIriValue = Double.parseDouble(downAvgIri.toString());
                    double truncatedDownIri = Math.floor(downIriValue * 100) / 100.0;
                    safeSetRQICellText(dataRow, 7, String.format("%.2f", truncatedDownIri));
                } else {
                    safeSetRQICellText(dataRow, 7, "");
                }

                // I列：下行RQI
                double calculatedDownRqi = 0.0;
                if (downAvgIri != null) {
                    calculatedDownRqi = calculateRQI(downIriValue);
                    double roundedDownRqi = Math.round(calculatedDownRqi * 100.0) / 100.0;
                    safeSetRQICellText(dataRow, 8, String.format("%.2f", roundedDownRqi));
                } else {
                    safeSetRQICellText(dataRow, 8, "");
                }

                // J列：下行RQI评分
                String downGrade = getRQIGradeByValue(calculatedDownRqi);
                safeSetRQICellText(dataRow, 9, downGrade);
            }

            log.info("完成填充 {} 行数据到RQI汇总表格", processedRows);

            // 添加合计行
            if (!thousandSectionData.isEmpty()) {
                XWPFTableRow summaryRow = summaryTable.createRow();

                // 确保行有足够的单元格
                while (summaryRow.getTableCells().size() < 10) {
                    summaryRow.createCell();
                }

                // 计算平均值
                int dataRowCount = thousandSectionData.size();
                double upIriSum = 0.0;
                double downIriSum = 0.0;

                // 重新计算IRI平均值
                for (Map<String, Object> record : thousandSectionData) {
                    Object upAvgIri = record.get("up_avg_iri");
                    if (upAvgIri != null) {
                        upIriSum += Double.parseDouble(upAvgIri.toString());
                    }
                    Object downAvgIri = record.get("down_avg_iri");
                    if (downAvgIri != null) {
                        downIriSum += Double.parseDouble(downAvgIri.toString());
                    }
                }

                double avgUpIri = upIriSum / dataRowCount;
                double avgDownIri = downIriSum / dataRowCount;

                // 设置合计行内容
                safeSetRQICellText(summaryRow, 0, "合计");
                safeSetRQICellText(summaryRow, 1, "");
                safeSetRQICellText(summaryRow, 2, "");
                safeSetRQICellText(summaryRow, 3, String.valueOf((int)totalDistanceSum));

                // 添加合计行的单元格合并：第1、2、3列（索引0、1、2）
                try {
                    // 使用水平合并标记来合并第1、2、3列
                    XWPFTableCell cell0 = summaryRow.getCell(0);
                    XWPFTableCell cell1 = summaryRow.getCell(1);
                    XWPFTableCell cell2 = summaryRow.getCell(2);

                    // 设置第一个单元格为合并开始
                    cell0.getCTTc().addNewTcPr().addNewHMerge().setVal(
                            org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge.RESTART);

                    // 设置第二、三个单元格为合并继续
                    cell1.getCTTc().addNewTcPr().addNewHMerge().setVal(
                            org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge.CONTINUE);
                    cell2.getCTTc().addNewTcPr().addNewHMerge().setVal(
                            org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge.CONTINUE);

                    log.info("合计行第1、2、3列合并成功");
                } catch (Exception e) {
                    log.warn("合计行单元格合并失败: {}", e.getMessage());
                }

                // 上行IRI和RQI平均值
                double truncatedAvgUpIri = Math.floor(avgUpIri * 100) / 100.0;
                safeSetRQICellText(summaryRow, 4, String.format("%.2f", truncatedAvgUpIri));
                double calculatedUpRqi = Math.round((calculateRQI(truncatedAvgUpIri)) * 100.0) / 100.0;
                safeSetRQICellText(summaryRow, 5, String.format("%.2f", calculatedUpRqi));
                String upGradeForSummary = getRQIGradeByValue(calculatedUpRqi);
                safeSetRQICellText(summaryRow, 6, upGradeForSummary);

                // 下行IRI和RQI平均值
                double truncatedAvgDownIri = Math.floor(avgDownIri * 100) / 100.0;
                safeSetRQICellText(summaryRow, 7, String.format("%.2f", truncatedAvgDownIri));
                double calculatedDownRqi = Math.round((calculateRQI(truncatedAvgDownIri)) * 100.0) / 100.0;
                safeSetRQICellText(summaryRow, 8, String.format("%.2f", calculatedDownRqi));
                String downGradeForSummary = getRQIGradeByValue(calculatedDownRqi);
                safeSetRQICellText(summaryRow, 9, downGradeForSummary);

                // 添加等级统计行
                addRQIGradeRowToWord(summaryTable, "优 (%)", upExcellentDistance, downExcellentDistance, totalDistanceSum);
                addRQIGradeRowToWord(summaryTable, "良 (%)", upGoodDistance, downGoodDistance, totalDistanceSum);
                addRQIGradeRowToWord(summaryTable, "中 (%)", upMediumDistance, downMediumDistance, totalDistanceSum);
                addRQIGradeRowToWord(summaryTable, "次 (%)", upFairDistance, downFairDistance, totalDistanceSum);
                addRQIGradeRowToWord(summaryTable, "差 (%)", upPoorDistance, downPoorDistance, totalDistanceSum);
            }
        } catch (Exception e) {
            log.error("填充RQI汇总表格失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 填充RQI详细数据表格
     */
    private void fillRQIDetailTable(XWPFTable detailTable, List<RoadCheckRQI> dataList) {
        try {
            log.info("开始填充RQI详细数据，数据条数: {}, 表格当前行数: {}", dataList.size(), detailTable.getRows().size());

            int rowIndex = 1; // 从第二行开始（索引为1）
            int processedRows = 0;

            for (RoadCheckRQI record : dataList) {
                XWPFTableRow dataRow;

                // 如果第二行已存在，直接使用；否则创建新行
                if (rowIndex == 1 && detailTable.getRows().size() > 1) {
                    dataRow = detailTable.getRow(1);

                    // 确保行有足够的单元格（9列）
                    while (dataRow.getTableCells().size() < 9) {
                        dataRow.createCell();
                    }
                } else {
                    // 创建新行
                    dataRow = detailTable.createRow();

                    // 确保行有足够的单元格（9列）
                    while (dataRow.getTableCells().size() < 9) {
                        dataRow.createCell();
                    }
                }

                processedRows++;
                if (processedRows <= 3) { // 只输出前3行的详细信息，避免日志过多
                    log.info("填充第 {} 行数据: 起始桩号={}", processedRows, record.getStartCode());
                }

                // 填充数据行内容（参考fillRQIDataToTemplate的逻辑）
                int colIndex = 0;

                // A列：起始桩号
                safeSetRQICellText(dataRow, colIndex++, record.getStartCode() != null ? record.getStartCode() : "");

                // B列：~
                com.tunnel.common.utils.WordDocumentUtils.setCellTextWithVerticalCenter(dataRow, colIndex++, "~");

                // C列：结束桩号
                safeSetRQICellText(dataRow, colIndex++, record.getEndCode() != null ? record.getEndCode() : "");

                // D列：左IRI
                safeSetRQICellText(dataRow, colIndex++, formatRQIDecimalValue(record.getLeftIri()));

                // E列：右IRI
                safeSetRQICellText(dataRow, colIndex++, formatRQIDecimalValue(record.getRightIri()));

                // F列：代表IRI
                safeSetRQICellText(dataRow, colIndex++, formatRQIDecimalValue(record.getRepresentIri()));

                // G列：RQI
                safeSetRQICellText(dataRow, colIndex++, formatRQIDecimalValue(record.getRqi()));

                // H列：路面类型
                safeSetRQICellText(dataRow, colIndex++, record.getRoadType() != null ? record.getRoadType() : "沥青路面");

                // I列：备注
                String remark = record.getRemark();
                if (remark == null || remark.trim().isEmpty()) {
                    // 根据direction字段判断
                    if (record.getDirection() != null && record.getDirection() == 2) {
                        remark = "下行行车道";
                    } else {
                        remark = "上行行车道";
                    }
                }
                safeSetRQICellText(dataRow, colIndex, remark);

                rowIndex++;
            }

            log.info("完成填充 {} 行数据到RQI详细表格", processedRows);
        } catch (Exception e) {
            log.error("填充RQI详细数据表格失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 添加RQI等级统计行到Word表格
     */
    private void addRQIGradeRowToWord(XWPFTable table, String gradeName, double upDistance,
                                      double downDistance, double totalDistance) {
        XWPFTableRow row = table.createRow();

        // 确保行有足够的单元格
        while (row.getTableCells().size() < 10) {
            row.createCell();
        }

        // 设置等级统计行内容
        safeSetRQICellText(row, 0, gradeName);
        safeSetRQICellText(row, 1, "");
        safeSetRQICellText(row, 2, "");
        safeSetRQICellText(row, 3, ""); // D列为空
        safeSetRQICellText(row, 4, String.valueOf((int)upDistance));

        // F、G列合并显示上行占比
        double upPercentage = (totalDistance > 0) ? (upDistance / totalDistance) : 0;
        safeSetRQICellText(row, 5, String.format("%.2f%%", upPercentage * 100));
        safeSetRQICellText(row, 6, "");

        safeSetRQICellText(row, 7, String.valueOf((int)downDistance));

        // I、J列合并显示下行占比
        double downPercentage = (totalDistance > 0) ? (downDistance / totalDistance) : 0;
        safeSetRQICellText(row, 8, String.format("%.2f%%", downPercentage * 100));
        safeSetRQICellText(row, 9, "");

        // 添加等级统计行的单元格合并
        try {
            // 合并第1、2、3列（索引0、1、2）
            XWPFTableCell cell0 = row.getCell(0);
            XWPFTableCell cell1 = row.getCell(1);
            XWPFTableCell cell2 = row.getCell(2);

            cell0.getCTTc().addNewTcPr().addNewHMerge().setVal(
                    org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge.RESTART);
            cell1.getCTTc().addNewTcPr().addNewHMerge().setVal(
                    org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge.CONTINUE);
            cell2.getCTTc().addNewTcPr().addNewHMerge().setVal(
                    org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge.CONTINUE);

            // 合并第6、7列（索引5、6）
            XWPFTableCell cell5 = row.getCell(5);
            XWPFTableCell cell6 = row.getCell(6);

            cell5.getCTTc().addNewTcPr().addNewHMerge().setVal(
                    org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge.RESTART);
            cell6.getCTTc().addNewTcPr().addNewHMerge().setVal(
                    org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge.CONTINUE);

            // 合并第9、10列（索引8、9）
            XWPFTableCell cell8 = row.getCell(8);
            XWPFTableCell cell9 = row.getCell(9);

            cell8.getCTTc().addNewTcPr().addNewHMerge().setVal(
                    org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge.RESTART);
            cell9.getCTTc().addNewTcPr().addNewHMerge().setVal(
                    org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge.CONTINUE);

            log.debug("等级统计行 {} 单元格合并成功", gradeName);
        } catch (Exception e) {
            log.warn("等级统计行 {} 单元格合并失败: {}", gradeName, e.getMessage());
        }
    }

    /**
     * 安全地设置RQI Word表格单元格文本
     */
    private void safeSetRQICellText(XWPFTableRow row, int cellIndex, String text) {
        try {
            if (row != null && cellIndex >= 0 && cellIndex < row.getTableCells().size()) {
                XWPFTableCell cell = row.getCell(cellIndex);
                if (cell != null) {
                    // 清除现有段落，创建新的居中段落
                    cell.removeParagraph(0);
                    XWPFParagraph paragraph = cell.addParagraph();
                    paragraph.setAlignment(ParagraphAlignment.CENTER);

                    // 按内容分段设置字体：字母/数字/符号用 Times New Roman，中文用宋体
                    appendTextWithFonts(paragraph, text != null ? text : "", 9, null, null, null);

                    // 设置单元格垂直居中对齐
                    cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
                }
            }
        } catch (Exception e) {
            log.warn("设置RQI单元格文本失败 [行:{}, 列:{}]: {}",
                    row != null ? row.getTableCells().size() : "null", cellIndex, e.getMessage());
        }
    }

    /**
     * 格式化RQI小数值
     */
    private String formatRQIDecimalValue(BigDecimal value) {
        if (value == null) {
            return "0.00";
        }
        return String.format("%.2f", value.doubleValue());
    }

    /**
     * 替换RQI文档中的占位符
     */
    private void replaceRQITextInDocument(XWPFDocument document, String placeholder, String value) {
        try {
            // 替换段落中的文本
            for (XWPFParagraph paragraph : document.getParagraphs()) {
                // 对于页眉标题，使用更精确的替换方法，保持原有格式
                if (placeholder.equals("${roadNameTitle}")) {
                    replaceTextInParagraphPreservingFormat(paragraph, placeholder, value != null ? value : "");
                } else {
                    replaceRQIParagraphText(paragraph, placeholder, value);
                }
            }

            // 替换表格中的文本
            for (XWPFTable table : document.getTables()) {
                for (XWPFTableRow row : table.getRows()) {
                    for (XWPFTableCell cell : row.getTableCells()) {
                        for (XWPFParagraph paragraph : cell.getParagraphs()) {
                            // 对于页眉标题，使用更精确的替换方法，保持原有格式
                            if (placeholder.equals("${roadNameTitle}")) {
                                replaceTextInParagraphPreservingFormat(paragraph, placeholder, value != null ? value : "");
                            } else {
                                replaceRQIParagraphText(paragraph, placeholder, value);
                            }
                        }
                    }
                }
            }

            // 替换页眉中的文本
            for (XWPFHeader header : document.getHeaderList()) {
                for (XWPFParagraph paragraph : header.getParagraphs()) {
                    // 对于页眉标题，使用更精确的替换方法，保持原有格式
                    if (placeholder.equals("${roadNameTitle}")) {
                        replaceTextInParagraphPreservingFormat(paragraph, placeholder, value != null ? value : "");
                    } else {
                        replaceRQIParagraphText(paragraph, placeholder, value);
                    }
                }
                for (XWPFTable table : header.getTables()) {
                    for (XWPFTableRow row : table.getRows()) {
                        for (XWPFTableCell cell : row.getTableCells()) {
                            for (XWPFParagraph paragraph : cell.getParagraphs()) {
                                // 对于页眉标题，使用更精确的替换方法，保持原有格式
                                if (placeholder.equals("${roadNameTitle}")) {
                                    replaceTextInParagraphPreservingFormat(paragraph, placeholder, value != null ? value : "");
                                } else {
                                    replaceRQIParagraphText(paragraph, placeholder, value);
                                }
                            }
                        }
                    }
                }
            }

            // 替换页脚中的文本
            for (XWPFFooter footer : document.getFooterList()) {
                for (XWPFParagraph paragraph : footer.getParagraphs()) {
                    // 对于页眉标题，使用更精确的替换方法，保持原有格式
                    if (placeholder.equals("${roadNameTitle}")) {
                        replaceTextInParagraphPreservingFormat(paragraph, placeholder, value != null ? value : "");
                    } else {
                        replaceRQIParagraphText(paragraph, placeholder, value);
                    }
                }
                for (XWPFTable table : footer.getTables()) {
                    for (XWPFTableRow row : table.getRows()) {
                        for (XWPFTableCell cell : row.getTableCells()) {
                            for (XWPFParagraph paragraph : cell.getParagraphs()) {
                                // 对于页眉标题，使用更精确的替换方法，保持原有格式
                                if (placeholder.equals("${roadNameTitle}")) {
                                    replaceTextInParagraphPreservingFormat(paragraph, placeholder, value != null ? value : "");
                                } else {
                                    replaceRQIParagraphText(paragraph, placeholder, value);
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("替换RQI文档占位符失败 [{}]: {}", placeholder, e.getMessage());
        }
    }

    /**
     * 替换RQI段落中的文本
     */
    private void replaceRQIParagraphText(XWPFParagraph paragraph, String placeholder, String value) {
        try {
            String text = paragraph.getText();
            if (text != null && text.contains(placeholder)) {
                String newText = text.replace(placeholder, value != null ? value : "");

                // 保存第一个run的样式信息（若有）
                XWPFRun firstRun = paragraph.getRuns().size() > 0 ? paragraph.getRuns().get(0) : null;
                Integer fontSize = firstRun != null ? firstRun.getFontSize() : null;
                Boolean isBold = firstRun != null ? firstRun.isBold() : null;
                Boolean isItalic = firstRun != null ? firstRun.isItalic() : null;
                String color = firstRun != null ? firstRun.getColor() : null;

                // 清除所有runs
                int runs = paragraph.getRuns().size();
                for (int i = runs - 1; i >= 0; i--) {
                    paragraph.removeRun(i);
                }

                // 使用分段写入，按内容应用中英文字体
                appendTextWithFonts(paragraph, newText, (fontSize != null && fontSize > 0) ? fontSize : null,
                        isBold, isItalic, color);
            }
        } catch (Exception e) {
            log.warn("替换RQI段落文本失败: {}", e.getMessage());
        }
    }

    /**
     * 计算段位汇总数据（百米或公里）
     */
    private List<Map<String, Object>> calculateSectionSummary(List<RoadCheckRQI> allData, String sectionType) {
        // 按段位和方向分组
        Map<String, Map<Integer, List<RoadCheckRQI>>> sectionGroups = new HashMap<>();

        for (RoadCheckRQI record : allData) {
            String sectionKey;
            if ("hundred".equals(sectionType)) {
                sectionKey = record.getHundredSection();
            } else {
                sectionKey = record.getThousandSection();
            }

            if (sectionKey != null && !sectionKey.trim().isEmpty()) {
                sectionGroups.computeIfAbsent(sectionKey, k -> new HashMap<>())
                        .computeIfAbsent(record.getDirection(), k -> new ArrayList<>())
                        .add(record);
            }
        }

        // 计算每个段位的汇总数据
        List<Map<String, Object>> result = new ArrayList<>();

        for (Map.Entry<String, Map<Integer, List<RoadCheckRQI>>> sectionEntry : sectionGroups.entrySet()) {
            String section = sectionEntry.getKey();
            Map<Integer, List<RoadCheckRQI>> directionData = sectionEntry.getValue();

            Map<String, Object> summary = new HashMap<>();

            // 获取起始和结束桩号
            String startCode = null;
            String endCode = null;
            String pavementType = null;
            String remark = null;

            for (List<RoadCheckRQI> records : directionData.values()) {
                for (RoadCheckRQI record : records) {
                    if (startCode == null || (record.getStartCode() != null && record.getStartCode().compareTo(startCode) < 0)) {
                        startCode = record.getStartCode();
                    }
                    if (endCode == null || (record.getEndCode() != null && record.getEndCode().compareTo(endCode) > 0)) {
                        endCode = record.getEndCode();
                    }
                    if (pavementType == null && record.getRoadType() != null) {
                        pavementType = record.getRoadType();
                    }
                    if (remark == null && record.getRemark() != null) {
                        remark = record.getRemark();
                    }
                }
            }

            summary.put("hundred_section", section);
            summary.put("start_code", startCode);
            summary.put("end_code", endCode);
            summary.put("pavement_type", pavementType);
            summary.put("remark", remark);

            // 计算上行数据
            List<RoadCheckRQI> upRecords = directionData.get(1);
            if (upRecords != null && !upRecords.isEmpty()) {
                double avgIri = upRecords.stream()
                        .filter(r -> r.getRepresentIri() != null)
                        .mapToDouble(r -> r.getRepresentIri().doubleValue())
                        .average()
                        .orElse(0.0);
                double rqi = calculateRQI(avgIri);

                summary.put("up_avg_iri", avgIri);
                summary.put("up_avg_rqi", Math.round(rqi * 10.0) / 10.0); // 保留一位小数
            }

            // 计算下行数据
            List<RoadCheckRQI> downRecords = directionData.get(2);
            if (downRecords != null && !downRecords.isEmpty()) {
                double avgIri = downRecords.stream()
                        .filter(r -> r.getRepresentIri() != null)
                        .mapToDouble(r -> r.getRepresentIri().doubleValue())
                        .average()
                        .orElse(0.0);
                double rqi = calculateRQI(avgIri);

                summary.put("down_avg_iri", avgIri);
                summary.put("down_avg_rqi", Math.round(rqi * 10.0) / 10.0); // 保留一位小数
            }

            result.add(summary);
        }

        // 按段位排序
        result.sort((a, b) -> {
            String sectionA = (String) a.get("hundred_section");
            String sectionB = (String) b.get("hundred_section");
            return sectionA != null && sectionB != null ? sectionA.compareTo(sectionB) : 0;
        });

        return result;
    }

    /**
     * 计算RQI值：RQI = 100/(1+0.026*EXP(0.65*IRI值))
     */
    private double calculateRQI(double iri) {
        return 100.0 / (1.0 + 0.026 * Math.exp(0.65 * iri));
    }

    /**
     * 复制RQI Sheet结构
     */
    private void copyRQISheetStructure(Sheet templateSheet, Sheet newSheet, SXSSFWorkbook workbook) {
        // 根据Sheet名称确定列数，公里汇总需要10列(A-J)，其他为9列(A-I)
        int columnCount = newSheet.getSheetName().contains("公里汇总") ? 10 : 9;

        // 复制列宽
        for (int i = 0; i < columnCount; i++) {
            if (i < templateSheet.getRow(0).getLastCellNum()) {
                newSheet.setColumnWidth(i, templateSheet.getColumnWidth(i));
            } else {
                newSheet.setColumnWidth(i, 15 * 256); // 默认列宽
            }
        }

        // 手动创建第一行标题，不复制模板样式
        Row titleRow = newSheet.createRow(0);
        titleRow.setHeight((short) 600); // 设置标题行高

        // 创建标题样式
        CellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        titleStyle.setBorderTop(BorderStyle.THIN);
        titleStyle.setBorderRight(BorderStyle.THIN);
        titleStyle.setBorderBottom(BorderStyle.THIN);
        titleStyle.setBorderLeft(BorderStyle.THIN);

        // 创建标题字体
        Font titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 14);
        titleFont.setFontName("宋体");
        titleStyle.setFont(titleFont);

        // 创建第一行的第一个单元格并设置标题文本
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue("路面平整度及路面行驶质量 (RQI) 检测记录");
        titleCell.setCellStyle(titleStyle);

        // 合并第一行的所有列，公里汇总为A到J列，其他为A到I列
        int lastCol = columnCount - 1;
        newSheet.addMergedRegion(new CellRangeAddress(0, 0, 0, lastCol));

        // 为其他被合并的单元格也创建相同的样式
        for (int i = 1; i <= lastCol; i++) {
            Cell cell = titleRow.createCell(i);
            cell.setCellStyle(titleStyle);
        }
    }

    /**
     * 填充RQI数据到模板Sheet
     */
    private void fillRQIDataToTemplate(Sheet sheet, List<RoadCheckRQI> dataList, String roadName,
                                       String companyName, String startCode, String endCode) {
        SXSSFWorkbook workbook = (SXSSFWorkbook) sheet.getWorkbook();
        
        // 创建统一的数据样式（中文字体）
        CellStyle dataStyle = createCellStyleWithFont(workbook, true);

        // 创建数值样式（Times New Roman字体）
        CellStyle numberStyle = createCellStyleWithFont(workbook, false, "0.00");

        // 创建RQI专用样式（一位小数，Times New Roman字体）- 仅用于百米汇总
        CellStyle rqiStyle = createCellStyleWithFont(workbook, false, "0.0");

        // 第二行：项目名称和检测单位
        Row row1 = sheet.createRow(1);
        row1.setHeight((short) 400); // 设置行高

        // 合并ABCDE列，写入项目名称
        Cell cellA1 = row1.createCell(0);
        cellA1.setCellValue("项目名称：" + (roadName != null ? roadName : ""));
        cellA1.setCellStyle(dataStyle);
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, 4)); // 合并A到E列

        // 创建被合并的空单元格
        for (int i = 1; i <= 4; i++) {
            Cell cell = row1.createCell(i);
            cell.setCellStyle(dataStyle);
        }

        // 合并FGHI列，写入检测单位
        Cell cellF1 = row1.createCell(5);
        cellF1.setCellValue("检测单位：" + (companyName != null ? companyName : "湖北交投智能检测股份有限公司"));
        cellF1.setCellStyle(dataStyle);
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 5, 8)); // 合并F到I列

        // 创建被合并的空单元格
        for (int i = 6; i <= 8; i++) {
            Cell cell = row1.createCell(i);
            cell.setCellStyle(dataStyle);
        }

        // 第三行：检测项目和检测段落
        Row row2 = sheet.createRow(2);
        row2.setHeight((short) 400); // 设置行高

        // 合并ABCDE列，写入检测项目
        Cell cellA2 = row2.createCell(0);
        cellA2.setCellValue("检测项目：路面平整度");
        cellA2.setCellStyle(dataStyle);
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 0, 4)); // 合并A到E列

        // 创建被合并的空单元格
        for (int i = 1; i <= 4; i++) {
            Cell cell = row2.createCell(i);
            cell.setCellStyle(dataStyle);
        }

        // 合并FGHI列，写入检测段落
        Cell cellF2 = row2.createCell(5);
        String checkSection = (startCode != null ? startCode : "") + "～" + (endCode != null ? endCode : "");
        cellF2.setCellValue("检测段落：" + checkSection);
        cellF2.setCellStyle(dataStyle);
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 5, 8)); // 合并F到I列

        // 创建被合并的空单元格
        for (int i = 6; i <= 8; i++) {
            Cell cell = row2.createCell(i);
            cell.setCellStyle(dataStyle);
        }

        // 第四行：主表头
        Row row3 = sheet.createRow(3);
        row3.setHeight((short) 350);

        // 创建表头样式
        CellStyle headerStyle = sheet.getWorkbook().createCellStyle();
        headerStyle.cloneStyleFrom(dataStyle);
        Font headerFont = sheet.getWorkbook().createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);

        // A列：桩号（合并ABC列，并且跨第4、5行）
        Cell cellA3 = row3.createCell(0);
        cellA3.setCellValue("桩号");
        cellA3.setCellStyle(headerStyle);
        sheet.addMergedRegion(new CellRangeAddress(3, 4, 0, 2)); // 合并第4、5行的A到C列

        // 创建被合并的空单元格
        Cell cellB3 = row3.createCell(1);
        cellB3.setCellStyle(headerStyle);
        Cell cellC3 = row3.createCell(2);
        cellC3.setCellStyle(headerStyle);

        // D到E列：上行行车道
        Cell cellD3 = row3.createCell(3);
        cellD3.setCellValue("上行行车道");
        cellD3.setCellStyle(headerStyle);
        sheet.addMergedRegion(new CellRangeAddress(3, 3, 3, 4));
        Cell cellE3 = row3.createCell(4);
        cellE3.setCellStyle(headerStyle);

        // F到G列：下行行车道
        Cell cellF3 = row3.createCell(5);
        cellF3.setCellValue("下行行车道");
        cellF3.setCellStyle(headerStyle);
        sheet.addMergedRegion(new CellRangeAddress(3, 3, 5, 6));
        Cell cellG3 = row3.createCell(6);
        cellG3.setCellStyle(headerStyle);

        // H列：路面类型（跨第4、5行）
        Cell cellH3 = row3.createCell(7);
        cellH3.setCellValue("路面类型");
        cellH3.setCellStyle(headerStyle);
        sheet.addMergedRegion(new CellRangeAddress(3, 4, 7, 7)); // 合并第4、5行的H列

        // I列：备注（跨第4、5行）
        Cell cellI3 = row3.createCell(8);
        cellI3.setCellValue("备注");
        cellI3.setCellStyle(headerStyle);
        sheet.addMergedRegion(new CellRangeAddress(3, 4, 8, 8)); // 合并第4、5行的I列

        // 第五行：详细表头
        Row row4 = sheet.createRow(4);
        row4.setHeight((short) 350);

        // 详细表头 - 只需要设置D、E、F、G列，其他列已经合并
        // A、B、C列已经被第4行合并，不需要设置
        Cell cellA4 = row4.createCell(0);
        cellA4.setCellStyle(headerStyle);
        Cell cellB4 = row4.createCell(1);
        cellB4.setCellStyle(headerStyle);
        Cell cellC4 = row4.createCell(2);
        cellC4.setCellStyle(headerStyle);

        // D列：IRI
        Cell cellD4 = row4.createCell(3);
        cellD4.setCellValue("IRI");
        cellD4.setCellStyle(headerStyle);

        // E列：RQI
        Cell cellE4 = row4.createCell(4);
        cellE4.setCellValue("RQI");
        cellE4.setCellStyle(headerStyle);

        // F列：IRI
        Cell cellF4 = row4.createCell(5);
        cellF4.setCellValue("IRI");
        cellF4.setCellStyle(headerStyle);

        // G列：RQI
        Cell cellG4 = row4.createCell(6);
        cellG4.setCellValue("RQI");
        cellG4.setCellStyle(headerStyle);

        // H、I列已经被第4行合并，但需要创建单元格保持样式
        Cell cellH4 = row4.createCell(7);
        cellH4.setCellStyle(headerStyle);
        Cell cellI4 = row4.createCell(8);
        cellI4.setCellStyle(headerStyle);

        // 第五行开始填充数据
        int rowIndex = 5;
        for (RoadCheckRQI record : dataList) {
            Row row = sheet.createRow(rowIndex++);
            row.setHeight((short) 350); // 设置数据行高

            // A列：起始桩号
            Cell cellA = row.createCell(0);
            cellA.setCellValue(record.getStartCode() != null ? record.getStartCode() : "");
            cellA.setCellStyle(dataStyle);

            // B列：~
            Cell cellB = row.createCell(1);
            cellB.setCellValue("~");
            cellB.setCellStyle(dataStyle);

            // C列：结束桩号
            Cell cellC = row.createCell(2);
            cellC.setCellValue(record.getEndCode() != null ? record.getEndCode() : "");
            cellC.setCellStyle(dataStyle);

            // D列：左IRI
            Cell cellD = row.createCell(3);
            if (record.getLeftIri() != null) {
                cellD.setCellValue(record.getLeftIri().doubleValue());
            } else {
                cellD.setCellValue(0.0);
            }
            cellD.setCellStyle(numberStyle);

            // E列：右IRI
            Cell cellE = row.createCell(4);
            if (record.getRightIri() != null) {
                cellE.setCellValue(record.getRightIri().doubleValue());
            } else {
                cellE.setCellValue(0.0);
            }
            cellE.setCellStyle(numberStyle);

            // F列：代表IRI
            Cell cellF = row.createCell(5);
            if (record.getRepresentIri() != null) {
                cellF.setCellValue(record.getRepresentIri().doubleValue());
            } else {
                cellF.setCellValue(0.0);
            }
            cellF.setCellStyle(numberStyle);

            // G列：RQI或RQI评分
            Cell cellG = row.createCell(6);
            // 否则按原来的逻辑填充RQI值
            if (record.getRqi() != null) {
                cellG.setCellValue(record.getRqi().doubleValue());
            } else {
                cellG.setCellValue(0.0);
            }
            cellG.setCellStyle(numberStyle);

            // H列：路面类型或下行IRI平均值
            Cell cellH = row.createCell(7);
            // 否则按原来的逻辑填充路面类型
            cellH.setCellValue(record.getRoadType() != null ? record.getRoadType() : "沥青路面");
            cellH.setCellStyle(dataStyle);

            // I列：备注或计算值
            Cell cellI = row.createCell(8);
            // 否则按原来的逻辑填充备注
            String remark = record.getRemark();
            if (remark == null || remark.trim().isEmpty()) {
                // 根据direction字段判断
                if (record.getDirection() != null && record.getDirection() == 2) {
                    remark = "下行行车道";
                } else {
                    remark = "上行行车道";
                }
            }
            cellI.setCellValue(remark);
            cellI.setCellStyle(dataStyle);
        }
    }

    /**
     * 根据RQI值获取RQI等级评分
     */
    private String getRQILevel(double rqi) {
        if (rqi >= 90) {
            return "优";
        } else if (rqi >= 80) {
            return "良";
        } else if (rqi >= 70) {
            return "中";
        } else if (rqi >= 60) {
            return "次";
        } else {
            return "差";
        }
    }

    /**
     * 填充百米段汇总数据到模板Sheet
     */
    private void fillHundredSectionDataToTemplate(Sheet sheet, List<Map<String, Object>> dataList, String roadName,
                                                  String companyName, String startCode, String endCode) {
        SXSSFWorkbook workbook = (SXSSFWorkbook) sheet.getWorkbook();
        
        // 创建统一的数据样式（中文字体）
        CellStyle dataStyle = createCellStyleWithFont(workbook, true);

        // 创建数值样式（Times New Roman字体）
        CellStyle numberStyle = createCellStyleWithFont(workbook, false, "0.00");

        // 创建RQI专用样式（一位小数，Times New Roman字体）- 仅用于百米汇总
        CellStyle rqiStyle = createCellStyleWithFont(workbook, false, "0.0");

        // 第二行：项目名称和检测单位
        Row row1 = sheet.createRow(1);
        row1.setHeight((short) 400); // 设置行高

        // 合并ABCDE列，写入项目名称
        Cell cellA1 = row1.createCell(0);
        cellA1.setCellValue("项目名称：" + (roadName != null ? roadName : ""));
        cellA1.setCellStyle(dataStyle);
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, 4)); // 合并A到E列

        // 创建被合并的空单元格
        for (int i = 1; i <= 4; i++) {
            Cell cell = row1.createCell(i);
            cell.setCellStyle(dataStyle);
        }

        // 合并FGHI列，写入检测单位
        Cell cellF1 = row1.createCell(5);
        cellF1.setCellValue("检测单位：" + (companyName != null ? companyName : "湖北交投智能检测股份有限公司"));
        cellF1.setCellStyle(dataStyle);
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 5, 8)); // 合并F到I列

        // 创建被合并的空单元格
        for (int i = 6; i <= 8; i++) {
            Cell cell = row1.createCell(i);
            cell.setCellStyle(dataStyle);
        }

        // 第三行：检测项目和检测段落
        Row row2 = sheet.createRow(2);
        row2.setHeight((short) 400); // 设置行高

        // 合并ABCDE列，写入检测项目
        Cell cellA2 = row2.createCell(0);
        cellA2.setCellValue("检测项目：路面平整度");
        cellA2.setCellStyle(dataStyle);
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 0, 4)); // 合并A到E列

        // 创建被合并的空单元格
        for (int i = 1; i <= 4; i++) {
            Cell cell = row2.createCell(i);
            cell.setCellStyle(dataStyle);
        }

        // 合并FGHI列，写入检测段落
        Cell cellF2 = row2.createCell(5);
        String checkSection = (startCode != null ? startCode : "") + "～" + (endCode != null ? endCode : "");
        cellF2.setCellValue("检测段落：" + checkSection);
        cellF2.setCellStyle(dataStyle);
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 5, 8)); // 合并F到I列

        // 创建被合并的空单元格
        for (int i = 6; i <= 8; i++) {
            Cell cell = row2.createCell(i);
            cell.setCellStyle(dataStyle);
        }

        // 第四行：主表头
        Row row3 = sheet.createRow(3);
        row3.setHeight((short) 350);

        // 创建表头样式
        CellStyle headerStyle = sheet.getWorkbook().createCellStyle();
        headerStyle.cloneStyleFrom(dataStyle);
        Font headerFont = sheet.getWorkbook().createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);

        // A列：桩号（合并ABC列，并且跨第4、5行）
        Cell cellA3 = row3.createCell(0);
        cellA3.setCellValue("桩号");
        cellA3.setCellStyle(headerStyle);
        sheet.addMergedRegion(new CellRangeAddress(3, 4, 0, 2)); // 合并第4、5行的A到C列

        // 创建被合并的空单元格
        Cell cellB3 = row3.createCell(1);
        cellB3.setCellStyle(headerStyle);
        Cell cellC3 = row3.createCell(2);
        cellC3.setCellStyle(headerStyle);

        // D到E列：上行行车道
        Cell cellD3 = row3.createCell(3);
        cellD3.setCellValue("上行行车道");
        cellD3.setCellStyle(headerStyle);
        sheet.addMergedRegion(new CellRangeAddress(3, 3, 3, 4));
        Cell cellE3 = row3.createCell(4);
        cellE3.setCellStyle(headerStyle);

        // F到G列：下行行车道
        Cell cellF3 = row3.createCell(5);
        cellF3.setCellValue("下行行车道");
        cellF3.setCellStyle(headerStyle);
        sheet.addMergedRegion(new CellRangeAddress(3, 3, 5, 6));
        Cell cellG3 = row3.createCell(6);
        cellG3.setCellStyle(headerStyle);

        // H列：路面类型（跨第4、5行）
        Cell cellH3 = row3.createCell(7);
        cellH3.setCellValue("路面类型");
        cellH3.setCellStyle(headerStyle);
        sheet.addMergedRegion(new CellRangeAddress(3, 4, 7, 7)); // 合并第4、5行的H列

        // I列：备注（跨第4、5行）
        Cell cellI3 = row3.createCell(8);
        cellI3.setCellValue("备注");
        cellI3.setCellStyle(headerStyle);
        sheet.addMergedRegion(new CellRangeAddress(3, 4, 8, 8)); // 合并第4、5行的I列

        // 第五行：详细表头
        Row row4 = sheet.createRow(4);
        row4.setHeight((short) 350);

        // 详细表头 - 只需要设置D、E、F、G列，其他列已经合并
        // A、B、C列已经被第4行合并，不需要设置
        Cell cellA4 = row4.createCell(0);
        cellA4.setCellStyle(headerStyle);
        Cell cellB4 = row4.createCell(1);
        cellB4.setCellStyle(headerStyle);
        Cell cellC4 = row4.createCell(2);
        cellC4.setCellStyle(headerStyle);

        // D列：IRI
        Cell cellD4 = row4.createCell(3);
        cellD4.setCellValue("IRI");
        cellD4.setCellStyle(headerStyle);

        // E列：RQI
        Cell cellE4 = row4.createCell(4);
        cellE4.setCellValue("RQI");
        cellE4.setCellStyle(headerStyle);

        // F列：IRI
        Cell cellF4 = row4.createCell(5);
        cellF4.setCellValue("IRI");
        cellF4.setCellStyle(headerStyle);

        // G列：RQI
        Cell cellG4 = row4.createCell(6);
        cellG4.setCellValue("RQI");
        cellG4.setCellStyle(headerStyle);

        // H、I列已经被第4行合并，但需要创建单元格保持样式
        Cell cellH4 = row4.createCell(7);
        cellH4.setCellStyle(headerStyle);
        Cell cellI4 = row4.createCell(8);
        cellI4.setCellStyle(headerStyle);

        // 第五行开始填充数据
        int rowIndex = 5;
        for (Map<String, Object> record : dataList) {
            Row row = sheet.createRow(rowIndex++);
            row.setHeight((short) 350); // 设置数据行高

            // A列：起始桩号
            Cell cellA = row.createCell(0);
            String startCodeVal = getStringValue(record.get("start_code"));
            cellA.setCellValue(startCodeVal);
            cellA.setCellStyle(dataStyle);

            // B列：~
            Cell cellB = row.createCell(1);
            cellB.setCellValue("~");
            cellB.setCellStyle(dataStyle);

            // C列：结束桩号
            Cell cellC = row.createCell(2);
            String endCodeVal = getStringValue(record.get("end_code"));
            cellC.setCellValue(endCodeVal);
            cellC.setCellStyle(dataStyle);

            // D列：上行平均IRI
            Cell cellD = row.createCell(3);
            Object upAvgIri = record.get("up_avg_iri");
            if (upAvgIri != null) {
                cellD.setCellValue(Double.parseDouble(upAvgIri.toString()));
                cellD.setCellStyle(numberStyle);
            } else {
                cellD.setCellStyle(dataStyle);
            }

            // E列：上行平均RQI
            Cell cellE = row.createCell(4);
            Object upAvgRqi = record.get("up_avg_rqi");
            if (upAvgRqi != null) {
                cellE.setCellValue(Double.parseDouble(upAvgRqi.toString()));
                cellE.setCellStyle(rqiStyle); // 使用一位小数格式
            } else {
                cellE.setCellStyle(dataStyle);
            }

            // F列：下行平均IRI
            Cell cellF = row.createCell(5);
            Object downAvgIri = record.get("down_avg_iri");
            if (downAvgIri != null) {
                cellF.setCellValue(Double.parseDouble(downAvgIri.toString()));
                cellF.setCellStyle(numberStyle);
            } else {
                cellF.setCellStyle(dataStyle);
            }

            // G列：下行平均RQI
            Cell cellG = row.createCell(6);
            Object downAvgRqi = record.get("down_avg_rqi");
            if (downAvgRqi != null) {
                cellG.setCellValue(Double.parseDouble(downAvgRqi.toString()));
                cellG.setCellStyle(rqiStyle); // 使用一位小数格式
            } else {
                cellG.setCellStyle(dataStyle);
            }

            // H列：路面类型
            Cell cellH = row.createCell(7);
            cellH.setCellValue(getStringValue(record.get("pavement_type")));
            cellH.setCellStyle(dataStyle);

            // I列：备注
            Cell cellI = row.createCell(8);
            cellI.setCellValue(getStringValue(record.get("remark")));
            cellI.setCellStyle(dataStyle);
        }
    }

    /**
     * 安全获取字符串值
     */
    private String getStringValue(Object value) {
        return value != null ? value.toString() : "";
    }

    /**
     * 填充公里汇总数据到模板Sheet
     */
    private void fillThousandSectionDataToTemplate(Sheet sheet, List<Map<String, Object>> dataList, String roadName,
                                                   String companyName, String startCode, String endCode) {
        SXSSFWorkbook workbook = (SXSSFWorkbook) sheet.getWorkbook();
        // 中文文本样式：宋体
        CellStyle dataStyle = createCellStyleWithFont(workbook, true);
        // 数值样式：Times New Roman（两位小数）
        CellStyle numberStyle = createCellStyleWithFont(workbook, false, "0.00");
        // RQI专用数值样式：Times New Roman（一位小数）
        CellStyle rqiStyle = createCellStyleWithFont(workbook, false, "0.0");
        // 整数样式：Times New Roman（用于距离等整数列）
        CellStyle integerStyle = createCellStyleWithFont(workbook, false, "0");
        // 百分比样式：Times New Roman
        CellStyle percentStyle = createCellStyleWithFont(workbook, false, "0.00%");
 
        // 第一行：使用模板已有的合并区域，只设置内容
        Row row0 = sheet.getRow(0);
        if (row0 == null) {
            row0 = sheet.createRow(0);
        }
        row0.setHeight((short) 600); // 设置标题行高
 
        // 创建标题样式
        CellStyle titleStyle = createCellStyleWithFont(workbook, true);
        Font titleFont = workbook.createFont();
        titleFont.setFontHeightInPoints((short) 14);
        titleFont.setBold(true);
        titleFont.setFontName("宋体");
        titleStyle.setFont(titleFont);
 
        // 获取或创建第一行的第一个单元格并设置标题文本
        Cell titleCell = row0.getCell(0);
        if (titleCell == null) {
            titleCell = row0.createCell(0);
        }
        String titleText = (roadName != null ? roadName : "") + "路面平整度及行驶质量指数汇总表";
        titleCell.setCellValue(titleText);
        titleCell.setCellStyle(titleStyle);
 
        // 第二行：合并A-J列，整体靠右显示桩号信息
        Row row1 = sheet.getRow(1);
        if (row1 == null) {
            row1 = sheet.createRow(1);
        }
        row1.setHeight((short) 400); // 设置行高
 
        // 创建桩号信息样式（靠右显示）
        CellStyle stakeCodeStyle = createCellStyleWithFont(workbook, true);
        stakeCodeStyle.cloneStyleFrom(titleStyle);
        stakeCodeStyle.setAlignment(HorizontalAlignment.RIGHT); // 靠右显示
        stakeCodeStyle.setBorderTop(BorderStyle.THIN);
        stakeCodeStyle.setBorderRight(BorderStyle.THIN);
        stakeCodeStyle.setBorderBottom(BorderStyle.THIN);
        stakeCodeStyle.setBorderLeft(BorderStyle.THIN);
 
        // 创建第二行的第一个单元格并设置桩号信息
        Cell stakeCodeCell = row1.getCell(0);
        if (stakeCodeCell == null) {
            stakeCodeCell = row1.createCell(0);
        }
        String stakeCodeText = "桩号:" + (startCode != null ? startCode : "") + "~" + (endCode != null ? endCode : "");
        stakeCodeCell.setCellValue(stakeCodeText);
        stakeCodeCell.setCellStyle(stakeCodeStyle);
 
        // 合并第二行的A-J列
        try {
            sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, 9));
        } catch (IllegalStateException e) {
            // 如果合并区域已存在，则忽略
            log.debug("第二行合并区域已存在，跳过创建");
        }
 
        // 为其他被合并的单元格创建相同的样式
        for (int i = 1; i <= 9; i++) {
            Cell cell = row1.getCell(i);
            if (cell == null) {
                cell = row1.createCell(i);
            }
            cell.setCellStyle(stakeCodeStyle);
        }
 
        // 第3、4行：创建表头结构
        createThousandSectionTableHeader(sheet, dataStyle);
 
        // 从第5行开始填充数据（索引为4）
        int rowIndex = 4;
 
        // 用于计算合计数据的变量
        double totalDistanceSum = 0.0;
        double upIriSum = 0.0;
        double upRqiSum = 0.0;
        double downIriSum = 0.0;
        double downRqiSum = 0.0;
        int dataRowCount = 0;
 
        // 用于统计各等级的公里数
        double upExcellentDistance = 0.0;
        double upGoodDistance = 0.0;
        double upMediumDistance = 0.0;
        double upFairDistance = 0.0;
        double upPoorDistance = 0.0;
        double downExcellentDistance = 0.0;
        double downGoodDistance = 0.0;
        double downMediumDistance = 0.0;
        double downFairDistance = 0.0;
        double downPoorDistance = 0.0;
 
        for (Map<String, Object> record : dataList) {
            Row row = sheet.createRow(rowIndex++);
            row.setHeight((short) 350); // 设置数据行高
 
            // A列：起始桩号
            Cell cellA = row.createCell(0);
            String startCodeVal = getStringValue(record.get("start_code"));
            cellA.setCellValue(startCodeVal);
            cellA.setCellStyle(dataStyle);
 
            // B列：~
            Cell cellB = row.createCell(1);
            cellB.setCellValue("~");
            cellB.setCellStyle(dataStyle);
 
            // C列：结束桩号
            Cell cellC = row.createCell(2);
            String endCodeVal = getStringValue(record.get("end_code"));
            cellC.setCellValue(endCodeVal);
            cellC.setCellStyle(dataStyle);
 
            // 计算该段位的实际距离（米）
            double sectionDistance = calculateSectionDistanceFromCodes(startCodeVal, endCodeVal);
 
            // D列：距离（新增）
            Cell cellD = row.createCell(3);
            cellD.setCellValue((int)sectionDistance);
            cellD.setCellStyle(integerStyle);
 
            // E列：上行平均IRI（原D列）- 只保留2位小数，直接截断
            Cell cellE = row.createCell(4);
            Object upAvgIri = record.get("up_avg_iri");
            double upIriValue = 0.0;
            if (upAvgIri != null) {
                upIriValue = Double.parseDouble(upAvgIri.toString());
                // 只保留2位小数，直接截断（不四舍五入）
                double truncatedUpIri = Math.floor(upIriValue * 100) / 100.0;
                cellE.setCellValue(truncatedUpIri); // 设置截断后的2位小数值
                cellE.setCellStyle(numberStyle); // 显示格式为2位小数
            } else {
                cellE.setCellStyle(dataStyle);
            }
 
            // F列：上行平均RQI（原E列）- 使用Java代码计算，基于E列原始5位小数值
            Cell cellF = row.createCell(5);
            double upRqiValue = 0.0;
            if (upAvgIri != null) {
                // 使用E列原始的5位小数值进行RQI计算
                // 公式：=ROUND(100/(1+0.026*EXP(0.65*E列)),2)
                double originalUpIri = upIriValue; // 使用原始5位小数值
                double calculatedRqi = 100.0 / (1.0 + 0.026 * Math.exp(0.65 * originalUpIri));
                // 四舍五入到2位小数
                upRqiValue = Math.round(calculatedRqi * 100.0) / 100.0;
                cellF.setCellValue(upRqiValue);
                cellF.setCellStyle(numberStyle); // 使用2位小数格式
            } else {
                cellF.setCellStyle(dataStyle);
            }
 
            // G列：上行RQI评分（根据F列的值判断）
            Cell cellG = row.createCell(6);
            String upGrade = getRQIGradeByValue(upRqiValue);
            cellG.setCellValue(upGrade);
            cellG.setCellStyle(dataStyle);
 
            // H列：下行平均IRI - 只保留2位小数，直接截断
            Cell cellH = row.createCell(7);
            Object downAvgIri = record.get("down_avg_iri");
            double downIriValue = 0.0;
            if (downAvgIri != null) {
                downIriValue = Double.parseDouble(downAvgIri.toString());
                // 只保留2位小数，直接截断（不四舍五入）
                double truncatedDownIri = Math.floor(downIriValue * 100) / 100.0;
                cellH.setCellValue(truncatedDownIri); // 设置截断后的2位小数值
                cellH.setCellStyle(numberStyle); // 显示格式为2位小数
            } else {
                cellH.setCellStyle(dataStyle);
            }
 
            // I列：下行RQI - 使用Java代码计算，基于H列原始5位小数值
            Cell cellI = row.createCell(8);
            double roundedDownRqi = 0.0;
            if (downAvgIri != null) {
                // 使用H列原始的5位小数值进行RQI计算
                // 公式：=ROUND(100/(1+0.026*EXP(0.65*H列)),2)
                double originalDownIri = downIriValue; // 使用原始5位小数值
                double calculatedDownRqi = 100.0 / (1.0 + 0.026 * Math.exp(0.65 * originalDownIri));
                // 四舍五入到2位小数
                roundedDownRqi = Math.round(calculatedDownRqi * 100.0) / 100.0;
                cellI.setCellValue(roundedDownRqi);
                cellI.setCellStyle(numberStyle);
            } else {
                cellI.setCellStyle(numberStyle);
            }
 
            // J列：下行RQI评分（根据I列的值判断）
            Cell cellJ = row.createCell(9);
            String downGrade = getRQIGradeByValue(roundedDownRqi);
            cellJ.setCellValue(downGrade);
            cellJ.setCellStyle(dataStyle);
 
            // 累计合计数据
            totalDistanceSum += sectionDistance;
            upIriSum += upIriValue;
            upRqiSum += upRqiValue;
            downIriSum += downIriValue;
            downRqiSum += roundedDownRqi; // 使用计算后的下行RQI值
            dataRowCount++;
 
            // 根据RQI值统计各等级的公里数
            String upGradeForStats = getRQIGrade(upRqiValue);
            String downGradeForStats = getRQIGrade(roundedDownRqi);
 
            // 上行等级统计
            switch(upGradeForStats) {
                case "优": upExcellentDistance += sectionDistance; break;
                case "良": upGoodDistance += sectionDistance; break;
                case "中": upMediumDistance += sectionDistance; break;
                case "次": upFairDistance += sectionDistance; break;
                case "差": upPoorDistance += sectionDistance; break;
            }
 
            // 下行等级统计
            switch(downGradeForStats) {
                case "优": downExcellentDistance += sectionDistance; break;
                case "良": downGoodDistance += sectionDistance; break;
                case "中": downMediumDistance += sectionDistance; break;
                case "次": downFairDistance += sectionDistance; break;
                case "差": downPoorDistance += sectionDistance; break;
            }
        }
 
        // 添加合计行
        if (dataRowCount > 0) {
            Row summaryRow = sheet.createRow(rowIndex);
 
            // A,B,C列合并显示"合计"
            Cell summaryCell = summaryRow.createCell(0);
            summaryCell.setCellValue("合计");
            summaryCell.setCellStyle(dataStyle);
 
            // 合并A,B,C列
            sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, 2));
 
            // 创建空的B,C列单元格以保持格式一致
            Cell cellB = summaryRow.createCell(1);
            cellB.setCellStyle(dataStyle);
            Cell cellC = summaryRow.createCell(2);
            cellC.setCellStyle(dataStyle);
 
            // D列：总距离
            Cell cellD = summaryRow.createCell(3);
            cellD.setCellValue((int)totalDistanceSum);
            cellD.setCellStyle(integerStyle);
 
            // E列：上行IRI平均值 - 只保留2位小数，直接截断
            Cell cellE = summaryRow.createCell(4);
            double avgUpIri = upIriSum / dataRowCount;
            // 只保留2位小数，直接截断（不四舍五入）
            double truncatedAvgUpIri = Math.floor(avgUpIri * 100) / 100.0;
            cellE.setCellValue(truncatedAvgUpIri); // 设置截断后的2位小数值
            cellE.setCellStyle(numberStyle); // 显示格式为2位小数
 
            // F列：上行RQI平均值 - 使用Java代码计算，基于原始5位小数值
            Cell cellF = summaryRow.createCell(5);
            // 使用原始的5位小数值进行RQI计算
            // 公式：=ROUND(100/(1+0.026*EXP(0.65*E列)),2)
            double originalAvgUpIri = avgUpIri; // 使用原始5位小数值
            double calculatedUpRqiValue = 100.0 / (1.0 + 0.026 * Math.exp(0.65 * originalAvgUpIri));
            // 四舍五入到2位小数
            double calculatedUpRqi = Math.round(calculatedUpRqiValue * 100.0) / 100.0;
            cellF.setCellValue(calculatedUpRqi);
            cellF.setCellStyle(numberStyle);
 
            // G列：上行RQI评分
            Cell cellG = summaryRow.createCell(6);
            String upGradeForSummary = getRQIGradeByValue(calculatedUpRqi);
            cellG.setCellValue(upGradeForSummary);
            cellG.setCellStyle(dataStyle);
 
            // H列：下行IRI平均值 - 只保留2位小数，直接截断
            Cell cellH = summaryRow.createCell(7);
            double avgDownIri = downIriSum / dataRowCount;
            // 只保留2位小数，直接截断（不四舍五入）
            double truncatedAvgDownIri = Math.floor(avgDownIri * 100) / 100.0;
            cellH.setCellValue(truncatedAvgDownIri); // 设置截断后的2位小数值
            cellH.setCellStyle(numberStyle); // 显示格式为2位小数
 
            // I列：下行RQI计算值 - 使用Java代码计算，基于原始5位小数值
            Cell cellI = summaryRow.createCell(8);
            // 使用原始的5位小数值进行RQI计算
            // 公式：=ROUND(100/(1+0.026*EXP(0.65*H列)),2)
            double originalAvgDownIri = avgDownIri; // 使用原始5位小数值
            double calculatedDownRqiValue = 100.0 / (1.0 + 0.026 * Math.exp(0.65 * originalAvgDownIri));
            // 四舍五入到2位小数
            double calculatedDownRqi = Math.round(calculatedDownRqiValue * 100.0) / 100.0;
            cellI.setCellValue(calculatedDownRqi);
            cellI.setCellStyle(numberStyle);
 
            // J列：下行RQI评分
            Cell cellJ = summaryRow.createCell(9);
            String downGradeForSummary = getRQIGradeByValue(calculatedDownRqi);
            cellJ.setCellValue(downGradeForSummary);
            cellJ.setCellStyle(dataStyle);
 
            rowIndex++;
 
            // 记录合计距离信息
            log.info("RQI汇总表生成完成 - 总计实际距离: {}米 (约{:.2f}公里), 数据行数: {}",
                    (int)totalDistanceSum, totalDistanceSum/1000.0, dataRowCount);
 
            // 添加等级统计行
            addRQIGradeStatisticsRows(sheet, rowIndex, dataStyle, numberStyle, percentStyle,
                    totalDistanceSum, upExcellentDistance, upGoodDistance, upMediumDistance,
                    upFairDistance, upPoorDistance, downExcellentDistance, downGoodDistance,
                    downMediumDistance, downFairDistance, downPoorDistance);
        }
    }

    /**
     * 根据RQI值获取等级（RQI评级标准）
     */
    private String getRQIGrade(double rqi) {
        if (rqi >= 90) {
            return "优";
        } else if (rqi >= 75) {
            return "良";
        } else if (rqi >= 60) {
            return "中";
        } else if (rqi >= 40) {
            return "次";
        } else {
            return "差";
        }
    }

    /**
     * 根据RQI值获取等级（公里汇总专用评级标准）
     */
    private String getRQIGradeByValue(double rqi) {
        if (rqi >= 90) {
            return "优";
        } else if (rqi >= 80) {
            return "良";
        } else if (rqi >= 70) {
            return "中";
        } else if (rqi >= 60) {
            return "次";
        } else {
            return "差";
        }
    }

    /**
     * 创建公里汇总表的第3、4行表头结构
     */
    private void createThousandSectionTableHeader(Sheet sheet, CellStyle dataStyle) {
        // 创建表头样式
        CellStyle headerStyle = sheet.getWorkbook().createCellStyle();
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);

        Font headerFont = sheet.getWorkbook().createFont();
        headerFont.setBold(true);
        headerFont.setFontName("宋体");
        headerFont.setFontHeightInPoints((short) 11);
        headerStyle.setFont(headerFont);

        // 第3行：主表头
        Row row3 = sheet.getRow(2);
        if (row3 == null) {
            row3 = sheet.createRow(2);
        }
        row3.setHeight((short) 350);

        // A列：桩号（合并A3:C4）
        Cell cellA3 = row3.getCell(0);
        if (cellA3 == null) {
            cellA3 = row3.createCell(0);
        }
        cellA3.setCellValue("桩号");
        cellA3.setCellStyle(headerStyle);

        // B、C列在第3行为空（被A列合并）
        for (int i = 1; i <= 2; i++) {
            Cell cell = row3.getCell(i);
            if (cell == null) {
                cell = row3.createCell(i);
            }
            cell.setCellStyle(headerStyle);
        }

        // D列：里程（合并D3:D4）
        Cell cellD3 = row3.getCell(3);
        if (cellD3 == null) {
            cellD3 = row3.createCell(3);
        }
        cellD3.setCellValue("里程");
        cellD3.setCellStyle(headerStyle);

        // E、F、G列：上行行车道（合并E3:G3）
        Cell cellE3 = row3.getCell(4);
        if (cellE3 == null) {
            cellE3 = row3.createCell(4);
        }
        cellE3.setCellValue("上行行车道");
        cellE3.setCellStyle(headerStyle);

        Cell cellF3 = row3.getCell(5);
        if (cellF3 == null) {
            cellF3 = row3.createCell(5);
        }
        cellF3.setCellStyle(headerStyle);

        Cell cellG3 = row3.getCell(6);
        if (cellG3 == null) {
            cellG3 = row3.createCell(6);
        }
        cellG3.setCellStyle(headerStyle);

        // H、I、J列：下行行车道（合并H3:J3）
        Cell cellH3 = row3.getCell(7);
        if (cellH3 == null) {
            cellH3 = row3.createCell(7);
        }
        cellH3.setCellValue("下行行车道");
        cellH3.setCellStyle(headerStyle);

        Cell cellI3 = row3.getCell(8);
        if (cellI3 == null) {
            cellI3 = row3.createCell(8);
        }
        cellI3.setCellStyle(headerStyle);

        Cell cellJ3 = row3.getCell(9);
        if (cellJ3 == null) {
            cellJ3 = row3.createCell(9);
        }
        cellJ3.setCellStyle(headerStyle);

        // 第4行：详细表头
        Row row4 = sheet.getRow(3);
        if (row4 == null) {
            row4 = sheet.createRow(3);
        }
        row4.setHeight((short) 700); // 设置较高的行高以容纳多行文字

        // A、B、C列已经被第3行合并，创建空单元格
        for (int i = 0; i <= 2; i++) {
            Cell cell = row4.getCell(i);
            if (cell == null) {
                cell = row4.createCell(i);
            }
            cell.setCellStyle(headerStyle);
        }

        // D列已经被第3行合并
        Cell cellD4 = row4.getCell(3);
        if (cellD4 == null) {
            cellD4 = row4.createCell(3);
        }
        cellD4.setCellStyle(headerStyle);

        // E列：平整度(IRI)
        Cell cellE4 = row4.getCell(4);
        if (cellE4 == null) {
            cellE4 = row4.createCell(4);
        }
        cellE4.setCellValue("平整度(IRI)");
        cellE4.setCellStyle(headerStyle);

        // F列：路面行驶质量指数(RQI)
        Cell cellF4 = row4.getCell(5);
        if (cellF4 == null) {
            cellF4 = row4.createCell(5);
        }
        cellF4.setCellValue("路面行驶质量指数(RQI)");
        cellF4.setCellStyle(headerStyle);

        // G列：行驶质量等级
        Cell cellG4 = row4.getCell(6);
        if (cellG4 == null) {
            cellG4 = row4.createCell(6);
        }
        cellG4.setCellValue("行驶质量等级");
        cellG4.setCellStyle(headerStyle);

        // H列：平整度(IRI)
        Cell cellH4 = row4.getCell(7);
        if (cellH4 == null) {
            cellH4 = row4.createCell(7);
        }
        cellH4.setCellValue("平整度(IRI)");
        cellH4.setCellStyle(headerStyle);

        // I列：路面行驶质量指数(RQI)
        Cell cellI4 = row4.getCell(8);
        if (cellI4 == null) {
            cellI4 = row4.createCell(8);
        }
        cellI4.setCellValue("路面行驶质量指数(RQI)");
        cellI4.setCellStyle(headerStyle);

        // J列：行驶质量等级
        Cell cellJ4 = row4.getCell(9);
        if (cellJ4 == null) {
            cellJ4 = row4.createCell(9);
        }
        cellJ4.setCellValue("行驶质量等级");
        cellJ4.setCellStyle(headerStyle);

        // 添加合并区域（使用try-catch避免重复合并的异常）
        try {
            // A3:C4 - 桩号
            sheet.addMergedRegion(new CellRangeAddress(2, 3, 0, 2));
            // D3:D4 - 里程
            sheet.addMergedRegion(new CellRangeAddress(2, 3, 3, 3));
            // E3:G3 - 上行行车道
            sheet.addMergedRegion(new CellRangeAddress(2, 2, 4, 6));
            // H3:J3 - 下行行车道
            sheet.addMergedRegion(new CellRangeAddress(2, 2, 7, 9));
        } catch (IllegalStateException e) {
            log.debug("表头合并区域已存在，跳过创建: {}", e.getMessage());
        }
    }

    /**
     * 计算段位距离（从桩号计算）
     */
    private double calculateSectionDistanceFromCodes(String startCode, String endCode) {
        try {
            // 使用 StakeCodeUtil 中的静态方法
            long startMeters = StakeCodeUtil.parseStakeCodeToMeters(startCode);
            long endMeters = StakeCodeUtil.parseStakeCodeToMeters(endCode);

            if (startMeters >= 0 && endMeters >= 0 && endMeters > startMeters) {
                return endMeters - startMeters;
            }
        } catch (Exception e) {
            log.warn("计算段位距离失败，起始桩号: {}, 结束桩号: {}, 错误: {}", startCode, endCode, e.getMessage());
        }

        return 1000.0; // 默认1公里
    }

    /**
     * 将桩号转换为总米数
     */


    /**
     * 添加等级统计行
     */
    private void addRQIGradeStatisticsRows(Sheet sheet, int startRow, CellStyle dataStyle, CellStyle numberStyle, CellStyle percentStyle,
                                           double totalDistance, double upExcellent, double upGood,
                                           double upMedium, double upFair, double upPoor,
                                           double downExcellent, double downGood, double downMedium,
                                           double downFair, double downPoor) {

        // 添加"优"等级统计行
        addRQIGradeRow(sheet, startRow, "优 (%)", dataStyle, numberStyle, percentStyle,
                totalDistance, upExcellent, downExcellent);

        // 添加"良"等级统计行
        addRQIGradeRow(sheet, startRow + 1, "良 (%)", dataStyle, numberStyle, percentStyle,
                totalDistance, upGood, downGood);

        // 添加"中"等级统计行
        addRQIGradeRow(sheet, startRow + 2, "中 (%)", dataStyle, numberStyle, percentStyle,
                totalDistance, upMedium, downMedium);

        // 添加"次"等级统计行
        addRQIGradeRow(sheet, startRow + 3, "次 (%)", dataStyle, numberStyle, percentStyle,
                totalDistance, upFair, downFair);

        // 添加"差"等级统计行
        addRQIGradeRow(sheet, startRow + 4, "差 (%)", dataStyle, numberStyle, percentStyle,
                totalDistance, upPoor, downPoor);
    }

    /**
     * 添加单个等级统计行
     */
    private void addRQIGradeRow(Sheet sheet, int rowIndex, String gradeName, CellStyle dataStyle,
                                CellStyle numberStyle, CellStyle percentStyle, double totalDistance,
                                double upDistance, double downDistance) {
        Row row = sheet.createRow(rowIndex);

        // A,B,C列合并显示等级名称
        Cell cellA = row.createCell(0);
        cellA.setCellValue(gradeName);
        cellA.setCellStyle(dataStyle);

        // 合并A,B,C列
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, 2));

        // 创建空的B,C列单元格以保持格式一致
        Cell cellB = row.createCell(1);
        cellB.setCellStyle(dataStyle);
        Cell cellC = row.createCell(2);
        cellC.setCellStyle(dataStyle);

        // D列：空
        Cell cellD = row.createCell(3);
        cellD.setCellStyle(dataStyle);

        // E列：上行该等级的公里数
        Cell cellE = row.createCell(4);
        cellE.setCellValue(upDistance);
        cellE.setCellStyle(numberStyle);

        // F列：上行该等级占比，合并F、G列
        Cell cellF = row.createCell(5);
        double upPercentage = (totalDistance > 0) ? (upDistance / totalDistance) : 0;
        cellF.setCellValue(upPercentage);
        cellF.setCellStyle(percentStyle);

        // 合并F、G列用于上行占比
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 5, 6));

        // G列：创建空单元格以保持格式
        Cell cellG = row.createCell(6);
        cellG.setCellStyle(percentStyle);

        // H列：下行该等级的公里数
        Cell cellH = row.createCell(7);
        cellH.setCellValue(downDistance);
        cellH.setCellStyle(numberStyle);

        // I列：下行该等级占比，合并I、J列
        Cell cellI = row.createCell(8);
        double downPercentage = (totalDistance > 0) ? (downDistance / totalDistance) : 0;
        cellI.setCellValue(downPercentage);
        cellI.setCellStyle(percentStyle);

        // 合并I、J列用于下行占比
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 8, 9));

        // J列：创建空单元格以保持格式
        Cell cellJ = row.createCell(9);
        cellJ.setCellStyle(percentStyle);
    }

    /**
     * 查找RQI汇总表格
     * 通过扫描所有表格，找到只有3行的表格（2行表头+1行空行）
     */
    private XWPFTable findTableOnPage(XWPFDocument document,int tableIndex) {
        try {
            List<XWPFTable> tables = document.getTables();
            if (tables == null || tables.isEmpty()) {
                log.warn("文档中没有找到任何表格");
                return null;
            }
            log.warn("未找到只有3行的表格，使用第一个表格作为备选");
            return tables.get(tableIndex-1);
        } catch (Exception e) {
            log.error("查找RQI汇总表格时出错: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 查找最后一页的表格
     */
    private XWPFTable findTableOnLastPage(XWPFDocument document) {
        try {
            List<XWPFTable> tables = document.getTables();
            if (tables == null || tables.isEmpty()) {
                return null;
            }

            // 返回最后一个表格
            return tables.get(tables.size() - 1);
        } catch (Exception e) {
            log.warn("查找最后一页表格时出错: {}", e.getMessage());
            return null;
        }
    }

    @Override
    public int deleteRoadRQIByRoadId(Long roadId) {
        return roadCheckRQIMapper.deleteRoadRQIByRoadId(roadId);
    }

    /**
     * 计算RQI Word文档的页数（返回详细信息）
     * @return int数组 [总页数, pageOne, pageTwo]
     */
    private int[] calculateRQIDocumentPagesWithDetails(List<Map<String, Object>> summaryData, List<RoadCheckRQI> upData, 
                                                      List<RoadCheckRQI> downData, String direction) {
        try {
            // 基础页数
            int basePages = 0;
            
            // 计算汇总表格页数
            int summaryRows = summaryData != null ? summaryData.size() : 0;
            int summaryPages = calculateTablePages(summaryRows + 6, 36, 38); // +6是固定的合计行和等级行
            
            // 根据方向确定当前数据行数
            List<RoadCheckRQI> currentDirectionData = "上行".equals(direction) ? upData : downData;
            int dataRows = currentDirectionData != null ? currentDirectionData.size() : 0;
            
            // 计算数据表格页数
            int dataPages = calculateTablePages(dataRows, 41, 42);
            
            // 计算 pageOne = 汇总表格的总页码 + 6
            int pageOne = summaryPages + basePages;
            
            // 计算 pageTwo = pageOne + 1
            int pageTwo = pageOne + 1;
            
            // 总页数计算
            int totalPages = basePages + summaryPages + dataPages;
            
            log.info("RQI页数计算详情：基础页数={}, 汇总行数={}, 汇总页数={}, {}数据行数={}, 数据页数={}, 总页数={}, pageOne={}, pageTwo={}", 
                     basePages, summaryRows, summaryPages, direction, dataRows, dataPages, totalPages, pageOne, pageTwo);
            
            return new int[]{totalPages, pageOne, pageTwo};
            
        } catch (Exception e) {
            log.warn("计算RQI文档页数失败，使用默认值: {}", e.getMessage());
            return new int[]{10, 13, 14}; // 默认返回页数
        }
    }

    /**
     * 计算RQI Word文档的页数（保持向后兼容性）
     * 考虑第一页的特殊行数限制：
     * - 汇总表格第一页只有38行数据，后续页41行
     * - 上/下行表格第一页只有40行数据，后续页42行
     * 
     * @param summaryData 汇总数据
     * @param upData 上行数据
     * @param downData 下行数据
     * @param direction 当前方向
     * @return 文档页数
     */
    private int calculateRQIDocumentPages(List<Map<String, Object>> summaryData, List<RoadCheckRQI> upData, 
                                         List<RoadCheckRQI> downData, String direction) {
        int[] calculations = calculateRQIDocumentPagesWithDetails(summaryData, upData, downData, direction);
        return calculations[0]; // 返回总页数
    }

    /**
     * 计算表格页数，考虑第一页和后续页的不同行数限制
     * 
     * @param totalRows 总行数
     * @param firstPageRows 第一页可容纳的行数
     * @param otherPageRows 后续页可容纳的行数
     * @return 所需页数
     */
    private int calculateTablePages(int totalRows, int firstPageRows, int otherPageRows) {
        if (totalRows <= 0) {
            return 0;
        }
        
        if (totalRows <= firstPageRows) {
            // 第一页就能容纳所有数据
            return 1;
        } else {
            // 需要多页
            int remainingRows = totalRows - firstPageRows; // 除第一页外的剩余行数
            int additionalPages = (int) Math.ceil((double) remainingRows / otherPageRows); // 额外需要的页数
            return 1 + additionalPages; // 第一页 + 额外页数
        }
    }

    /**
     * 创建带字体的单元格样式
     * @param workbook 工作簿
     * @param isChinese 是否为中文字体（宋体），false为数字字母字体（新罗马）
     * @param dataFormat 数据格式，如"0.000"、"0.00"等
     * @return 单元格样式
     */
    private CellStyle createCellStyleWithFont(SXSSFWorkbook workbook, boolean isChinese, String dataFormat) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        
        // 创建字体
        Font font = workbook.createFont();
        if (isChinese) {
            // 中文字体：宋体
            font.setFontName("宋体");
        } else {
            // 数字字母字体：Times New Roman（新罗马）
            font.setFontName("Times New Roman");
        }
        font.setFontHeightInPoints((short) 10);
        style.setFont(font);
        
        // 设置数据格式
        if (dataFormat != null && !dataFormat.isEmpty()) {
            DataFormat format = workbook.createDataFormat();
            style.setDataFormat(format.getFormat(dataFormat));
        }
        
        return style;
    }

    /**
     * 创建带字体的单元格样式（无数据格式）
     * @param workbook 工作簿
     * @param isChinese 是否为中文字体（宋体），false为数字字母字体（新罗马）
     * @return 单元格样式
     */
    private CellStyle createCellStyleWithFont(SXSSFWorkbook workbook, boolean isChinese) {
        return createCellStyleWithFont(workbook, isChinese, null);
    }

    /**
     * 将文本按"字母/数字/常见符号"和"中文/其他"分段写入段落，分别设置字体。
     * - 字母/数字/符号（. % + - ( ) ~ / :）使用 Times New Roman
     * - 其他文字使用 宋体
     * 可选择性地继承字号/加粗/斜体/颜色
     */
    private void appendTextWithFonts(XWPFParagraph paragraph, String text, Integer inheritFontSize,
                                     Boolean inheritBold, Boolean inheritItalic, String inheritColor) {
        if (text == null) {
            return;
        }
        StringBuilder segment = new StringBuilder();
        Boolean currentLatin = null;
        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);
            boolean isLatin = isLatinDigitOrSymbol(c);
            if (currentLatin == null) {
                currentLatin = isLatin;
            }
            if (isLatin != currentLatin) {
                createRunWithFont(paragraph, segment.toString(), currentLatin,
                        inheritFontSize, inheritBold, inheritItalic, inheritColor);
                segment.setLength(0);
                currentLatin = isLatin;
            }
            segment.append(c);
        }
        createRunWithFont(paragraph, segment.toString(), currentLatin != null ? currentLatin : false,
                inheritFontSize, inheritBold, inheritItalic, inheritColor);
    }

    private boolean isLatinDigitOrSymbol(char c) {
        if (Character.isDigit(c)) return true;
        if ((c >= 'A' && c <= 'Z') || (c >= 'a' && c <= 'z')) return true;
        switch (c) {
            case '.':
            case '%':
            case '+':
            case '-':
            case '(':
            case ')':
            case '~':
            case '/':
            case ':':
                return true;
            default:
                return false;
        }
    }

    private void createRunWithFont(XWPFParagraph paragraph, String content, boolean latin,
                                   Integer inheritFontSize, Boolean inheritBold, Boolean inheritItalic, String inheritColor) {
        if (content == null || content.isEmpty()) {
            return;
        }
        XWPFRun run = paragraph.createRun();
        run.setText(content);
        run.setFontFamily(latin ? "Times New Roman" : "宋体");
        if (inheritFontSize != null && inheritFontSize > 0) {
            run.setFontSize(inheritFontSize);
        } else {
            run.setFontSize(10); // 默认10号
        }
        if (inheritBold != null) run.setBold(inheritBold);
        if (inheritItalic != null) run.setItalic(inheritItalic);
        if (inheritColor != null && !inheritColor.isEmpty()) run.setColor(inheritColor);
    }
} 