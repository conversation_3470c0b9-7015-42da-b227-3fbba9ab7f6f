package com.tunnel.service;

import com.tunnel.domain.CheckTeam;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import org.apache.poi.xwpf.usermodel.XWPFTable;

/**
 * 检测分组Service接口
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
public interface CheckTeamService {
    /**
     * 查询检测分组
     *
     * @param id 检测分组主键
     * @return 检测分组
     */
    public CheckTeam selectCheckTeamById(Long id);

    /**
     * 查询检测分组列表
     *
     * @param checkTeam 检测分组
     * @return 检测分组集合
     */
    public List<CheckTeam> selectCheckTeamList(CheckTeam checkTeam);

    /**
     * 新增检测分组
     *
     * @param checkTeam 检测分组
     * @return 结果
     */
    public int insertCheckTeam(CheckTeam checkTeam);

    /**
     * 修改检测分组
     *
     * @param checkTeam 检测分组
     * @return 结果
     */
    public int updateCheckTeam(CheckTeam checkTeam);

    /**
     * 批量删除检测分组
     *
     * @param ids 需要删除的检测分组主键集合
     * @return 结果
     */
    public int deleteCheckTeamByIds(Long[] ids);

    /**
     * 删除检测分组信息
     *
     * @param id 检测分组主键
     * @return 结果
     */
    public int deleteCheckTeamById(Long id);

    /**
     * 校验分组名称是否唯一
     *
     * @param checkTeam 检测分组信息
     * @return 结果
     */
    public boolean checkTeamNameUnique(CheckTeam checkTeam);


    /**
     * 导出检测分组信息列表
     *
     * @param response HTTP响应
     * @param checkTeam 查询条件
     */
    public void exportCheckTeam(HttpServletResponse response, CheckTeam checkTeam);

    /**
     * 查询活跃的检测分组列表
     *
     * @param type 路线类型
     * @return 活跃的检测分组集合
     */
    public List<CheckTeam> selectActiveCheckTeamList(Integer type);

    /**
     * 修改检测分组状态
     *
     * @param checkTeam 检测分组
     * @return 结果
     */
    public int updateCheckTeamStatus(CheckTeam checkTeam);

    /**
     * 填充检测人员信息到Word表格
     *
     * @param table Word表格
     * @param teamId 检测分组ID
     */
    public void fillCheckTeamUserTable(XWPFTable table, Long teamId);

}