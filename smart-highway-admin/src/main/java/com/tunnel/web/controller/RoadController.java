package com.tunnel.web.controller;

import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.domain.Road;
import com.tunnel.service.RoadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 基础项目路线信息Controller
 * 
 * <AUTHOR>
 * @date 2024-12-02
 */
@RestController
@RequestMapping("/smart/road")
@Api(tags = "基础项目路线管理")
public class RoadController extends BaseController {
    @Autowired
    private RoadService roadService;

    /**
     * 查询基础项目路线信息列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "获取基础项目路线信息列表", notes = "获取全部基础项目路线信息数据")
    @Operation(summary = "获取基础项目路线信息列表", description = "获取全部基础项目路线信息数据")
    public TableDataInfo list(Road road) {
        startPage();
        List<Road> list = roadService.selectRoadList(road);
        return getDataTable(list);
    }

    /**
     * 导出基础项目路线信息列表
     */
    @Log(title = "基础项目路线信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出基础项目路线信息", notes = "导出基础项目路线信息到Excel")
    @Operation(summary = "导出基础项目路线信息", description = "导出基础项目路线信息到Excel")
    public void export(HttpServletResponse response, Road road) {
        roadService.exportRoad(response, road);
    }

    /**
     * 获取基础项目路线信息详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取基础项目路线信息详细", notes = "根据ID获取基础项目路线信息详情")
    @Operation(summary = "获取基础项目路线信息详细", description = "根据ID获取基础项目路线信息详情")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true, dataType = "Long", paramType = "path")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(roadService.selectRoadById(id));
    }

    /**
     * 新增基础项目路线信息
     */
    @Log(title = "基础项目路线信息", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增基础项目路线信息", notes = "新增单条基础项目路线信息")
    @Operation(summary = "新增基础项目路线信息", description = "新增单条基础项目路线信息")
    public AjaxResult add(@Validated @RequestBody Road road) {
        return toAjax(roadService.insertRoad(road));
    }

    /**
     * 修改基础项目路线信息
     */
    @Log(title = "基础项目路线信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改基础项目路线信息", notes = "修改基础项目路线信息")
    @Operation(summary = "修改基础项目路线信息", description = "修改基础项目路线信息")
    public AjaxResult edit(@Validated @RequestBody Road road) {
        return toAjax(roadService.updateRoad(road));
    }

    /**
     * 删除基础项目路线信息
     */
    @Log(title = "基础项目路线信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除基础项目路线信息", notes = "批量删除基础项目路线信息")
    @Operation(summary = "删除基础项目路线信息", description = "批量删除基础项目路线信息")
    @ApiImplicitParam(name = "ids", value = "主键ID数组", required = true, dataType = "Long[]", paramType = "path")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(roadService.deleteRoadByIds(ids));
    }

    /**
     * 根据路线编号获取路线信息
     */
    @GetMapping("/getByCode/{roadCode}")
    @ApiOperation(value = "根据路线编号获取路线信息", notes = "根据路线编号获取路线信息")
    @Operation(summary = "根据路线编号获取路线信息", description = "根据路线编号获取路线信息")
    @ApiImplicitParam(name = "roadCode", value = "路线编号", required = true, dataType = "String", paramType = "path")
    public AjaxResult getByCode(@PathVariable("roadCode") String roadCode) {
        return success(roadService.selectRoadByCode(roadCode));
    }

    /**
     * 根据用户ID获取路线信息列表
     */
    @GetMapping("/listByUserId/{userId}")
    @ApiOperation(value = "根据用户ID获取路线信息列表", notes = "根据用户ID获取路线信息列表")
    @Operation(summary = "根据用户ID获取路线信息列表", description = "根据用户ID获取路线信息列表")
    @ApiImplicitParam(name = "userId", value = "用户ID", required = true, dataType = "Long", paramType = "path")
    public AjaxResult getListByUserId(@PathVariable("userId") Long userId) {
        List<Road> list = roadService.selectRoadListByUserId(userId);
        return success(list);
    }

    /**
     * 根据部门ID获取路线信息列表
     */
    @GetMapping("/listByDeptId/{deptId}")
    @ApiOperation(value = "根据部门ID获取路线信息列表", notes = "根据部门ID获取路线信息列表")
    @Operation(summary = "根据部门ID获取路线信息列表", description = "根据部门ID获取路线信息列表")
    @ApiImplicitParam(name = "deptId", value = "部门ID", required = true, dataType = "Long", paramType = "path")
    public AjaxResult getListByDeptId(@PathVariable("deptId") Long deptId) {
        List<Road> list = roadService.selectRoadListByDeptId(deptId);
        return success(list);
    }

    /**
     * 校验路线编号
     */
    @PostMapping("/checkRoadCodeUnique")
    @ApiOperation(value = "校验路线编号", notes = "校验路线编号是否唯一")
    @Operation(summary = "校验路线编号", description = "校验路线编号是否唯一")
    public AjaxResult checkRoadCodeUnique(@RequestBody Road road) {
        return success(roadService.checkRoadCodeUnique(road));
    }

    /**
     * 批量导入路线数据
     */
    @Log(title = "基础项目路线信息", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @ApiOperation(value = "批量导入路线数据", notes = "从Excel导入路线数据")
    public AjaxResult importData(@RequestPart("file") MultipartFile file) {
        String message = roadService.importRoad(file);
        return success(message);
    }

    /**
     * 下载导入模板
     */
    @GetMapping("/importTemplate")
    @ApiOperation(value = "下载导入模板", notes = "下载路线数据导入模板")
    @Operation(summary = "下载导入模板", description = "下载路线数据导入模板")
    public void importTemplate(HttpServletResponse response) {
        roadService.importTemplate(response);
    }

    /**
     * 获取基础项目路线信息记录数
     */
    @GetMapping("/count")
    @ApiOperation(value = "获取基础项目路线信息记录数", notes = "获取基础项目路线信息记录总数")
    @Operation(summary = "获取基础项目路线信息记录数", description = "获取基础项目路线信息记录总数")
    public AjaxResult count(Road road) {
        int count = roadService.countRoad(road);
        return success(count);
    }
}