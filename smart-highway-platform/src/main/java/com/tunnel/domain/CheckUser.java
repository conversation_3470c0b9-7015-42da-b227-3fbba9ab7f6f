package com.tunnel.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * 检测人员对象 sc_check_user
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CheckUser extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 姓名 */
    @Excel(name = "姓名")
    @NotBlank(message = "姓名不能为空")
    @Size(max = 50, message = "姓名长度不能超过50个字符")
    private String userName;

    /** 职位 */
    @Excel(name = "职位")
    @NotBlank(message = "职位不能为空")
    @Size(max = 50, message = "职位长度不能超过50个字符")
    private String position;

    /** 证书编号 */
    @Excel(name = "证书编号")
    @Size(max = 100, message = "证书编号长度不能超过100个字符")
    private String certificateNo;

    /** 职称 */
    @Excel(name = "职称")
    @Size(max = 50, message = "职称长度不能超过50个字符")
    private String title;

    /** 签名图片路径 */
    @Excel(name = "签名图片路径")
    @Size(max = 255, message = "签名图片路径长度不能超过255个字符")
    private String signature;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @Size(max = 20, message = "联系电话长度不能超过20个字符")
    private String phone;

    /** 邮箱 */
    @Excel(name = "邮箱")
    @Email(message = "邮箱格式不正确")
    @Size(max = 100, message = "邮箱长度不能超过100个字符")
    private String email;

    /** 排序 */
    @Excel(name = "排序")
    private Integer sortNum;

    /** 路线类型:1.高速,2.国省道 */
    @Excel(name = "路线类型", readConverterExp = "1=高速,2=国省道")
    private Integer type;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 创建人 */
    private Long creator;

    /** 更新人 */
    private Long modifier;

    /** 分组名称 - 非数据库字段，用于前端展示 */
    private String teamName;
} 