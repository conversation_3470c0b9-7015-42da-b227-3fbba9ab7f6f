package com.tunnel.mapper;

import com.tunnel.domain.Road;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 基础项目路线信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Mapper
public interface RoadMapper {
    /**
     * 查询基础项目路线信息
     *
     * @param id 基础项目路线信息主键
     * @return 基础项目路线信息
     */
    public Road selectRoadById(Long id);

    /**
     * 查询基础项目路线信息列表
     *
     * @param road 基础项目路线信息
     * @return 基础项目路线信息集合
     */
    public List<Road> selectRoadList(Road road);

    /**
     * 新增基础项目路线信息
     *
     * @param road 基础项目路线信息
     * @return 结果
     */
    public int insertRoad(Road road);

    /**
     * 修改基础项目路线信息
     *
     * @param road 基础项目路线信息
     * @return 结果
     */
    public int updateRoad(Road road);

    /**
     * 删除基础项目路线信息
     *
     * @param id 基础项目路线信息主键
     * @return 结果
     */
    public int deleteRoadById(Long id);

    /**
     * 批量删除基础项目路线信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRoadByIds(Long[] ids);

    /**
     * 根据路线编号查询路线信息
     *
     * @param roadCode 路线编号
     * @return 路线信息
     */
    public Road selectRoadByCode(@Param("roadCode") String roadCode);

    /**
     * 根据用户ID查询路线信息列表
     *
     * @param userId 用户ID
     * @return 路线信息集合
     */
    public List<Road> selectRoadListByUserId(@Param("userId") Long userId);

    /**
     * 根据部门ID查询路线信息列表
     *
     * @param deptId 部门ID
     * @return 路线信息集合
     */
    public List<Road> selectRoadListByDeptId(@Param("deptId") Long deptId);

    /**
     * 检查路线编号是否唯一
     *
     * @param road 路线信息
     * @return 结果
     */
    public Road checkRoadCodeUnique(Road road);

    /**
     * 获取路线信息记录数
     *
     * @param road 查询条件
     * @return 记录数
     */
    public int countRoad(Road road);
} 