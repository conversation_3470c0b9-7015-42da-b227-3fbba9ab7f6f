package com.tunnel.service.impl;

import com.tunnel.common.exception.ServiceException;
import com.tunnel.common.utils.DateUtils;
import com.tunnel.common.utils.SecurityUtils;
import com.tunnel.common.utils.StringUtils;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.CheckTeam;
import com.tunnel.domain.CheckUser;
import com.tunnel.domain.CheckTeamUser;
import com.tunnel.mapper.CheckTeamMapper;
import com.tunnel.mapper.CheckUserMapper;
import com.tunnel.service.CheckTeamService;
import com.tunnel.service.CheckTeamUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge;

import javax.servlet.http.HttpServletResponse;
import java.math.BigInteger;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.LinkedHashMap;
import java.util.stream.Collectors;

/**
 * 检测分组Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
@Service
@Slf4j
public class CheckTeamServiceImpl implements CheckTeamService {
    @Autowired
    private CheckTeamMapper checkTeamMapper;

    @Autowired
    private CheckUserMapper checkUserMapper;

    @Autowired
    private CheckTeamUserService checkTeamUserService;

    /**
     * 查询检测分组
     *
     * @param id 检测分组主键
     * @return 检测分组
     */
    @Override
    public CheckTeam selectCheckTeamById(Long id) {
        CheckTeam checkTeam = checkTeamMapper.selectCheckTeamById(id);
        if (checkTeam != null) {
            // 查询分组下的人员列表
            checkTeam.setUserList(checkTeamUserService.selectCheckUsersByTeamId(id, null));
            // 设置人员数量
            if (checkTeam.getUserList() != null) {
                checkTeam.setUserCount(checkTeam.getUserList().size());
            }
        }
        return checkTeam;
    }

    /**
     * 查询检测分组列表
     *
     * @param checkTeam 检测分组
     * @return 检测分组
     */
    @Override
    public List<CheckTeam> selectCheckTeamList(CheckTeam checkTeam) {
        List<CheckTeam> list = checkTeamMapper.selectCheckTeamList(checkTeam);
        // 为每个分组设置人员数量
        for (CheckTeam team : list) {
            List<CheckUser> userList = checkTeamUserService.selectCheckUsersByTeamId(team.getId(), null);
            team.setUserCount(userList != null ? userList.size() : 0);
        }
        return list;
    }

    /**
     * 新增检测分组
     *
     * @param checkTeam 检测分组
     * @return 结果
     */
    @Override
    public int insertCheckTeam(CheckTeam checkTeam) {
        // 校验分组名称唯一性
        if (!checkTeamNameUnique(checkTeam)) {
            throw new ServiceException("新增分组'" + checkTeam.getTeamName() + "'失败，分组名称已存在");
        }
        
        checkTeam.setCreateBy(SecurityUtils.getUsername());
        checkTeam.setCreateTime(DateUtils.getNowDate());
        return checkTeamMapper.insertCheckTeam(checkTeam);
    }

    /**
     * 修改检测分组
     *
     * @param checkTeam 检测分组
     * @return 结果
     */
    @Override
    public int updateCheckTeam(CheckTeam checkTeam) {
        // 校验分组名称唯一性
        if (!checkTeamNameUnique(checkTeam)) {
            throw new ServiceException("修改分组'" + checkTeam.getTeamName() + "'失败，分组名称已存在");
        }
        
        checkTeam.setUpdateBy(SecurityUtils.getUsername());
        checkTeam.setUpdateTime(DateUtils.getNowDate());
        return checkTeamMapper.updateCheckTeam(checkTeam);
    }

    /**
     * 批量删除检测分组
     *
     * @param ids 需要删除的检测分组主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteCheckTeamByIds(Long[] ids) {
        // 删除分组时，同时删除分组下的所有人员
        for (Long id : ids) {
            checkUserMapper.deleteCheckUserByTeamId(id);
        }
        return checkTeamMapper.deleteCheckTeamByIds(ids);
    }

    /**
     * 删除检测分组信息
     *
     * @param id 检测分组主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteCheckTeamById(Long id) {
        // 删除分组时，同时删除分组下的所有人员
        checkUserMapper.deleteCheckUserByTeamId(id);
        return checkTeamMapper.deleteCheckTeamById(id);
    }


    /**
     * 校验分组名称是否唯一
     *
     * @param checkTeam 检测分组信息
     * @return 结果
     */
    @Override
    public boolean checkTeamNameUnique(CheckTeam checkTeam) {
        CheckTeam team = checkTeamMapper.checkTeamNameUnique(checkTeam);
        return team == null;
    }


    /**
     * 导出检测分组信息列表
     *
     * @param response HTTP响应
     * @param checkTeam 查询条件
     */
    @Override
    public void exportCheckTeam(HttpServletResponse response, CheckTeam checkTeam) {
        List<CheckTeam> list = selectCheckTeamList(checkTeam);
        ExcelUtil<CheckTeam> util = new ExcelUtil<CheckTeam>(CheckTeam.class);
        util.exportExcel(response, list, "检测分组数据");
    }

    /**
     * 查询活跃的检测分组列表
     *
     * @param type 路线类型
     * @return 活跃的检测分组集合
     */
    @Override
    public List<CheckTeam> selectActiveCheckTeamList(Integer type) {
        CheckTeam queryParam = new CheckTeam();
        queryParam.setType(type); // 设置路线类型
        return checkTeamMapper.selectCheckTeamList(queryParam);
    }

    /**
     * 修改检测分组状态
     *
     * @param checkTeam 检测分组
     * @return 结果
     */
    @Override
    public int updateCheckTeamStatus(CheckTeam checkTeam) {
        checkTeam.setUpdateBy(SecurityUtils.getUsername());
        checkTeam.setUpdateTime(DateUtils.getNowDate());
        return checkTeamMapper.updateCheckTeam(checkTeam);
    }

    /**
     * 填充检测人员信息到Word表格
     *
     * @param table Word表格
     * @param teamId 检测分组ID
     */
    @Override
    public void fillCheckTeamUserTable(XWPFTable table, Long teamId) {
        try {
            // 查询检测分组信息
            CheckTeam checkTeam = checkTeamMapper.selectCheckTeamById(teamId);
            if (checkTeam == null) {
                log.warn("未找到检测分组信息，teamId: {}", teamId);
                return;
            }

            // 查询该分组的检测人员，按职位排序
            List<CheckTeamUser> checkTeamUsers = checkTeamUserService.selectCheckTeamUserListByTeamId(teamId, null);
            if (checkTeamUsers == null || checkTeamUsers.isEmpty()) {
                log.warn("未找到检测分组的人员信息，teamId: {}", teamId);
                return;
            }

            // 按职位排序：项目负责人、检测工程师、质量负责人、技术负责人、检测员
            String[] positionOrder = {"项目负责人", "项目主要参加人员", "报告编写人", "报告审核人", "报告批准人"};
            checkTeamUsers.sort((a, b) -> {
                int indexA = java.util.Arrays.asList(positionOrder).indexOf(a.getUserPosition());
                int indexB = java.util.Arrays.asList(positionOrder).indexOf(b.getUserPosition());
                if (indexA == -1) indexA = positionOrder.length;
                if (indexB == -1) indexB = positionOrder.length;
                return Integer.compare(indexA, indexB);
            });

            // 设置表格边框
            setTableBorders(table);

            // 清空表格中除表头外的所有行（假设第一行是表头）
            while (table.getRows().size() > 1) {
                table.removeRow(1);
            }

            // 按职位分组处理
            Map<String, List<CheckTeamUser>> positionGroups = checkTeamUsers.stream()
                .collect(Collectors.groupingBy(CheckTeamUser::getUserPosition, LinkedHashMap::new, Collectors.toList()));

            // 填充数据行
            for (Map.Entry<String, List<CheckTeamUser>> entry : positionGroups.entrySet()) {
                String position = entry.getKey();
                List<CheckTeamUser> users = entry.getValue();
                
                // 为每个用户创建一行
                for (int i = 0; i < users.size(); i++) {
                    CheckTeamUser user = users.get(i);
                    XWPFTableRow dataRow = table.createRow();
                    
                    // 设置最小行高为1.2cm，内容多时可自动扩展
                    setRowHeight(dataRow, 1.2);
                    
                    // 只有同一职位的第一个人显示职位名称，其他人职位列为空（用于合并效果）
                    String displayPosition = (i == 0) ? position : "";
                    fillCheckUserRow(dataRow, user, displayPosition);
                }
                
                // 如果同一职位有多个人员，合并职位列
                if (users.size() > 1) {
                    int startRowIndex = table.getRows().size() - users.size();
                    int endRowIndex = table.getRows().size() - 1;
                    mergePositionCells(table, startRowIndex, endRowIndex);
                }
            }

            log.info("成功填充检测人员信息表格，分组: {}, 人员数: {}", checkTeam.getTeamName(), checkTeamUsers.size());
        } catch (Exception e) {
            log.error("填充检测人员信息表格失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 填充检测人员信息行（标准5列格式：职位、姓名、职业资格证书编号、职称、签字）
     */
    private void fillCheckUserRow(XWPFTableRow row, CheckTeamUser user, String position) {
        int colIndex = 0;
        
        // 第1列：职位
        safeSetCellText(row, colIndex++, position != null ? position : "");
        
        // 第2列：姓名
        safeSetCellText(row, colIndex++, user.getUserName() != null ? user.getUserName() : "");
        
        // 第3列：职业资格证书编号
        safeSetCellText(row, colIndex++, user.getUserCertificateNo() != null ? user.getUserCertificateNo() : "");
        
        // 第4列：职称
        safeSetCellText(row, colIndex++, user.getUserTitle() != null ? user.getUserTitle() : "");
        
        // 第5列：签字（留空）
        safeSetCellText(row, colIndex, "");
    }

    /**
     * 合并职位列的单元格（用于同一职位多个人员的情况）
     */
    private void mergePositionCells(XWPFTable table, int startRowIndex, int endRowIndex) {
        try {
            if (startRowIndex >= endRowIndex || table.getRows().size() <= endRowIndex) {
                return;
            }

            // 获取第一行的职位单元格，设置合并开始
            XWPFTableRow firstRow = table.getRow(startRowIndex);
            if (firstRow.getTableCells().size() > 0) {
                XWPFTableCell firstCell = firstRow.getCell(0);
                
                // 设置第一个单元格为合并开始
                if (firstCell.getCTTc().getTcPr() == null) {
                    firstCell.getCTTc().addNewTcPr();
                }
                if (firstCell.getCTTc().getTcPr().getVMerge() == null) {
                    firstCell.getCTTc().getTcPr().addNewVMerge().setVal(STMerge.RESTART);
                }

                // 设置后续行的职位单元格为合并继续
                for (int i = startRowIndex + 1; i <= endRowIndex; i++) {
                    XWPFTableRow row = table.getRow(i);
                    if (row.getTableCells().size() > 0) {
                        XWPFTableCell cell = row.getCell(0);
                        
                        if (cell.getCTTc().getTcPr() == null) {
                            cell.getCTTc().addNewTcPr();
                        }
                        if (cell.getCTTc().getTcPr().getVMerge() == null) {
                            cell.getCTTc().getTcPr().addNewVMerge().setVal(STMerge.CONTINUE);
                        }
                        
                        // 清空合并单元格的内容
                        cell.getParagraphs().clear();
                        cell.addParagraph();
                    }
                }
            }

            log.debug("成功合并职位列，行范围: {} - {}", startRowIndex + 1, endRowIndex + 1);
        } catch (Exception e) {
            log.warn("合并职位列失败，行范围: {} - {}, 错误: {}", startRowIndex + 1, endRowIndex + 1, e.getMessage());
        }
    }

    /**
     * 安全设置表格单元格文本
     */
    private void safeSetCellText(XWPFTableRow row, int cellIndex, String text) {
        try {
            // 确保有足够的单元格
            while (row.getTableCells().size() <= cellIndex) {
                row.createCell();
            }

            XWPFTableCell cell = row.getCell(cellIndex);
            if (cell != null) {
                // 设置单元格边框
                setCellBorders(cell);

                // 获取第一个段落，如果不存在则创建
                XWPFParagraph paragraph = cell.getParagraphs().get(0);

                // 清空段落中的所有run
                for (int i = paragraph.getRuns().size() - 1; i >= 0; i--) {
                    paragraph.removeRun(i);
                }

                paragraph.setAlignment(ParagraphAlignment.CENTER);

                XWPFRun run = paragraph.createRun();
                run.setText(text != null ? text : "");
                run.setFontFamily("宋体"); // 设置为宋体

                // 使用方法三：通过CTRPr精确设置10.5磅字体
                setFontSizeHalfPoint(run, 10.5);

                // 设置单元格垂直居中对齐
                cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            }
        } catch (Exception e) {
            log.warn("设置单元格文本失败 [行:{}, 列:{}]: {}",
                    row != null ? row.getTableCells().size() : "null", cellIndex, e.getMessage());
        }
    }

    /**
     * 精确设置字体大小（支持小数点）
     * @param run XWPFRun对象
     * @param fontSize 字体大小（磅）
     */
    private void setFontSizeHalfPoint(XWPFRun run, double fontSize) {
        // 在Word中，字体大小以半磅为单位存储，所以10.5磅 = 21个半磅单位
        int halfPoints = (int) (fontSize * 2);

        if (run.getCTR() != null) {
            if (run.getCTR().getRPr() == null) {
                run.getCTR().addNewRPr();
            }

            // 设置字体大小（半磅单位）
            if (run.getCTR().getRPr().getSz() == null) {
                run.getCTR().getRPr().addNewSz();
            }
            run.getCTR().getRPr().getSz().setVal(BigInteger.valueOf(halfPoints));

            // 设置复杂脚本字体大小（用于中文等）
            if (run.getCTR().getRPr().getSzCs() == null) {
                run.getCTR().getRPr().addNewSzCs();
            }
            run.getCTR().getRPr().getSzCs().setVal(BigInteger.valueOf(halfPoints));
        }
    }

    /**
     * 设置表格整体边框
     */
    private void setTableBorders(XWPFTable table) {
        try {
            // 获取或创建表格属性
            if (table.getCTTbl().getTblPr() == null) {
                table.getCTTbl().addNewTblPr();
            }

            // 获取或创建表格边框属性
            if (table.getCTTbl().getTblPr().getTblBorders() == null) {
                table.getCTTbl().getTblPr().addNewTblBorders();
            }

            // 创建边框样式
            org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder border = 
                org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder.Factory.newInstance();
            border.setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STBorder.SINGLE);
            border.setSz(java.math.BigInteger.valueOf(6)); // 外边框稍微粗一点
            border.setSpace(java.math.BigInteger.valueOf(0));
            border.setColor("000000"); // 黑色边框

            // 设置表格外边框
            table.getCTTbl().getTblPr().getTblBorders().setTop(border);
            table.getCTTbl().getTblPr().getTblBorders().setBottom((org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder) border.copy());
            table.getCTTbl().getTblPr().getTblBorders().setLeft((org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder) border.copy());
            table.getCTTbl().getTblPr().getTblBorders().setRight((org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder) border.copy());

            // 创建内边框样式（稍微细一点）
            org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder insideBorder = 
                org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder.Factory.newInstance();
            insideBorder.setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STBorder.SINGLE);
            insideBorder.setSz(java.math.BigInteger.valueOf(4));
            insideBorder.setSpace(java.math.BigInteger.valueOf(0));
            insideBorder.setColor("000000");

            // 设置表格内边框
            table.getCTTbl().getTblPr().getTblBorders().setInsideH(insideBorder);
            table.getCTTbl().getTblPr().getTblBorders().setInsideV((org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder) insideBorder.copy());

        } catch (Exception e) {
            log.warn("设置表格边框失败: {}", e.getMessage());
        }
    }

    /**
     * 设置表格行高（最小高度，内容多时可自动扩展）
     * @param row 表格行
     * @param minHeightCm 最小行高（厘米）
     */
    private void setRowHeight(XWPFTableRow row, double minHeightCm) {
        try {
            // 将厘米转换为缇（twips），1厘米 = 567缇
            int heightTwips = (int) (minHeightCm * 567);

            // 获取或创建行属性
            if (row.getCtRow().getTrPr() == null) {
                row.getCtRow().addNewTrPr();
            }

            // 获取或创建行高属性
            if (row.getCtRow().getTrPr().getTrHeightArray() == null || row.getCtRow().getTrPr().getTrHeightArray().length == 0) {
                row.getCtRow().getTrPr().addNewTrHeight();
            }

            // 设置最小行高，使用 AT_LEAST 规则允许内容多时自动扩展
            row.getCtRow().getTrPr().getTrHeightArray(0).setVal(BigInteger.valueOf(heightTwips));
            row.getCtRow().getTrPr().getTrHeightArray(0).setHRule(org.openxmlformats.schemas.wordprocessingml.x2006.main.STHeightRule.AT_LEAST);

        } catch (Exception e) {
            log.warn("设置行高失败: {}", e.getMessage());
        }
    }

    /**
     * 设置单元格边框
     */
    private void setCellBorders(XWPFTableCell cell) {
        try {
            // 获取或创建单元格属性
            if (cell.getCTTc().getTcPr() == null) {
                cell.getCTTc().addNewTcPr();
            }

            // 获取或创建边框属性
            if (cell.getCTTc().getTcPr().getTcBorders() == null) {
                cell.getCTTc().getTcPr().addNewTcBorders();
            }

            // 设置所有边框（上、下、左、右）
            org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder border = 
                org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder.Factory.newInstance();
            border.setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STBorder.SINGLE);
            border.setSz(java.math.BigInteger.valueOf(4)); // 边框宽度
            border.setSpace(java.math.BigInteger.valueOf(0));
            border.setColor("000000"); // 黑色边框

            // 设置四个边的边框
            cell.getCTTc().getTcPr().getTcBorders().setTop(border);
            cell.getCTTc().getTcPr().getTcBorders().setBottom((org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder) border.copy());
            cell.getCTTc().getTcPr().getTcBorders().setLeft((org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder) border.copy());
            cell.getCTTc().getTcPr().getTcBorders().setRight((org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder) border.copy());

        } catch (Exception e) {
            log.warn("设置单元格边框失败: {}", e.getMessage());
        }
    }
}