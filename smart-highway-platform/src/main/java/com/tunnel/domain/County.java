package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * 农村项目信息对象 sc_county_base
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class County extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 唯一主键 */
    private Long id;

    /** 年份 */
    @Excel(name = "年份", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "年份不能为空")
    private Integer year;

    /** 项目名称 */
    @Excel(name = "项目名称", cellType = Excel.ColumnType.STRING)
    @NotBlank(message = "项目名称不能为空")
    @Size(max = 100, message = "项目名称长度不能超过100个字符")
    private String projectName;

    /** 委托单位 */
    @Excel(name = "委托单位", cellType = Excel.ColumnType.STRING)
    @Size(max = 100, message = "委托单位长度不能超过100个字符")
    private String companyName;

    /** 行政等级 */
    @Excel(name = "行政等级", cellType = Excel.ColumnType.STRING)
    @NotBlank(message = "行政等级不能为空")
    @Size(max = 60, message = "行政等级长度不能超过60个字符")
    private String rank;

    /** 检测区域 */
    @Excel(name = "检测区域", cellType = Excel.ColumnType.STRING)
    @Size(max = 50, message = "检测区域长度不能超过50个字符")
    private String checkArea;

    /** 检测起始日期 */
    @Excel(name = "检测起始日期", cellType = Excel.ColumnType.STRING)
    @NotBlank(message = "检测起始日期不能为空")
    @Size(max = 60, message = "检测起始日期长度不能超过60个字符")
    private String startDate;

    /** 检测结束日期 */
    @Excel(name = "检测结束日期", cellType = Excel.ColumnType.STRING)
    @NotBlank(message = "检测结束日期不能为空")
    @Size(max = 60, message = "检测结束日期长度不能超过60个字符")
    private String endDate;

    /** 报告编号 */
    @Excel(name = "报告编号", cellType = Excel.ColumnType.STRING)
    @NotBlank(message = "报告编号不能为空")
    @Size(max = 60, message = "报告编号长度不能超过60个字符")
    private String reportNo;

    /** 备注信息 */
    @Excel(name = "备注信息", cellType = Excel.ColumnType.STRING)
    private String remark;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private Long creator;

    /** 更新人 */
    private Long modifier;
} 