package com.tunnel.service;

import com.tunnel.domain.County;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 农村项目信息Service接口
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
public interface CountyService {
    /**
     * 查询农村项目信息
     *
     * @param id 农村项目信息主键
     * @return 农村项目信息
     */
    public County selectCountyById(Long id);

    /**
     * 查询农村项目信息列表
     *
     * @param county 农村项目信息
     * @return 农村项目信息集合
     */
    public List<County> selectCountyList(County county);

    /**
     * 新增农村项目信息
     *
     * @param county 农村项目信息
     * @return 结果
     */
    public int insertCounty(County county);

    /**
     * 修改农村项目信息
     *
     * @param county 农村项目信息
     * @return 结果
     */
    public int updateCounty(County county);

    /**
     * 批量删除农村项目信息
     *
     * @param ids 需要删除的农村项目信息主键集合
     * @return 结果
     */
    public int deleteCountyByIds(Long[] ids);

    /**
     * 删除农村项目信息
     *
     * @param id 农村项目信息主键
     * @return 结果
     */
    public int deleteCountyById(Long id);

    /**
     * 根据报告编号查询农村项目信息
     *
     * @param reportNo 报告编号
     * @return 农村项目信息
     */
    public County selectCountyByReportNo(String reportNo);

    /**
     * 校验报告编号是否唯一
     *
     * @param county 农村项目信息
     * @return 结果
     */
    public String checkReportNoUnique(County county);

    /**
     * 导出农村项目信息列表
     *
     * @param response HTTP响应
     * @param county 查询条件
     */
    public void exportCounty(HttpServletResponse response, County county);

    /**
     * 批量导入农村项目数据
     *
     * @param file 导入文件
     * @return 导入结果
     */
    public String importCounty(MultipartFile file);

    /**
     * 下载导入模板
     *
     * @param response HTTP响应
     */
    public void importTemplate(HttpServletResponse response);

    /**
     * 获取农村项目信息记录数
     *
     * @param county 查询条件
     * @return 记录数
     */
    public int countCounty(County county);
} 