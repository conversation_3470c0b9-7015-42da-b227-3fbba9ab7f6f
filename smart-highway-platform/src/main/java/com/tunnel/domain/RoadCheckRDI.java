package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 路面车辙深度检测信息对象 sc_road_check_rdi
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RoadCheckRDI extends BaseRoadCheckEntity {
    private static final long serialVersionUID = 1L;

    // 基础字段已在BaseRoadCheckEntity中定义

    /** 左轮迹车辙深度 */
    @Excel(name = "左轮迹车辙深度", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal leftRd;

    /** 右轮迹车辙深度 */
    @Excel(name = "右轮迹车辙深度", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal rightRd;

    /** 最大车辙深度 */
    @Excel(name = "最大车辙深度", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal maxRd;

    // 路面类型、时间字段等已在BaseRoadCheckEntity中定义

    /** 道路名称（非数据库字段，用于关联查询） */
    private String roadName;
} 