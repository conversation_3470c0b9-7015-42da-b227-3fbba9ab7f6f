package com.tunnel.common.utils;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.List;
import java.util.ArrayList;

/**
 * 桩号工具类，用于计算百米段和公里段
 *
 * <AUTHOR>
 */
public class StakeCodeUtil {

    private static final Logger log = LoggerFactory.getLogger(StakeCodeUtil.class);

    /**
     * 根据起始桩号计算百米段
     * 支持多种格式：K2320+260、2320260等
     * 例如：K2320+260 -> K2320+200~K2320+300
     *
     * @param startCode 起始桩号
     * @return 百米段
     */
    public static String calculateHundredSection(String startCode) {
        if (StringUtils.isBlank(startCode)) {
            return "";
        }
        try {
            // 将桩号转换为总米数
            long totalMeters = parseStakeCodeToMeters(startCode);
            if (totalMeters < 0) {
                return "";
            }

            // 计算百米段的起始和结束（按100米分段）
            long startMeters = (totalMeters / 100) * 100;
            long endMeters = startMeters + 100;

            // 转换回桩号格式
            String startStake = formatMetersToStakeCode(startMeters);
            String endStake = formatMetersToStakeCode(endMeters);

            return startStake + "~" + endStake;
        } catch (Exception e) {
            return "";
        }
    }


    /**
     * 根据起始桩号计算公里段
     * 支持多种格式：K2320+260、2320260等
     * 例如：K2320+260 -> K2320+000~K2321+000
     *
     * @param startCode 起始桩号
     * @return 公里段
     */
    public static String calculateThousandSection(String startCode) {
        if (StringUtils.isBlank(startCode)) {
            return "";
        }
        try {
            // 将桩号转换为总米数
            long totalMeters = parseStakeCodeToMeters(startCode);
            if (totalMeters < 0) {
                return "";
            }

            // 计算公里段的起始和结束（按1000米分段）
            long startMeters = (totalMeters / 1000) * 1000;
            long endMeters = startMeters + 1000;

            // 转换回桩号格式
            String startStake = formatMetersToStakeCode(startMeters);
            String endStake = formatMetersToStakeCode(endMeters);

            return startStake + "~" + endStake;
        } catch (Exception e) {
            return "";
        }
    }


    /**
     * 格式化桩号为标准K格式（例如：2320000 -> K2320+000）
     * 支持多种输入格式的自动识别和转换
     *
     * 支持的格式示例：
     * - 纯数字：2320000 -> K2320+000, 2320260 -> K2320+260
     * - K+数字：K2320000 -> K2320+000, K2320260 -> K2320+260
     * - +号分隔：2320+260 -> K2320+260, K2320+60 -> K2320+060, K23+260 -> K23+260
     * - 小数格式：23.260 -> K23+260, K23.260 -> K23+260
     * - 空格分隔：K2320 260 -> K2320+260, 2320 260 -> K2320+260
     * - 标准格式：K2320+260 -> K2320+260（不变）
     */
    public static String formatStakeCode(String stakeCode) {
        if (stakeCode == null || stakeCode.trim().isEmpty()) {
            return "";
        }

        stakeCode = stakeCode.trim().toUpperCase(); // 统一转为大写

        // 如果已经是K开头的标准格式（K数字+三位数字），直接返回
        if (stakeCode.startsWith("K") && stakeCode.contains("+") && stakeCode.matches("K\\d+\\+\\d{3}")) {
            return stakeCode;
        }

        try {
            // 处理纯数字格式（如：2320000, 2320260, 2320000.0）
            if (stakeCode.matches("\\d+(\\.0+)?")) {
                // 去掉小数点和后面的零
                String cleanNumber = stakeCode.replaceAll("\\.0+$", "");
                long value = Long.parseLong(cleanNumber);
                // 转换为公里和米
                long km = value / 1000;
                long m = value % 1000;
                return String.format("K%d+%03d", km, m);
            }

            // 处理已经是K开头但没有+号的格式（如：K2320000, K2320260）
            if (stakeCode.startsWith("K") && stakeCode.matches("K\\d+")) {
                String numberPart = stakeCode.substring(1);
                long value = Long.parseLong(numberPart);
                long km = value / 1000;
                long m = value % 1000;
                return String.format("K%d+%03d", km, m);
            }

            // 处理带+号但格式不标准的情况（如：2320+260, K2320+260, K2320+60）
            if (stakeCode.contains("+")) {
                String cleanCode = stakeCode.replace("K", "");
                String[] parts = cleanCode.split("\\+");
                if (parts.length == 2) {
                    long km = Long.parseLong(parts[0]);
                    long m = Long.parseLong(parts[1]);
                    return String.format("K%d+%03d", km, m);
                }
            }

            // 处理小数点格式（如：23.260, 23.200）- 公里为单位
            if (stakeCode.contains(".")) {
                double value = Double.parseDouble(stakeCode.replace("K", ""));
                long totalMeters = Math.round(value * 1000);
                long km = totalMeters / 1000;
                long m = totalMeters % 1000;
                return String.format("K%d+%03d", km, m);
            }

            // 处理带空格的格式（如：K2320 260, 2320 260）
            if (stakeCode.contains(" ")) {
                String cleanCode = stakeCode.replace("K", "").replace(" ", "");
                if (cleanCode.matches("\\d+")) {
                    long value = Long.parseLong(cleanCode);
                    long km = value / 1000;
                    long m = value % 1000;
                    return String.format("K%d+%03d", km, m);
                }
            }

            // 处理类似 K23+260 这种已经是正确格式但+前数字不够的情况
            if (stakeCode.startsWith("K") && stakeCode.contains("+")) {
                String cleanCode = stakeCode.substring(1); // 去掉K
                String[] parts = cleanCode.split("\\+");
                if (parts.length == 2) {
                    long km = Long.parseLong(parts[0]);
                    long m = Long.parseLong(parts[1]);
                    return String.format("K%d+%03d", km, m);
                }
            }

        } catch (NumberFormatException e) {
            log.warn("桩号格式化失败，无法解析数字: {}", stakeCode);
        } catch (Exception e) {
            log.warn("桩号格式化失败: {}, 错误: {}", stakeCode, e.getMessage());
        }

        // 如果所有格式化都失败，返回原始值
        log.warn("桩号格式化失败，使用原始值: {}", stakeCode);
        return stakeCode;
    }

    /**
     * 修正数据集合中的百米段和公里段，确保它们不超出实际的最大结束桩号
     * 这是为了保证hundred_section和thousand_section有统一的逻辑
     *
     * @param dataList 数据列表，泛型T必须有getEndCode, getHundredSection, getThousandSection,
     *                 setHundredSection, setThousandSection等方法
     * @param <T> 数据类型，支持所有检测数据实体类
     */
    public static <T> void correctSectionsWithMaxEndCode(List<T> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }

        try {
            // 找到最大的实际结束桩号
            String maxActualEndCode = null;
            for (T record : dataList) {
                String endCode = getEndCodeByReflection(record);
                if (endCode != null && !endCode.trim().isEmpty()) {
                    if (maxActualEndCode == null || endCode.compareTo(maxActualEndCode) > 0) {
                        maxActualEndCode = endCode;
                    }
                }
            }

            if (maxActualEndCode == null) {
                return;
            }

            log.info("找到最大实际结束桩号: {}", maxActualEndCode);

            // 解析最大结束桩号为米数
            long maxEndMeters = parseStakeCodeToMeters(maxActualEndCode);

            if (maxEndMeters > 0) {
                // 遍历所有记录，修正超出实际结束桩号的段位
                for (T record : dataList) {
                    boolean needUpdate = false;

                    // 检查并修正百米段
                    String hundredSection = getHundredSectionByReflection(record);
                    if (hundredSection != null && hundredSection.contains("~")) {
                        String[] parts = hundredSection.split("~");
                        if (parts.length == 2) {
                            String hundredEndCode = parts[1].trim();
                            long hundredEndMeters = parseStakeCodeToMeters(hundredEndCode);
                            if (hundredEndMeters > maxEndMeters) {
                                // 百米段结束超出了实际结束桩号，需要修正
                                String newHundredSection = parts[0].trim() + "~" + maxActualEndCode;
                                setHundredSectionByReflection(record, newHundredSection);
                                needUpdate = true;
                                log.debug("修正百米段：{} -> {}", hundredSection, newHundredSection);
                            }
                        }
                    }

                    // 检查并修正公里段
                    String thousandSection = getThousandSectionByReflection(record);
                    if (thousandSection != null && thousandSection.contains("~")) {
                        String[] parts = thousandSection.split("~");
                        if (parts.length == 2) {
                            String thousandEndCode = parts[1].trim();
                            long thousandEndMeters = parseStakeCodeToMeters(thousandEndCode);
                            if (thousandEndMeters > maxEndMeters) {
                                // 公里段结束超出了实际结束桩号，需要修正
                                String newThousandSection = parts[0].trim() + "~" + maxActualEndCode;
                                setThousandSectionByReflection(record, newThousandSection);
                                needUpdate = true;
                                log.debug("修正公里段：{} -> {}", thousandSection, newThousandSection);
                            }
                        }
                    }

                    if (needUpdate) {
                        String startCode = getStartCodeByReflection(record);
                        String endCode = getEndCodeByReflection(record);
                        String newHundredSection = getHundredSectionByReflection(record);
                        String newThousandSection = getThousandSectionByReflection(record);
                        log.info("修正记录段位：起始={}, 结束={}, 百米段={}, 公里段={}",
                                startCode, endCode, newHundredSection, newThousandSection);
                    }
                }
            }
        } catch (Exception e) {
            log.error("修正段位时发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 通过反射获取对象的endCode属性
     */
    private static <T> String getEndCodeByReflection(T obj) {
        try {
            java.lang.reflect.Method method = obj.getClass().getMethod("getEndCode");
            Object result = method.invoke(obj);
            return result != null ? result.toString() : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 通过反射获取对象的startCode属性
     */
    private static <T> String getStartCodeByReflection(T obj) {
        try {
            java.lang.reflect.Method method = obj.getClass().getMethod("getStartCode");
            Object result = method.invoke(obj);
            return result != null ? result.toString() : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 通过反射获取对象的hundredSection属性
     */
    private static <T> String getHundredSectionByReflection(T obj) {
        try {
            java.lang.reflect.Method method = obj.getClass().getMethod("getHundredSection");
            Object result = method.invoke(obj);
            return result != null ? result.toString() : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 通过反射获取对象的thousandSection属性
     */
    private static <T> String getThousandSectionByReflection(T obj) {
        try {
            java.lang.reflect.Method method = obj.getClass().getMethod("getThousandSection");
            Object result = method.invoke(obj);
            return result != null ? result.toString() : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 通过反射设置对象的hundredSection属性
     */
    private static <T> void setHundredSectionByReflection(T obj, String value) {
        try {
            java.lang.reflect.Method method = obj.getClass().getMethod("setHundredSection", String.class);
            method.invoke(obj, value);
        } catch (Exception e) {
            log.warn("设置hundredSection失败: {}", e.getMessage());
        }
    }

    /**
     * 通过反射设置对象的thousandSection属性
     */
    private static <T> void setThousandSectionByReflection(T obj, String value) {
        try {
            java.lang.reflect.Method method = obj.getClass().getMethod("setThousandSection", String.class);
            method.invoke(obj, value);
        } catch (Exception e) {
            log.warn("设置thousandSection失败: {}", e.getMessage());
        }
    }

    /**
     * 将桩号转换为总米数
     * 支持格式：K2320+260、2320260、K2320000等
     */
    public static long parseStakeCodeToMeters(String stakeCode) {
        if (StringUtils.isBlank(stakeCode)) {
            return -1;
        }

        stakeCode = stakeCode.trim().toUpperCase();

        try {
            // 处理K2320+260格式
            if (stakeCode.startsWith("K") && stakeCode.contains("+")) {
                String cleanCode = stakeCode.substring(1); // 去掉K
                String[] parts = cleanCode.split("\\+");
                if (parts.length == 2) {
                    long km = Long.parseLong(parts[0]);
                    long m = Long.parseLong(parts[1]);
                    return km * 1000 + m;
                }
            }

            // 处理纯数字格式（如：2320260）
            if (stakeCode.matches("\\d+")) {
                return Long.parseLong(stakeCode);
            }

            // 处理K开头但没有+号的格式（如：K2320260）
            if (stakeCode.startsWith("K") && stakeCode.matches("K\\d+")) {
                String numberPart = stakeCode.substring(1);
                return Long.parseLong(numberPart);
            }

            return -1;
        } catch (NumberFormatException e) {
            return -1;
        }
    }

    /**
     * 将总米数转换为桩号格式
     * 例如：2320260 -> K2320+260
     */
    private static String formatMetersToStakeCode(long totalMeters) {
        long km = totalMeters / 1000;
        long m = totalMeters % 1000;
        return String.format("K%d+%03d", km, m);
    }

    /**
     * 根据roadType按顺序分段处理段位信息（优化版）
     * 优化策略：先基于整体数据计算连续的段位信息，再根据路面类型进行标记
     * 这样可以保证百米段和公里段在跨越不同路面类型时保持桩号的连续性
     *
     * @param dataList 需要处理段位的数据列表（已按桩号排序）
     */
    public static <T> void processSectionsByRoadType(List<T> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }

        log.info("开始优化处理段位信息，共 {} 条记录", dataList.size());

        // 第一步：确保数据按桩号排序
        dataList.sort((a, b) -> {
            String aStartCode = getStartCodeByReflection(a);
            String bStartCode = getStartCodeByReflection(b);
            if (aStartCode == null) return 1;
            if (bStartCode == null) return -1;
            return aStartCode.compareTo(bStartCode);
        });

        // 第二步：获取整体数据的桩号范围
        String globalMinStakeCode = getStartCodeByReflection(dataList.get(0));
        String globalMaxStakeCode = dataList.stream()
                .map(StakeCodeUtil::getEndCodeByReflection)
                .filter(code -> code != null && !code.trim().isEmpty())
                .max(String::compareTo)
                .orElse(getStartCodeByReflection(dataList.get(dataList.size() - 1)));

        log.info("整体桩号范围：{} ~ {}", globalMinStakeCode, globalMaxStakeCode);

        // 第三步：识别路面类型分段
        List<RoadTypeSegment> roadTypeSegments = identifyRoadTypeSegments(dataList);
        
        // 第四步：基于路面类型分段计算连续的百米段和公里段范围
        List<HundredSectionRange> globalHundredRanges = calculateGlobalHundredSectionRanges(roadTypeSegments);
        List<ThousandSectionRange> globalThousandRanges = calculateOptimizedThousandSectionRanges(roadTypeSegments);

        log.info("计算得到 {} 个百米段范围，{} 个公里段范围", globalHundredRanges.size(), globalThousandRanges.size());

        // 第五步：为每条记录分配对应的段位信息（基于全局连续的段位）
        for (T record : dataList) {
            String startCode = getStartCodeByReflection(record);
            String endCode = getEndCodeByReflection(record);

            if (startCode != null && !startCode.trim().isEmpty()) {
                // 分配百米段（基于全局连续范围）
                String hundredSection = findMatchingHundredSection(startCode, globalHundredRanges);
                setHundredSectionByReflection(record, hundredSection);

                // 分配公里段（基于全局连续范围）
                String thousandSection = findMatchingThousandSection(startCode, globalThousandRanges);
                setThousandSectionByReflection(record, thousandSection);
            }
        }

        // 第六步：按路面类型统计和验证结果
        validateAndLogRoadTypeStatistics(dataList);

        log.info("优化段位信息处理完成，保证了跨路面类型的段位连续性");
    }

    /**
     * 通过反射获取对象的roadType属性
     */
    private static <T> String getRoadTypeByReflection(T obj) {
        try {
            java.lang.reflect.Method method = obj.getClass().getMethod("getRoadType");
            Object result = method.invoke(obj);
            return result != null ? result.toString() : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 基于路面类型分段计算优化后的公里段范围列表
     * 在每个路面类型分段内计算公里段，确保不跨越路面类型边界
     * 同时应用优化规则：
     * 1. 如果第一段的起点桩号距离下一个公里桩号小于200米，合并到下一段公里的桩号
     * 2. 如果倒数第二段的终点桩号合并到最后一段的桩号范围，距离不超过1200米，就省掉最后一段公里范围
     *
     * @param roadTypeSegments 路面类型分段列表
     * @return 优化后的公里段范围列表
     */
    private static List<ThousandSectionRange> calculateOptimizedThousandSectionRanges(List<RoadTypeSegment> roadTypeSegments) {
        List<ThousandSectionRange> allRanges = new ArrayList<>();
        
        try {
            for (RoadTypeSegment segment : roadTypeSegments) {
                long segmentStart = segment.startMeters;
                long segmentEnd = segment.endMeters;
                
                log.info("处理路面类型 '{}' 分段：{} ~ {}", 
                        segment.roadType, 
                        formatMetersToStakeCode(segmentStart), 
                        formatMetersToStakeCode(segmentEnd));
                
                // 为当前路面类型分段生成标准公里段范围
                List<ThousandSectionRange> segmentRanges = generateStandardThousandSectionRanges(segmentStart, segmentEnd);
                
                if (!segmentRanges.isEmpty()) {
                    // 对当前分段的公里段应用优化规则
                    List<ThousandSectionRange> optimizedSegmentRanges = applyOptimizationRules(segmentRanges);
                    
                    log.info("路面类型 '{}' 分段优化后生成 {} 个公里段：", segment.roadType, optimizedSegmentRanges.size());
                    for (int i = 0; i < optimizedSegmentRanges.size(); i++) {
                        log.info("    第{}段：{}", i + 1, optimizedSegmentRanges.get(i).toString());
                    }
                    
                    allRanges.addAll(optimizedSegmentRanges);
                }
            }
            
            log.info("基于路面类型分段生成了总共 {} 个公里段范围", allRanges.size());
            
        } catch (Exception e) {
            log.error("计算基于路面类型的公里段范围时发生错误", e);
        }
        
        return allRanges;
    }
    

    /**
     * 生成标准的公里段范围（按1000米分段）
     */
    private static List<ThousandSectionRange> generateStandardThousandSectionRanges(long minMeters, long maxMeters) {
        List<ThousandSectionRange> ranges = new ArrayList<>();

        // 计算第一个公里段的起始位置
        long currentStart = minMeters;

        while (currentStart < maxMeters) {
            // 计算当前公里段的理论结束位置
            long kmStart = (currentStart / 1000) * 1000;
            long kmEnd = kmStart + 1000;

            // 实际结束位置不能超过最大桩号
            long actualEnd = Math.min(kmEnd, maxMeters);

            ranges.add(new ThousandSectionRange(currentStart, actualEnd));

            // 移动到下一个公里段
            currentStart = kmEnd;
        }

        return ranges;
    }

    /**
     * 应用优化规则到标准公里段范围
     */
    private static List<ThousandSectionRange> applyOptimizationRules(List<ThousandSectionRange> standardRanges) {
        if (standardRanges == null || standardRanges.isEmpty()) {
            return new ArrayList<>();
        }

        List<ThousandSectionRange> optimizedRanges = new ArrayList<>(standardRanges);

        // 规则1：如果第一段的起点桩号距离下一个公里桩号小于200米，合并到下一段公里的桩号
        if (optimizedRanges.size() >= 2) {
            ThousandSectionRange firstRange = optimizedRanges.get(0);
            ThousandSectionRange secondRange = optimizedRanges.get(1);

            // 计算第一段起点到下一个公里桩号的距离
            long firstStartKm = (firstRange.startMeters / 1000) * 1000;
            long nextKmStart = firstStartKm + 1000;
            long distanceToNextKm = nextKmStart - firstRange.startMeters;

            if (distanceToNextKm < 200) {
                log.info("应用规则1：第一段起点{}距离下一个公里桩号{}米，小于200米，合并到下一段",
                        formatMetersToStakeCode(firstRange.startMeters), distanceToNextKm);

                // 合并第一段和第二段
                ThousandSectionRange mergedRange = new ThousandSectionRange(firstRange.startMeters, secondRange.endMeters);
                optimizedRanges.set(0, mergedRange);
                optimizedRanges.remove(1);
            }
        }

        // 规则2：如果倒数第二段的终点桩号合并到最后一段的桩号范围，距离不超过1200米，就省掉最后一段公里范围
        if (optimizedRanges.size() >= 2) {
            ThousandSectionRange secondLastRange = optimizedRanges.get(optimizedRanges.size() - 2);
            ThousandSectionRange lastRange = optimizedRanges.get(optimizedRanges.size() - 1);

            // 计算合并后的总距离
            long totalDistance = lastRange.endMeters - secondLastRange.startMeters;

            if (totalDistance <= 1200) {
                log.info("应用规则2：倒数第二段和最后一段合并后总距离{}米，不超过1200米，合并这两段", totalDistance);

                // 合并倒数第二段和最后一段
                ThousandSectionRange mergedRange = new ThousandSectionRange(secondLastRange.startMeters, lastRange.endMeters);
                optimizedRanges.set(optimizedRanges.size() - 2, mergedRange);
                optimizedRanges.remove(optimizedRanges.size() - 1);
            }
        }

        return optimizedRanges;
    }

    /**
     * 根据起始桩号查找匹配的公里段
     */
    private static String findMatchingThousandSection(String startCode, List<ThousandSectionRange> ranges) {
        try {
            long startMeters = parseStakeCodeToMeters(startCode);
            if (startMeters < 0) {
                return calculateThousandSection(startCode);
            }

            for (ThousandSectionRange range : ranges) {
                if (startMeters >= range.startMeters && startMeters < range.endMeters) {
                    return range.toString();
                }
            }

            // 如果没有找到匹配的范围，使用默认计算方法
            return calculateThousandSection(startCode);
        } catch (Exception e) {
            log.warn("查找匹配公里段失败，使用默认方法：{}", e.getMessage());
            return calculateThousandSection(startCode);
        }
    }



    /**
     * 识别路面类型分段
     * 按照数据顺序，当路面类型发生变化时分为不同的段
     *
     * @param dataList 数据列表
     * @return 路面类型分段列表
     */
    private static <T> List<RoadTypeSegment> identifyRoadTypeSegments(List<T> dataList) {
        List<RoadTypeSegment> segments = new ArrayList<>();
        
        if (dataList == null || dataList.isEmpty()) {
            return segments;
        }
        
        String currentRoadType = null;
        long segmentStartMeters = -1;
        long segmentEndMeters = -1;
        
        for (T record : dataList) {
            String recordRoadType = getRoadTypeByReflection(record);
            if (recordRoadType == null) {
                recordRoadType = "default";
            }
            
            String startCode = getStartCodeByReflection(record);
            String endCode = getEndCodeByReflection(record);
            
            if (startCode == null) continue;
            
            long startMeters = parseStakeCodeToMeters(startCode);
            long endMeters = parseStakeCodeToMeters(endCode != null ? endCode : startCode);
            
            // 如果路面类型发生变化，结束当前段并开始新段
            if (currentRoadType == null || !currentRoadType.equals(recordRoadType)) {
                // 保存之前的段（如果存在）
                if (currentRoadType != null && segmentStartMeters >= 0) {
                    segments.add(new RoadTypeSegment(currentRoadType, segmentStartMeters, segmentEndMeters));
                }
                
                // 开始新段
                currentRoadType = recordRoadType;
                segmentStartMeters = startMeters;
                segmentEndMeters = endMeters;
            } else {
                // 继续当前段，更新结束位置
                segmentEndMeters = Math.max(segmentEndMeters, endMeters);
            }
        }
        
        // 添加最后一段
        if (currentRoadType != null && segmentStartMeters >= 0) {
            segments.add(new RoadTypeSegment(currentRoadType, segmentStartMeters, segmentEndMeters));
        }
        
        log.info("识别出 {} 个路面类型分段：", segments.size());
        for (int i = 0; i < segments.size(); i++) {
            RoadTypeSegment segment = segments.get(i);
            log.info("  第{}段：路面类型 '{}', 桩号范围 {} ~ {}", 
                    i + 1, segment.roadType, 
                    formatMetersToStakeCode(segment.startMeters), 
                    formatMetersToStakeCode(segment.endMeters));
        }
        
        return segments;
    }
    
    /**
     * 基于路面类型分段计算全局连续的百米段范围
     * 在每个路面类型分段内计算百米段，确保不跨越路面类型边界
     *
     * @param roadTypeSegments 路面类型分段列表
     * @return 百米段范围列表
     */
    private static List<HundredSectionRange> calculateGlobalHundredSectionRanges(List<RoadTypeSegment> roadTypeSegments) {
        List<HundredSectionRange> ranges = new ArrayList<>();
        
        try {
            for (RoadTypeSegment segment : roadTypeSegments) {
                long segmentStart = segment.startMeters;
                long segmentEnd = segment.endMeters;
                
                // 在当前路面类型分段内计算百米段
                long currentStart = segmentStart;
                
                while (currentStart < segmentEnd) {
                    // 计算当前百米段的理论结束位置
                    long hundredStart = (currentStart / 100) * 100;
                    long hundredEnd = hundredStart + 100;
                    
                    // 实际结束位置不能超过当前路面类型分段的结束位置
                    long actualEnd = Math.min(hundredEnd, segmentEnd);
                    
                    ranges.add(new HundredSectionRange(currentStart, actualEnd));
                    
                    // 移动到下一个百米段
                    currentStart = hundredEnd;
                    
                    // 如果下一个百米段超出了当前路面类型分段，跳出循环
                    if (currentStart >= segmentEnd) {
                        break;
                    }
                }
            }
            
            log.info("基于路面类型分段生成了 {} 个百米段范围", ranges.size());
            
        } catch (Exception e) {
            log.error("计算基于路面类型的百米段范围时发生错误", e);
        }
        
        return ranges;
    }

    /**
     * 根据起始桩号查找匹配的百米段
     */
    private static String findMatchingHundredSection(String startCode, List<HundredSectionRange> ranges) {
        try {
            long startMeters = parseStakeCodeToMeters(startCode);
            if (startMeters < 0) {
                return calculateHundredSection(startCode);
            }

            for (HundredSectionRange range : ranges) {
                if (startMeters >= range.startMeters && startMeters < range.endMeters) {
                    return range.toString();
                }
            }

            // 如果没有找到匹配的范围，使用默认计算方法
            return calculateHundredSection(startCode);
        } catch (Exception e) {
            log.warn("查找匹配百米段失败，使用默认方法：{}", e.getMessage());
            return calculateHundredSection(startCode);
        }
    }

    /**
     * 按路面类型统计和验证结果
     */
    private static <T> void validateAndLogRoadTypeStatistics(List<T> dataList) {
        try {
            // 按路面类型分组统计
            java.util.Map<String, java.util.List<T>> roadTypeGroups = new java.util.HashMap<>();
            
            for (T record : dataList) {
                String roadType = getRoadTypeByReflection(record);
                if (roadType == null) {
                    roadType = "default";
                }
                
                roadTypeGroups.computeIfAbsent(roadType, k -> new ArrayList<>()).add(record);
            }

            log.info("按路面类型统计结果：");
            for (java.util.Map.Entry<String, java.util.List<T>> entry : roadTypeGroups.entrySet()) {
                String roadType = entry.getKey();
                java.util.List<T> records = entry.getValue();
                
                if (!records.isEmpty()) {
                    String firstStartCode = getStartCodeByReflection(records.get(0));
                    String lastEndCode = getEndCodeByReflection(records.get(records.size() - 1));
                    
                    // 获取该路面类型的段位信息示例
                    String sampleHundredSection = getHundredSectionByReflection(records.get(0));
                    String sampleThousandSection = getThousandSectionByReflection(records.get(0));
                    
                    log.info("  路面类型 '{}': {} 条记录, 桩号范围 {} ~ {}, 示例段位 [百米段: {}, 公里段: {}]", 
                            roadType, records.size(), firstStartCode, lastEndCode, 
                            sampleHundredSection, sampleThousandSection);
                }
            }

            // 验证段位连续性
            validateSectionContinuity(dataList);

        } catch (Exception e) {
            log.error("统计和验证路面类型结果时发生错误", e);
        }
    }

    /**
     * 验证段位连续性
     */
    private static <T> void validateSectionContinuity(List<T> dataList) {
        try {
            java.util.Set<String> hundredSections = new java.util.HashSet<>();
            java.util.Set<String> thousandSections = new java.util.HashSet<>();
            
            for (T record : dataList) {
                String hundredSection = getHundredSectionByReflection(record);
                String thousandSection = getThousandSectionByReflection(record);
                
                if (hundredSection != null) {
                    hundredSections.add(hundredSection);
                }
                if (thousandSection != null) {
                    thousandSections.add(thousandSection);
                }
            }
            
            log.info("段位连续性验证：共生成 {} 个不同的百米段，{} 个不同的公里段", 
                    hundredSections.size(), thousandSections.size());
            
            // 记录前几个段位用于验证
            if (!hundredSections.isEmpty()) {
                java.util.List<String> sortedHundred = new ArrayList<>(hundredSections);
                sortedHundred.sort(String::compareTo);
                log.info("前5个百米段示例：{}", sortedHundred.subList(0, Math.min(5, sortedHundred.size())));
            }
            
            if (!thousandSections.isEmpty()) {
                java.util.List<String> sortedThousand = new ArrayList<>(thousandSections);
                sortedThousand.sort(String::compareTo);
                log.info("前5个公里段示例：{}", sortedThousand.subList(0, Math.min(5, sortedThousand.size())));
            }
            
        } catch (Exception e) {
            log.error("验证段位连续性时发生错误", e);
        }
    }

    /**
     * 路面类型分段内部类
     */
    private static class RoadTypeSegment {
        String roadType;
        long startMeters;
        long endMeters;
        
        public RoadTypeSegment(String roadType, long startMeters, long endMeters) {
            this.roadType = roadType;
            this.startMeters = startMeters;
            this.endMeters = endMeters;
        }
        
        @Override
        public String toString() {
            return String.format("路面类型: %s, 范围: %s ~ %s", 
                    roadType, 
                    formatMetersToStakeCode(startMeters), 
                    formatMetersToStakeCode(endMeters));
        }
    }
    
    /**
     * 百米段范围内部类
     */
    private static class HundredSectionRange {
        long startMeters;
        long endMeters;

        public HundredSectionRange(long startMeters, long endMeters) {
            this.startMeters = startMeters;
            this.endMeters = endMeters;
        }

        @Override
        public String toString() {
            return formatMetersToStakeCode(startMeters) + "~" + formatMetersToStakeCode(endMeters);
        }
    }

    /**
     * 公里段范围内部类
     */
    private static class ThousandSectionRange {
        long startMeters;
        long endMeters;

        public ThousandSectionRange(long startMeters, long endMeters) {
            this.startMeters = startMeters;
            this.endMeters = endMeters;
        }

        @Override
        public String toString() {
            return formatMetersToStakeCode(startMeters) + "~" + formatMetersToStakeCode(endMeters);
        }
    }
}