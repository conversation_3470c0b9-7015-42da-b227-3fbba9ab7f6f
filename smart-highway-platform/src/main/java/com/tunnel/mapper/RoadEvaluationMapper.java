package com.tunnel.mapper;

import com.tunnel.domain.RoadEvaluation;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 道路技术状况评定信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
@Mapper
public interface RoadEvaluationMapper {
    /**
     * 查询道路技术状况评定信息
     *
     * @param id 道路技术状况评定信息主键
     * @return 道路技术状况评定信息
     */
    public RoadEvaluation selectRoadEvaluationById(Long id);

    /**
     * 查询道路技术状况评定信息列表
     *
     * @param roadEvaluation 道路技术状况评定信息
     * @return 道路技术状况评定信息集合
     */
    public List<RoadEvaluation> selectRoadEvaluationList(RoadEvaluation roadEvaluation);

    /**
     * 新增道路技术状况评定信息
     *
     * @param roadEvaluation 道路技术状况评定信息
     * @return 结果
     */
    public int insertRoadEvaluation(RoadEvaluation roadEvaluation);

    /**
     * 修改道路技术状况评定信息
     *
     * @param roadEvaluation 道路技术状况评定信息
     * @return 结果
     */
    public int updateRoadEvaluation(RoadEvaluation roadEvaluation);

    /**
     * 删除道路技术状况评定信息
     *
     * @param id 道路技术状况评定信息主键
     * @return 结果
     */
    public int deleteRoadEvaluationById(Long id);

    /**
     * 批量删除道路技术状况评定信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRoadEvaluationByIds(Long[] ids);

    /**
     * 校验路线编号是否唯一
     *
     * @param roadEvaluation 道路技术状况评定信息
     * @return 结果
     */
    public RoadEvaluation checkRoadCodeUnique(RoadEvaluation roadEvaluation);

    /**
     * 批量插入道路技术状况评定信息
     *
     * @param roadEvaluationList 道路技术状况评定信息列表
     * @return 结果
     */
    public int batchInsertRoadEvaluation(List<RoadEvaluation> roadEvaluationList);
} 