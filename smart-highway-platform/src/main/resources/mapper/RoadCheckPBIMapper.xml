<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.RoadCheckPBIMapper">

    <resultMap type="com.tunnel.domain.RoadCheckPBI" id="RoadCheckBumpResult">
        <result property="id" column="id"/>
        <result property="roadId" column="road_id"/>
        <result property="startCode" column="start_code"/>
        <result property="endCode" column="end_code"/>
        <result property="hundredSection" column="hundred_section"/>
        <result property="thousandSection" column="thousand_section"/>
        <result property="bpl" column="bpl"/>
        <result property="bpm" column="bpm"/>
        <result property="bph" column="bph"/>
        <result property="bumpHeight" column="bump_height"/>
        <result property="pbi" column="pbi"/>
        <result property="roadType" column="road_type"/>
        <result property="direction" column="direction"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="creator" column="creator"/>
        <result property="modifier" column="modifier"/>
        <result property="roadName" column="road_name"/>
    </resultMap>

    <sql id="selectRoadCheckBumpVo">
        select id, road_id, start_code, end_code, hundred_section, thousand_section, bpl, bpm, bph, bump_height, pbi,
               road_type, direction, create_time, update_time, creator, modifier
        from sc_road_check_pbi
    </sql>

    <select id="selectRoadCheckBumpList" parameterType="com.tunnel.domain.RoadCheckPBI" resultMap="RoadCheckBumpResult">
        <include refid="selectRoadCheckBumpVo"/>
        <where>
            <if test="roadId != null ">and road_id = #{roadId}</if>
            <if test="startCode != null and startCode != ''">and start_code = #{startCode}</if>
            <if test="endCode != null and endCode != ''">and end_code = #{endCode}</if>
            <if test="direction != null and direction != ''">and direction = #{direction}</if>
        </where>
    </select>
    
    <select id="selectRoadCheckBumpListByPage" resultMap="RoadCheckBumpResult">
        <include refid="selectRoadCheckBumpVo"/>
        <where>
            <if test="roadCheckPBI.roadId != null ">and road_id = #{roadCheckPBI.roadId}</if>
            <if test="roadCheckPBI.startCode != null and roadCheckPBI.startCode != ''">and start_code = #{roadCheckPBI.startCode}</if>
            <if test="roadCheckPBI.endCode != null and roadCheckPBI.endCode != ''">and end_code = #{roadCheckPBI.endCode}</if>
        </where>
        limit #{offset}, #{limit}
    </select>

    <select id="selectRoadCheckBumpById" parameterType="Long" resultMap="RoadCheckBumpResult">
        <include refid="selectRoadCheckBumpVo"/>
        where id = #{id}
    </select>

    <select id="selectRoadCheckBumpByRoadId" parameterType="Long" resultMap="RoadCheckBumpResult">
        <include refid="selectRoadCheckBumpVo"/>
        where road_id = #{roadId}
    </select>

    <insert id="insertRoadCheckBump" parameterType="com.tunnel.domain.RoadCheckPBI" useGeneratedKeys="true" keyProperty="id">
        insert into sc_road_check_pbi
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="roadId != null">road_id,</if>
            <if test="startCode != null and startCode != ''">start_code,</if>
            <if test="endCode != null and endCode != ''">end_code,</if>
            <if test="hundredSection != null and hundredSection != ''">hundred_section,</if>
            <if test="thousandSection != null and thousandSection != ''">thousand_section,</if>
            <if test="bpl != null">bpl,</if>
            <if test="bpm != null">bpm,</if>
            <if test="bph != null">bph,</if>
            <if test="bumpHeight != null">bump_height,</if>
            <if test="pbi != null">pbi,</if>
            <if test="roadType != null and roadType != ''">road_type,</if>
            <if test="direction != null">direction,</if>
            <if test="creator != null">creator,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="roadId != null">#{roadId},</if>
            <if test="startCode != null and startCode != ''">#{startCode},</if>
            <if test="endCode != null and endCode != ''">#{endCode},</if>
            <if test="hundredSection != null and hundredSection != ''">#{hundredSection},</if>
            <if test="thousandSection != null and thousandSection != ''">#{thousandSection},</if>
            <if test="bpl != null">#{bpl},</if>
            <if test="bpm != null">#{bpm},</if>
            <if test="bph != null">#{bph},</if>
            <if test="bumpHeight != null">#{bumpHeight},</if>
            <if test="pbi != null">#{pbi},</if>
            <if test="roadType != null and roadType != ''">#{roadType},</if>
            <if test="direction != null">#{direction},</if>
            <if test="creator != null">#{creator},</if>
        </trim>
    </insert>

    <update id="updateRoadCheckBump" parameterType="com.tunnel.domain.RoadCheckPBI">
        update sc_road_check_pbi
        <trim prefix="SET" suffixOverrides=",">
            <if test="roadId != null">road_id = #{roadId},</if>
            <if test="startCode != null and startCode != ''">start_code = #{startCode},</if>
            <if test="endCode != null and endCode != ''">end_code = #{endCode},</if>
            <if test="hundredSection != null and hundredSection != ''">hundred_section = #{hundredSection},</if>
            <if test="thousandSection != null and thousandSection != ''">thousand_section = #{thousandSection},</if>
            <if test="bpl != null">bpl = #{bpl},</if>
            <if test="bpm != null">bpm = #{bpm},</if>
            <if test="bph != null">bph = #{bph},</if>
            <if test="bumpHeight != null">bump_height = #{bumpHeight},</if>
            <if test="pbi != null">pbi = #{pbi},</if>
            <if test="roadType != null and roadType != ''">road_type = #{roadType},</if>
            <if test="direction != null">direction = #{direction},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRoadCheckBumpById" parameterType="Long">
        delete from sc_road_check_pbi where id = #{id}
    </delete>

    <delete id="deleteRoadCheckBumpByIds" parameterType="Long">
        delete from sc_road_check_pbi where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteRoadCheckBumpByRoadId" parameterType="Long">
        delete from sc_road_check_pbi where road_id = #{roadId}
    </delete>
    
    <insert id="batchInsert" parameterType="java.util.List">
        insert into sc_road_check_pbi(road_id, start_code, end_code, hundred_section, thousand_section, bpl, bpm, bph, 
                                  bump_height, pbi, road_type, direction, creator)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.roadId}, #{item.startCode}, #{item.endCode}, #{item.hundredSection}, #{item.thousandSection}, 
             #{item.bpl}, #{item.bpm}, #{item.bph},
             #{item.bumpHeight}, #{item.pbi}, #{item.roadType}, #{item.direction}, #{item.creator})
        </foreach>
    </insert>
    
    <select id="countRoadCheckBump" parameterType="com.tunnel.domain.RoadCheckPBI" resultType="int">
        select count(1) from sc_road_check_pbi
        <where>
            <if test="roadId != null ">and road_id = #{roadId}</if>
            <if test="startCode != null and startCode != ''">and start_code = #{startCode}</if>
            <if test="endCode != null and endCode != ''">and end_code = #{endCode}</if>
        </where>
    </select>
</mapper> 