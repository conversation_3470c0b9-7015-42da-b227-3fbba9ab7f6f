package com.tunnel.web.controller;

import com.tunnel.common.annotation.Anonymous;
import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.utils.StakeCodeUtil;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.BatchAddResponse;
import com.tunnel.domain.RoadCheckPCI;
import com.tunnel.service.RoadCheckPCIService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.List;

/**
 * 路面破损信息Controller
 *
 * <AUTHOR>
 * @date 2024-11-28
 */
@Api(tags = "路面破损信息管理")
@RestController
@RequestMapping("/smart/road/check")
public class RoadCheckPCIController extends BaseController {
    @Autowired
    private RoadCheckPCIService roadCheckPCIService;

    /**
     * 查询路面破损信息列表
     */
    @ApiOperation(value = "获取路面破损信息列表", notes = "获取路面破损信息列表")
//    @PreAuthorize("@ss.hasPermi('road:check:list')")
    @GetMapping("/list")
    public TableDataInfo list(RoadCheckPCI roadCheckPCI) {
        startPage();
        List<RoadCheckPCI> list = roadCheckPCIService.selectRoadCheckRecordList(roadCheckPCI);
        return getDataTable(list);
    }

    /**
     * 导出路面破损信息列表
     * 
     * 对于小数据量使用普通导出
     */
    @ApiOperation(value = "导出路面破损信息", notes = "导出路面破损信息列表")
//    @PreAuthorize("@ss.hasPermi('road:check:export')")
    @Log(title = "路面破损信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RoadCheckPCI roadCheckPCI) {
        // 数据量大于10000条，使用优化的导出方法
        roadCheckPCIService.exportOptimized(response, roadCheckPCI);
    }
    
    /**
     * 大数据量导出路面破损信息列表
     * 
     * 使用优化的导出方法，适用于几万条数据的导出
     */
    @ApiOperation(value = "大数据量导出路面破损信息", notes = "优化的导出方法，适用于几万条数据的导出")
//    @PreAuthorize("@ss.hasPermi('road:check:export')")
    @Log(title = "路面破损信息", businessType = BusinessType.EXPORT)
    @PostMapping("/exportLarge")
    public void exportLarge(HttpServletResponse response, RoadCheckPCI roadCheckPCI) {
        roadCheckPCIService.exportOptimized(response, roadCheckPCI);
    }

    /**
     * 获取路面破损信息详细信息
     */
    @ApiOperation(value = "获取路面破损信息详细", notes = "根据ID获取路面破损信息详细信息")
    @ApiImplicitParam(name = "id", value = "路面破损信息ID", required = true, dataType = "Long", paramType = "path")
//    @PreAuthorize("@ss.hasPermi('road:check:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(roadCheckPCIService.selectRoadCheckRecordById(id));
    }

    /**
     * 新增路面破损信息
     */
    @ApiOperation(value = "新增路面破损信息", notes = "新增路面破损信息")
//    @PreAuthorize("@ss.hasPermi('road:check:add')")
    @Log(title = "路面破损信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RoadCheckPCI roadCheckPCI) {
        return toAjax(roadCheckPCIService.insertRoadCheckRecord(roadCheckPCI));
    }

    /**
     * 修改路面破损信息
     */
    @ApiOperation(value = "修改路面破损信息", notes = "修改路面破损信息")
//    @PreAuthorize("@ss.hasPermi('road:check:edit')")
    @Log(title = "路面破损信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RoadCheckPCI roadCheckPCI) {
        return toAjax(roadCheckPCIService.updateRoadCheckRecord(roadCheckPCI));
    }

    /**
     * 删除路面破损信息
     */
    @ApiOperation(value = "删除路面破损信息", notes = "批量删除路面破损信息")
    @ApiImplicitParam(name = "ids", value = "路面破损信息ID数组", required = true, dataType = "Long[]", paramType = "path")
//    @PreAuthorize("@ss.hasPermi('road:check:remove')")
    @Log(title = "路面破损信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(roadCheckPCIService.deleteRoadCheckRecordByIds(ids));
    }

    /**
     * 根据道路ID获取路面破损信息
     */
    @ApiOperation(value = "根据道路ID获取路面破损信息", notes = "根据道路ID获取路面破损信息")
    @ApiImplicitParam(name = "roadId", value = "道路ID", required = true, dataType = "Long", paramType = "path")
//    @PreAuthorize("@ss.hasPermi('road:check:list')")
    @GetMapping("/roadId/{roadId}")
    public AjaxResult getByRoadId(@PathVariable("roadId") Long roadId) {
        List<RoadCheckPCI> list = roadCheckPCIService.selectRoadCheckRecordByRoadId(roadId);
        return AjaxResult.success(list);
    }

    /**
     * 导入路面破损数据
     */
    @ApiOperation(value = "导入路面破损数据", notes = "从Excel文件导入路面破损数据")
    @Anonymous
    @PostMapping("/pci/importData")
    public AjaxResult importData(@RequestPart("file") MultipartFile file, @RequestParam("roadId") Long roadId) throws Exception {
        BatchAddResponse response = roadCheckPCIService.batchImport(file, roadId);
        return AjaxResult.success("导入处理完成", response);
    }

    /**
     * 获取PCI导入模板
     */
    @ApiOperation(value = "获取PCI导入模板", notes = "下载路面破损状况（PCI）数据导入模板")
    @PostMapping("/pci/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException {
        ClassPathResource resource = new ClassPathResource("static/pci-template.xlsx");
        InputStream inputStream = resource.getInputStream();
        String fileName = "路面破损状况（PCI）数据导入模板.xlsx";
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
        FileCopyUtils.copy(inputStream, response.getOutputStream());
    }

    /**
     * 导出PCI数据（分上行下行Sheet）
     */
    @ApiOperation(value = "导出PCI数据", notes = "导出路面破损状况（PCI）数据，分上行下行两个Sheet")
//    @PreAuthorize("@ss.hasPermi('road:check:export')")
    @PostMapping("/pci/export")
    public void exportPCIByDirection(HttpServletResponse response, @RequestParam("roadId") Long roadId) {
        roadCheckPCIService.exportPCIByDirection(response, roadId);
    }


    @ApiOperation(value = "导出Word报告PCI", notes = "导出路面破损状况（PCI）word报告，分上行下行两个Word")
//    @PreAuthorize("@ss.hasPermi('road:check:export')")
    @PostMapping("/pci/exportWordPCI")
    public void exportWordPCI(HttpServletResponse response, 
                             @RequestParam("roadId") Long roadId,
                             @RequestParam(value = "teamId", required = false) Long teamId,
                             @RequestParam(value = "dateTime", required = false) String dateTime,
                             @RequestParam(value = "monthDate", required = false) String monthDate,
                             @RequestParam(value = "titleName", required = false) String titleName,
                             @RequestParam(value = "checkName", required = false) String checkName,
                             @RequestParam(value = "reviewName", required = false) String reviewName) {
        roadCheckPCIService.exportWordPCIByDirection(response, roadId, teamId, dateTime, monthDate, titleName, checkName, reviewName);
    }
} 