package com.tunnel.mapper;

import com.tunnel.domain.RoadCheckPBI;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 路面跳车检测信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
public interface RoadCheckPBIMapper {
    /**
     * 查询路面跳车检测信息
     *
     * @param id 路面跳车检测信息主键
     * @return 路面跳车检测信息
     */
    public RoadCheckPBI selectRoadCheckBumpById(Long id);

    /**
     * 查询路面跳车检测信息列表
     *
     * @param roadCheckPBI 路面跳车检测信息
     * @return 路面跳车检测信息集合
     */
    public List<RoadCheckPBI> selectRoadCheckBumpList(RoadCheckPBI roadCheckPBI);

    /**
     * 分页查询路面跳车检测信息列表
     *
     * @param roadCheckPBI 路面跳车检测信息
     * @param offset 起始位置
     * @param limit 查询条数
     * @return 路面跳车检测信息集合
     */
    public List<RoadCheckPBI> selectRoadCheckBumpListByPage(@Param("roadCheckBump") RoadCheckPBI roadCheckPBI, @Param("offset") int offset, @Param("limit") int limit);

    /**
     * 新增路面跳车检测信息
     *
     * @param roadCheckPBI 路面跳车检测信息
     * @return 结果
     */
    public int insertRoadCheckBump(RoadCheckPBI roadCheckPBI);

    /**
     * 修改路面跳车检测信息
     *
     * @param roadCheckPBI 路面跳车检测信息
     * @return 结果
     */
    public int updateRoadCheckBump(RoadCheckPBI roadCheckPBI);

    /**
     * 删除路面跳车检测信息
     *
     * @param id 路面跳车检测信息主键
     * @return 结果
     */
    public int deleteRoadCheckBumpById(Long id);

    /**
     * 批量删除路面跳车检测信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRoadCheckBumpByIds(Long[] ids);

    /**
     * 批量新增路面跳车检测数据
     *
     * @param list 路面跳车检测信息列表
     * @return 结果
     */
    public int batchInsert(List<RoadCheckPBI> list);
    
    /**
     * 根据道路ID获取路面跳车检测信息
     *
     * @param roadId 道路ID
     * @return 路面跳车检测信息集合
     */
    public List<RoadCheckPBI> selectRoadCheckBumpByRoadId(Long roadId);
    
    /**
     * 获取记录总数
     *
     * @param roadCheckPBI 查询条件
     * @return 总记录数
     */
    public int countRoadCheckBump(RoadCheckPBI roadCheckPBI);

    /**
     * 根据道路ID删除路面跳车检测信息
     *
     * @param roadId 道路ID
     * @return 结果
     */
    public int deleteRoadCheckBumpByRoadId(Long roadId);
} 