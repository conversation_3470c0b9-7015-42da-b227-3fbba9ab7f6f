package com.tunnel.service;

import com.tunnel.domain.Road;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 基础项目路线信息Service接口
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
public interface RoadService {
    /**
     * 查询基础项目路线信息
     *
     * @param id 基础项目路线信息主键
     * @return 基础项目路线信息
     */
    public Road selectRoadById(Long id);

    /**
     * 查询基础项目路线信息列表
     *
     * @param road 基础项目路线信息
     * @return 基础项目路线信息集合
     */
    public List<Road> selectRoadList(Road road);

    /**
     * 新增基础项目路线信息
     *
     * @param road 基础项目路线信息
     * @return 结果
     */
    public int insertRoad(Road road);

    /**
     * 修改基础项目路线信息
     *
     * @param road 基础项目路线信息
     * @return 结果
     */
    public int updateRoad(Road road);

    /**
     * 批量删除基础项目路线信息
     *
     * @param ids 需要删除的基础项目路线信息主键集合
     * @return 结果
     */
    public int deleteRoadByIds(Long[] ids);

    /**
     * 删除基础项目路线信息信息
     *
     * @param id 基础项目路线信息主键
     * @return 结果
     */
    public int deleteRoadById(Long id);

    /**
     * 根据路线编号查询路线信息
     *
     * @param roadCode 路线编号
     * @return 路线信息
     */
    public Road selectRoadByCode(String roadCode);

    /**
     * 根据用户ID查询路线信息列表
     *
     * @param userId 用户ID
     * @return 路线信息集合
     */
    public List<Road> selectRoadListByUserId(Long userId);

    /**
     * 根据部门ID查询路线信息列表
     *
     * @param deptId 部门ID
     * @return 路线信息集合
     */
    public List<Road> selectRoadListByDeptId(Long deptId);

    /**
     * 校验路线编号是否唯一
     *
     * @param road 路线信息
     * @return 结果
     */
    public String checkRoadCodeUnique(Road road);

    /**
     * 导出基础项目路线信息列表
     *
     * @param response HTTP响应
     * @param road 查询条件
     */
    public void exportRoad(HttpServletResponse response, Road road);

    /**
     * 批量导入路线数据
     *
     * @param file 导入文件
     * @return 导入结果
     */
    public String importRoad(MultipartFile file);

    /**
     * 下载导入模板
     *
     * @param response HTTP响应
     */
    public void importTemplate(HttpServletResponse response);

    /**
     * 获取路线信息记录数
     *
     * @param road 查询条件
     * @return 记录数
     */
    public int countRoad(Road road);
} 