package com.tunnel.service;

import com.tunnel.domain.BatchAddResponse;
import com.tunnel.domain.RoadCheckPCI;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 路面破损信息Service接口
 *
 * <AUTHOR>
 * @date 2024-11-28
 */
public interface RoadCheckPCIService {
    /**
     * 查询路面破损信息
     *
     * @param id 路面破损信息主键
     * @return 路面破损信息
     */
    public RoadCheckPCI selectRoadCheckRecordById(Long id);

    /**
     * 查询路面破损信息列表
     *
     * @param roadCheckPCI 路面破损信息
     * @return 路面破损信息集合
     */
    public List<RoadCheckPCI> selectRoadCheckRecordList(RoadCheckPCI roadCheckPCI);

    /**
     * 新增路面破损信息
     *
     * @param roadCheckPCI 路面破损信息
     * @return 结果
     */
    public int insertRoadCheckRecord(RoadCheckPCI roadCheckPCI);

    /**
     * 修改路面破损信息
     *
     * @param roadCheckPCI 路面破损信息
     * @return 结果
     */
    public int updateRoadCheckRecord(RoadCheckPCI roadCheckPCI);

    /**
     * 批量删除路面破损信息
     *
     * @param ids 需要删除的路面破损信息主键集合
     * @return 结果
     */
    public int deleteRoadCheckRecordByIds(Long[] ids);

    /**
     * 删除路面破损信息信息
     *
     * @param id 路面破损信息主键
     * @return 结果
     */
    public int deleteRoadCheckRecordById(Long id);

    /**
     * 根据道路ID获取路面破损信息
     *
     * @param roadId 道路ID
     * @return 路面破损信息集合
     */
    public List<RoadCheckPCI> selectRoadCheckRecordByRoadId(Long roadId);

    /**
     * 批量导入路面破损状况检测数据
     *
     * @param file 导入文件
     * @param roadId 道路ID
     * @return 导入结果
     */
    public BatchAddResponse batchImport(MultipartFile file, Long roadId);
    
    /**
     * 导出路面破损信息列表（大数据量优化）
     *
     * @param response HTTP响应
     * @param roadCheckPCI 查询条件
     */
    public void exportOptimized(HttpServletResponse response, RoadCheckPCI roadCheckPCI);
    
    /**
     * 获取符合条件的总记录数
     *
     * @param roadCheckPCI 查询条件
     * @return 总记录数
     */
    public int countRoadCheckRecord(RoadCheckPCI roadCheckPCI);

    /**
     * 导出PCI数据（分上行下行Sheet）
     *
     * @param response HTTP响应
     * @param roadId 道路ID
     */
    public void exportPCIByDirection(HttpServletResponse response, Long roadId);

    /**
     * 导出Word版PCI报告（分上行下行）
     *
     * @param response HTTP响应
     * @param roadId 路线ID
     */
    void exportWordPCIByDirection(HttpServletResponse response, Long roadId);

    /**
     * 导出Word版PCI报告（分上行下行）
     *
     * @param response HTTP响应
     * @param roadId 路线ID
     * @param teamId 检测分组ID
     * @param dateTime 检测日期时间
     * @param monthDate 月份日期
     * @param titleName 页眉标题名称
     * @param checkName 检测人员姓名
     * @param reviewName 审核人员姓名
     */
    void exportWordPCIByDirection(HttpServletResponse response, Long roadId, Long teamId, String dateTime, String monthDate, String titleName, String checkName, String reviewName);

    /**
     * 根据道路ID删除路面破损信息
     *
     * @param roadId 道路ID
     * @return 结果
     */
    public int deleteRoadCheckRecordByRoadId(Long roadId);
}