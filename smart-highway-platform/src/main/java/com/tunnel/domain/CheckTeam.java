package com.tunnel.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * 检测分组对象 sc_check_team
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CheckTeam extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 分组名称 */
    @Excel(name = "分组名称")
    @NotBlank(message = "分组名称不能为空")
    @Size(max = 100, message = "分组名称长度不能超过100个字符")
    private String teamName;

    /** 分组描述 */
    @Excel(name = "分组描述")
    @Size(max = 255, message = "分组描述长度不能超过255个字符")
    private String description;

    /** 路线类型:1.高速,2.国省道 */
    @Excel(name = "路线类型", readConverterExp = "1=高速,2=国省道")
    private Integer type;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 创建人 */
    private Long creator;

    /** 更新人 */
    private Long modifier;

    /** 人员列表 - 非数据库字段，用于前端展示 */
    private List<CheckUser> userList;

    /** 人员数量 - 非数据库字段，用于前端展示 */
    private Integer userCount;
} 