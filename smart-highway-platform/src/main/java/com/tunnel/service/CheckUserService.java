package com.tunnel.service;

import com.tunnel.domain.CheckUser;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 检测人员Service接口
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
public interface CheckUserService {
    /**
     * 查询检测人员
     *
     * @param id 检测人员主键
     * @return 检测人员
     */
    public CheckUser selectCheckUserById(Long id);

    /**
     * 查询检测人员列表
     *
     * @param checkUser 检测人员
     * @return 检测人员集合
     */
    public List<CheckUser> selectCheckUserList(CheckUser checkUser);

    /**
     * 新增检测人员
     *
     * @param checkUser 检测人员
     * @return 结果
     */
    public int insertCheckUser(CheckUser checkUser);

    /**
     * 修改检测人员
     *
     * @param checkUser 检测人员
     * @return 结果
     */
    public int updateCheckUser(CheckUser checkUser);

    /**
     * 批量删除检测人员
     *
     * @param ids 需要删除的检测人员主键集合
     * @return 结果
     */
    public int deleteCheckUserByIds(Long[] ids);

    /**
     * 删除检测人员信息
     *
     * @param id 检测人员主键
     * @return 结果
     */
    public int deleteCheckUserById(Long id);

    /**
     * 根据分组ID查询检测人员列表
     *
     * @param teamId 分组ID
     * @return 检测人员集合
     */
    public List<CheckUser> selectCheckUserListByTeamId(Long teamId);

    /**
     * 根据职位查询检测人员列表
     *
     * @param position 职位
     * @return 检测人员集合
     */
    public List<CheckUser> selectCheckUserListByPosition(String position);

    /**
     * 根据分组ID和职位查询检测人员列表
     *
     * @param teamId 分组ID
     * @param position 职位
     * @return 检测人员集合
     */
    public List<CheckUser> selectCheckUserListByTeamIdAndPosition(Long teamId, String position);

    /**
     * 导出检测人员数据
     *
     * @param response 响应对象
     * @param checkUser 查询条件
     */
    public void exportCheckUser(HttpServletResponse response, CheckUser checkUser);

    /**
     * 批量导入检测人员数据
     *
     * @param file Excel文件
     * @return 导入结果信息
     */
    public String importCheckUser(MultipartFile file);

    /**
     * 下载导入模板
     *
     * @param response 响应对象
     */
    public void importTemplate(HttpServletResponse response);

    /**
     * 获取下一个排序号
     *
     * @return 下一个排序号
     */
    public Integer getNextSortNum();

    /**
     * 批量更新人员排序
     *
     * @param checkUserList 检测人员列表
     * @return 结果
     */
    public int batchUpdateSort(List<CheckUser> checkUserList);
} 