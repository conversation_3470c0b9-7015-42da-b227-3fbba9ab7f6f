-- ----------------------------
-- 1、路面跳车检测信息表
-- ----------------------------
DROP TABLE IF EXISTS `road_check_bump`;
CREATE TABLE `road_check_bump` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `road_id` bigint(20) NOT NULL COMMENT '道路ID',
  `start_code` varchar(50) NOT NULL COMMENT '起始桩号(km)',
  `end_code` varchar(50) NOT NULL COMMENT '结束桩号(km)',
  `bpl` decimal(10,2) DEFAULT NULL COMMENT '低频跳车',
  `bpm` decimal(10,2) DEFAULT NULL COMMENT '中频跳车',
  `bph` decimal(10,2) DEFAULT NULL COMMENT '高频跳车',
  `bump_height` decimal(10,2) DEFAULT NULL COMMENT '跳车高度差(cm)',
  `pbi` decimal(10,2) DEFAULT NULL COMMENT '路面平整度指数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` bigint(20) DEFAULT NULL COMMENT '创建人',
  `modifier` bigint(20) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  KEY `idx_road_id` (`road_id`),
  KEY `idx_start_code` (`start_code`),
  KEY `idx_end_code` (`end_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='路面跳车检测信息表';

-- ----------------------------
-- 2、路面跳车检测信息表的菜单权限
-- ----------------------------
-- 菜单 SQL
INSERT INTO sys_menu(menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('路面跳车检测管理', 2000, 11, 'bump', 'platform/roadCheck/bump/index', 1, 0, 'C', '0', '0', 'road:check:bump:list', 'tool', 'admin', sysdate(), '', null, '路面跳车检测信息菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu(menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('路面跳车检测查询', @parentId, 1, '#', '', 1, 0, 'F', '0', '0', 'road:check:bump:query', '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu(menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('路面跳车检测新增', @parentId, 2, '#', '', 1, 0, 'F', '0', '0', 'road:check:bump:add', '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu(menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('路面跳车检测修改', @parentId, 3, '#', '', 1, 0, 'F', '0', '0', 'road:check:bump:edit', '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu(menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('路面跳车检测删除', @parentId, 4, '#', '', 1, 0, 'F', '0', '0', 'road:check:bump:remove', '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu(menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('路面跳车检测导出', @parentId, 5, '#', '', 1, 0, 'F', '0', '0', 'road:check:bump:export', '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu(menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('路面跳车检测导入', @parentId, 6, '#', '', 1, 0, 'F', '0', '0', 'road:check:bump:import', '#', 'admin', sysdate(), '', null, ''); 