package com.tunnel.domain.template;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;

/**
 * 基础数据对象 sc_road
 * 
 * <AUTHOR>
 * @date 2024-06-18
 */
@Data
public class EvaluateRoadSheetOne extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 唯一主键 */
    private Long id;
    //任务ID
    private Long taskId;

    @Excel(name = "序号")
    private String code;

    /** 路线编号 */
    @Excel(name = "路线编号")
    private String roadCode;

    /** 路线名称 */
    @Excel(name = "路线名称")
    private String roadName;

    /** 管养单位 */
    @Excel(name = "管养单位")
    private String companyName;

    /** 起点桩号 */
    @Excel(name = "起点桩号")
    private String startCode;

    /** 迄点桩号 */
    @Excel(name = "讫点桩号")
    private String endCode;

    /** 里程(km) */
    @Excel(name = "里程(km)")
    private BigDecimal mileage;

    /** 里程(km) */
    @Excel(name = "所在县市区")
    private String city;

    @Excel(name = "检查起点")
    private String startCheckCode;

    @Excel(name = "检查终点")
    private String endCheckCode;

    @Excel(name = "幅别")
    private Integer direction;

    @Excel(name = "服务区")
    private String serviceArea;

    @Excel(name = "收费站")
    private String feeArea;

    @Excel(name = "隧道")
    private String tunnel;

    @Excel(name = "中央分隔带")
    private BigDecimal centerDivideScore=BigDecimal.ZERO;

    @Excel(name = "路侧边坡")
    private BigDecimal roadSideScore=BigDecimal.ZERO;

    @Excel(name = "互通区及收费站")
    private BigDecimal feeAreaScore=BigDecimal.ZERO;

    @Excel(name = "服务区得分")
    private BigDecimal serviceAreaScore=BigDecimal.ZERO;

    @Excel(name = "隧道出入口")
    private BigDecimal tunnelScore=BigDecimal.ZERO;

    @Excel(name = "桥梁及桥下空间")
    private BigDecimal bridgeScore=BigDecimal.ZERO;

    @Excel(name = "路基路面及交安设施")
    private BigDecimal roadUpScore=BigDecimal.ZERO;

    @Excel(name = "公路用地分数合计")
    private BigDecimal roadTotalScore=BigDecimal.ZERO;



    @Excel(name = "中央分隔带数量")
    private Integer centerDivideCount=0;

    @Excel(name = "路侧边坡数量")
    private Integer roadSideCount=0;

    @Excel(name = "互通区及收费站数量")
    private Integer feeAreaCount=0;

    @Excel(name = "服务区得分数量")
    private Integer serviceAreaCount=0;

    @Excel(name = "隧道出入口数量")
    private Integer tunnelCount=0;

    @Excel(name = "桥梁及桥下空间数量")
    private Integer bridgeCount=0;

    @Excel(name = "路基路面及交安设施数量")
    private Integer roadUpCount=0;

    @Excel(name = "公路用地数量合计")
    private Integer roadTotalCount=0;



    @Excel(name = "清理建筑控制区范围内杂物、白色垃圾等")
    private BigDecimal buildingOneScore=BigDecimal.ZERO;

    @Excel(name = "加强树木维护、管养，适时修剪")
    private BigDecimal buildingTwoScore=BigDecimal.ZERO;

    @Excel(name = "无乱搭乱建临时棚屋、活动板房、栅栏等建（构）筑物")
    private BigDecimal buildingThreeScore=BigDecimal.ZERO;

    @Excel(name = "结合实际对破损、废弃建（构）筑物采取遮挡、粉刷或拆除等措施")
    private BigDecimal buildingFourScore=BigDecimal.ZERO;

    @Excel(name = "生态环境保护")
    private BigDecimal buildingFiveScore=BigDecimal.ZERO;

    @Excel(name = "建筑用地总分")
    private BigDecimal buildingTotalScore=BigDecimal.ZERO;



    @Excel(name = "清理建筑控制区范围内杂物、白色垃圾等")
    private Integer buildingOneCount=0;

    @Excel(name = "加强树木维护、管养，适时修剪")
    private Integer buildingTwoCount=0;

    @Excel(name = "无乱搭乱建临时棚屋、活动板房、栅栏等建（构）筑物")
    private Integer buildingThreeCount=0;

    @Excel(name = "结合实际对破损、废弃建（构）筑物采取遮挡、粉刷或拆除等措施")
    private Integer buildingFourCount=0;

    @Excel(name = "生态环境保护")
    private Integer buildingFiveCount=0;

    @Excel(name = "建筑用地总分")
    private Integer buildingTotalCount=0;

    @Excel(name = "检测桩号")
    private String checkCode;

    @Excel(name = "检测类别")
    private String checkType;

    @Excel(name = "检测项目")
    private String checkProject;

    @Excel(name = "检测内容")
    private String checkContent;

    @Excel(name = "问题类型")
    private String questionType;

    @Excel(name = "公路设施维护")
    private String roadMaintenance;

    @Excel(name = "公路沿线保洁")
    private String roadClean;

    @Excel(name = "隧道设施管理")
    private String tunnelManage;

    @Excel(name = "公路沿线绿化")
    private String roadGreen;

    @Excel(name = "标志标线管理")
    private String tagManage;


    @Excel(name = "公路设施维护")
    private BigDecimal roadMaintenanceScore=BigDecimal.ZERO;

    @Excel(name = "公路沿线保洁")
    private BigDecimal roadCleanScore=BigDecimal.ZERO;

    @Excel(name = "隧道设施管理")
    private BigDecimal tunnelManageScore=BigDecimal.ZERO;

    @Excel(name = "公路沿线绿化")
    private BigDecimal roadGreenScore=BigDecimal.ZERO;

    @Excel(name = "标志标线管理")
    private BigDecimal tagManageScore=BigDecimal.ZERO;

    @Excel(name = "路域环境总分")
    private BigDecimal roadEnvScore=BigDecimal.ZERO;


    @Excel(name = "公路设施维护")
    private Integer roadMaintenanceCount=0;

    @Excel(name = "公路沿线保洁")
    private Integer roadCleanCount=0;

    @Excel(name = "隧道设施管理")
    private Integer tunnelManageCount=0;

    @Excel(name = "公路沿线绿化")
    private Integer roadGreenCount=0;

    @Excel(name = "标志标线管理")
    private Integer tagManageCount=0;

    @Excel(name = "路域环境总分")
    private Integer roadEnvCount=0;

    @Excel(name = "现场照片1")
    private String scenePic1;

    @Excel(name = "工作照片")
    private String workPic;

    @Excel(name = "任务月份")
    private String taskDate;

    /**
     * 1.专项检测  2.路域环境
     */
    private Integer type;

    /**
     * 检测录入的备注
     */
    private String remark;
    /**
     * 检测名称
     */
    private String name;

}
