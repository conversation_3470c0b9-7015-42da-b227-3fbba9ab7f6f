package com.tunnel.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;
import com.tunnel.domain.Road;

import java.util.ArrayList;
import java.util.List;

/**
 * Word文档公共处理工具类
 * 用于处理各个分册Word模板中的占位符替换
 * 
 * <AUTHOR>
 * @date 2024-12-09
 */
@Slf4j
public class WordDocumentUtils {

    /**
     * 替换文档,指定字体样式
     * @param document Word文档对象
     * @param placeholder 占位符（如：${number}）
     * @param value 替换值
     * @param fontFamily 字体族（可选，为null时使用原有逻辑）
     * @param fontSize 字体大小（可选，为null时使用原有逻辑）
     */
    public static void replaceTextInDocumentWithFontStyle(XWPFDocument document, String placeholder, String value, String fontFamily, Integer fontSize){
        // 如果没有指定字体样式，使用原有逻辑
        if (fontFamily == null && fontSize == null) {
            replaceTextInDocument(document, placeholder, value);
            return;
        }
        
        // 如果指定了字体样式，使用新的替换逻辑
        replaceTextInDocumentWithCustomFont(document, placeholder, value, fontFamily, fontSize);
    }

    /**
     * 替换Word文档中的占位符，使用指定的字体样式
     * 
     * @param document Word文档对象
     * @param placeholder 占位符（如：${number}）
     * @param value 替换值
     * @param fontFamily 字体族
     * @param fontSize 字体大小
     */
    private static void replaceTextInDocumentWithCustomFont(XWPFDocument document, String placeholder, String value, String fontFamily, Integer fontSize) {
        try {
            // 替换段落中的文本
            for (XWPFParagraph paragraph : document.getParagraphs()) {
                replaceParagraphTextWithCustomFont(paragraph, placeholder, value, fontFamily, fontSize);
            }

            // 替换表格中的文本
            for (XWPFTable table : document.getTables()) {
                for (XWPFTableRow row : table.getRows()) {
                    for (XWPFTableCell cell : row.getTableCells()) {
                        for (XWPFParagraph paragraph : cell.getParagraphs()) {
                            replaceParagraphTextWithCustomFont(paragraph, placeholder, value, fontFamily, fontSize);
                        }
                    }
                }
            }

            // 替换页眉中的文本
            for (XWPFHeader header : document.getHeaderList()) {
                for (XWPFParagraph paragraph : header.getParagraphs()) {
                    replaceParagraphTextWithCustomFont(paragraph, placeholder, value, fontFamily, fontSize);
                }

                for (XWPFTable table : header.getTables()) {
                    for (XWPFTableRow row : table.getRows()) {
                        for (XWPFTableCell cell : row.getTableCells()) {
                            for (XWPFParagraph paragraph : cell.getParagraphs()) {
                                replaceParagraphTextWithCustomFont(paragraph, placeholder, value, fontFamily, fontSize);
                            }
                        }
                    }
                }
            }

            // 替换页脚中的文本
            for (XWPFFooter footer : document.getFooterList()) {
                for (XWPFParagraph paragraph : footer.getParagraphs()) {
                    replaceParagraphTextWithCustomFont(paragraph, placeholder, value, fontFamily, fontSize);
                }

                for (XWPFTable table : footer.getTables()) {
                    for (XWPFTableRow row : table.getRows()) {
                        for (XWPFTableCell cell : row.getTableCells()) {
                            for (XWPFParagraph paragraph : cell.getParagraphs()) {
                                replaceParagraphTextWithCustomFont(paragraph, placeholder, value, fontFamily, fontSize);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("替换文档占位符失败 [{}]: {}", placeholder, e.getMessage());
        }
    }

    /**
     * 替换段落中的文本，使用指定的字体样式
     */
    private static void replaceParagraphTextWithCustomFont(XWPFParagraph paragraph, String placeholder, String value, String fontFamily, Integer fontSize) {
        try {
            String text = paragraph.getText();
            if (text != null && text.contains(placeholder)) {
                String newText = text.replace(placeholder, value != null ? value : "");

                // 保存第一个run的字体格式信息（如果存在的话）
                XWPFRun firstRun = null;
                String originalFontFamily = null;
                Integer originalFontSize = null;
                Boolean isBold = null;
                Boolean isItalic = null;
                String color = null;
                
                if (paragraph.getRuns().size() > 0) {
                    firstRun = paragraph.getRuns().get(0);
                    originalFontFamily = firstRun.getFontFamily();
                    originalFontSize = firstRun.getFontSize();
                    isBold = firstRun.isBold();
                    isItalic = firstRun.isItalic();
                    color = firstRun.getColor();
                }

                // 清除所有现有的runs
                int runs = paragraph.getRuns().size();
                for (int i = runs - 1; i >= 0; i--) {
                    paragraph.removeRun(i);
                }

                // 创建新的run并设置替换后的文本
                XWPFRun run = paragraph.createRun();
                run.setText(newText);
                
                // 应用指定的字体样式，如果未指定则使用原有样式
                if (fontFamily != null) {
                    run.setFontFamily(fontFamily);
                } else if (originalFontFamily != null) {
                    run.setFontFamily(originalFontFamily);
                }
                
                if (fontSize != null && fontSize > 0) {
                    run.setFontSize(fontSize);
                } else if (originalFontSize != null && originalFontSize > 0) {
                    run.setFontSize(originalFontSize);
                }
                
                if (isBold != null) {
                    run.setBold(isBold);
                }
                if (isItalic != null) {
                    run.setItalic(isItalic);
                }
                if (color != null && !color.isEmpty()) {
                    run.setColor(color);
                }
            }
        } catch (Exception e) {
            log.warn("替换段落文本失败: {}", e.getMessage());
        }
    }

    /**
     * 替换Word文档中的占位符
     * 
     * @param document Word文档对象
     * @param placeholder 占位符（如：${number}）
     * @param value 替换值
     */
    public static void replaceTextInDocument(XWPFDocument document, String placeholder, String value) {
        try {
            // 替换段落中的文本
            for (XWPFParagraph paragraph : document.getParagraphs()) {
                replaceParagraphText(paragraph, placeholder, value);
            }

            // 替换表格中的文本
            for (XWPFTable table : document.getTables()) {
                for (XWPFTableRow row : table.getRows()) {
                    for (XWPFTableCell cell : row.getTableCells()) {
                        for (XWPFParagraph paragraph : cell.getParagraphs()) {
                            replaceParagraphText(paragraph, placeholder, value);
                        }
                    }
                }
            }

            // 替换页眉中的文本
            for (XWPFHeader header : document.getHeaderList()) {
                for (XWPFParagraph paragraph : header.getParagraphs()) {
                    replaceParagraphText(paragraph, placeholder, value);
                }

                for (XWPFTable table : header.getTables()) {
                    for (XWPFTableRow row : table.getRows()) {
                        for (XWPFTableCell cell : row.getTableCells()) {
                            for (XWPFParagraph paragraph : cell.getParagraphs()) {
                                replaceParagraphText(paragraph, placeholder, value);
                            }
                        }
                    }
                }
            }

            // 替换页脚中的文本
            for (XWPFFooter footer : document.getFooterList()) {
                for (XWPFParagraph paragraph : footer.getParagraphs()) {
                    replaceParagraphText(paragraph, placeholder, value);
                }

                for (XWPFTable table : footer.getTables()) {
                    for (XWPFTableRow row : table.getRows()) {
                        for (XWPFTableCell cell : row.getTableCells()) {
                            for (XWPFParagraph paragraph : cell.getParagraphs()) {
                                replaceParagraphText(paragraph, placeholder, value);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("替换文档占位符失败 [{}]: {}", placeholder, e.getMessage());
        }
    }

    /**
     * 替换段落中的文本
     */
    private static void replaceParagraphText(XWPFParagraph paragraph, String placeholder, String value) {
        try {
            String text = paragraph.getText();
            if (text != null && text.contains(placeholder)) {
                String newText = text.replace(placeholder, value != null ? value : "");

                // 保存第一个run的字体格式信息（如果存在的话）
                XWPFRun firstRun = null;
                String fontFamily = null;
                Integer fontSize = null;
                Boolean isBold = null;
                Boolean isItalic = null;
                String color = null;
                
                if (paragraph.getRuns().size() > 0) {
                    firstRun = paragraph.getRuns().get(0);
                    fontFamily = firstRun.getFontFamily();
                    fontSize = firstRun.getFontSize();
                    isBold = firstRun.isBold();
                    isItalic = firstRun.isItalic();
                    color = firstRun.getColor();
                }

                // 清除所有现有的runs
                int runs = paragraph.getRuns().size();
                for (int i = runs - 1; i >= 0; i--) {
                    paragraph.removeRun(i);
                }

                // 创建新的run并设置替换后的文本
                XWPFRun run = paragraph.createRun();
                run.setText(newText);
                
                // 应用保存的字体格式
                if (fontFamily != null) {
                    run.setFontFamily(fontFamily);
                }
                if (fontSize != null && fontSize > 0) {
                    run.setFontSize(fontSize);
                }
                if (isBold != null) {
                    run.setBold(isBold);
                }
                if (isItalic != null) {
                    run.setItalic(isItalic);
                }
                if (color != null && !color.isEmpty()) {
                    run.setColor(color);
                }
            }
        } catch (Exception e) {
            log.warn("替换段落文本失败: {}", e.getMessage());
        }
    }

    /**
     * 在段落中替换文本，完全保持原有格式
     * 这个方法逐个run进行处理，确保格式不被破坏
     */
    private static void replaceTextInParagraphPreservingFormat(XWPFParagraph paragraph, String placeholder, String replacement) {
        try {
            List<XWPFRun> runs = paragraph.getRuns();
            if (runs == null || runs.isEmpty()) {
                return;
            }

            // 构建完整的段落文本，同时记录每个字符对应的run索引
            StringBuilder fullText = new StringBuilder();
            List<Integer> charToRunMap = new ArrayList<>();
            
            for (int runIndex = 0; runIndex < runs.size(); runIndex++) {
                XWPFRun run = runs.get(runIndex);
                String runText = run.getText(0);
                if (runText != null) {
                    fullText.append(runText);
                    for (int i = 0; i < runText.length(); i++) {
                        charToRunMap.add(runIndex);
                    }
                }
            }

            String originalText = fullText.toString();
            if (!originalText.contains(placeholder)) {
                return;
            }

            // 找到占位符的位置
            int placeholderStart = originalText.indexOf(placeholder);
            int placeholderEnd = placeholderStart + placeholder.length();

            // 确定占位符所在的run范围
            if (placeholderStart >= 0 && placeholderStart < charToRunMap.size()) {
                int targetRunIndex = charToRunMap.get(placeholderStart);
                XWPFRun targetRun = runs.get(targetRunIndex);

                // 保存目标run的所有格式信息
                String fontFamily = targetRun.getFontFamily();
                Integer fontSize = targetRun.getFontSize();
                Boolean isBold = targetRun.isBold();
                Boolean isItalic = targetRun.isItalic();
                String color = targetRun.getColor();
                Boolean isUnderlined = targetRun.getUnderline() != null;

                // 替换文本
                String newText = originalText.replace(placeholder, replacement);

                // 清除所有run的文本
                for (XWPFRun run : runs) {
                    run.setText("", 0);
                }

                // 将新文本设置到目标run中，保持其格式
                targetRun.setText(newText, 0);

                // 确保格式完全保持
                if (fontFamily != null) {
                    targetRun.setFontFamily(fontFamily);
                }
                if (fontSize != null && fontSize > 0) {
                    targetRun.setFontSize(fontSize);
                }
                if (isBold != null) {
                    targetRun.setBold(isBold);
                }
                if (isItalic != null) {
                    targetRun.setItalic(isItalic);
                }
                if (color != null && !color.isEmpty()) {
                    targetRun.setColor(color);
                }
                if (isUnderlined != null && isUnderlined) {
                    targetRun.setUnderline(targetRun.getUnderline());
                }

                log.debug("页眉标题替换成功：{} -> {}，保持原格式（字体: {}, 大小: {}）", 
                         placeholder, replacement, fontFamily, fontSize);
            }
        } catch (Exception e) {
            log.warn("保持格式的文本替换失败: {}", e.getMessage());
            // 如果精确替换失败，回退到简单替换
            try {
                String text = paragraph.getText();
                if (text != null && text.contains(placeholder)) {
                    // 简单的整体替换作为备用方案
                    for (XWPFRun run : paragraph.getRuns()) {
                        String runText = run.getText(0);
                        if (runText != null && runText.contains(placeholder)) {
                            run.setText(runText.replace(placeholder, replacement), 0);
                            break;
                        }
                    }
                }
            } catch (Exception fallbackException) {
                log.warn("备用文本替换也失败: {}", fallbackException.getMessage());
            }
        }
    }

    /**
     * 获取分册编号
     *
     * @param bookType 分册类型（PCI/RQI/RDI/PBI/SRI）
     * @return 分册编号
     */
    public static String getBookNumber(String bookType) {
        switch (bookType) {
            case "PCI":
                return "01";
            case "RQI":
                return "02";
            case "RDI":
                return "03";  
            case "PBI":
                return "04";
            case "SRI":
                return "05";
            default:
                log.warn("未知的分册类型: {}", bookType);
                return "00";
        }
    }

    /**
     * 替换页眉中的占位符，使用特殊字体规则
     * 中文使用宋体，数字和英文使用Times New Roman
     * 
     * @param document Word文档对象
     * @param placeholder 占位符
     * @param value 替换值
     */
    private static void replaceHeaderPlaceholderWithSpecialFont(XWPFDocument document, String placeholder, String value) {
        if (value == null || value.trim().isEmpty()) {
            return;
        }
        
        try {
            for (XWPFHeader header : document.getHeaderList()) {
                for (XWPFParagraph paragraph : header.getParagraphs()) {
                    replaceParagraphTextWithMixedFont(paragraph, placeholder, value);
                }

                for (XWPFTable table : header.getTables()) {
                    for (XWPFTableRow row : table.getRows()) {
                        for (XWPFTableCell cell : row.getTableCells()) {
                            for (XWPFParagraph paragraph : cell.getParagraphs()) {
                                replaceParagraphTextWithMixedFont(paragraph, placeholder, value);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("替换页眉占位符失败 [{}]: {}", placeholder, e.getMessage());
        }
    }

    /**
     * 替换段落中的文本，使用混合字体（中文宋体，数字英文Times New Roman）
     * 保留图片和其他非文本内容
     */
    private static void replaceParagraphTextWithMixedFont(XWPFParagraph paragraph, String placeholder, String value) {
        try {
            // 先尝试精确替换，只替换包含占位符的runs
            if (replaceTextInSpecificRuns(paragraph, placeholder, value)) {
                return; // 如果精确替换成功，直接返回
            }

            // 如果精确替换失败，使用保守的方法：只处理文本，保留图片
            String text = paragraph.getText();
            if (text != null && text.contains(placeholder)) {
                String newText = text.replace(placeholder, value != null ? value : "");

                // 保存原有格式信息
                Integer originalFontSize = null;
                Boolean isBold = null;
                Boolean isItalic = null;
                String color = null;

                List<XWPFRun> runs = paragraph.getRuns();

                // 找到第一个文本run的格式信息
                for (XWPFRun run : runs) {
                    if (!hasImages(run) && run.getText(0) != null && !run.getText(0).trim().isEmpty()) {
                        originalFontSize = run.getFontSize();
                        isBold = run.isBold();
                        isItalic = run.isItalic();
                        color = run.getColor();
                        break;
                    }
                }

                // 只清除文本runs，保留图片runs
                for (int i = runs.size() - 1; i >= 0; i--) {
                    XWPFRun run = runs.get(i);
                    if (!hasImages(run)) {
                        paragraph.removeRun(i);
                    }
                }

                // 在段落开头插入新的文本runs
                int insertPosition = 0;
                for (int i = 0; i < newText.length(); i++) {
                    char c = newText.charAt(i);
                    XWPFRun run = paragraph.insertNewRun(insertPosition++);
                    run.setText(String.valueOf(c));

                    // 设置字体
                    if (isChineseOrPunctuation(c)) {
                        // 中文字符和中文标点使用宋体
                        run.setFontFamily("宋体");
                    } else {
                        // 数字、英文、英文标点使用Times New Roman
                        run.setFontFamily("Times New Roman");
                    }

                    // 应用其他格式
                    if (originalFontSize != null && originalFontSize > 0) {
                        run.setFontSize(originalFontSize);
                    }
                    if (isBold != null) {
                        run.setBold(isBold);
                    }
                    if (isItalic != null) {
                        run.setItalic(isItalic);
                    }
                    if (color != null && !color.isEmpty()) {
                        run.setColor(color);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("替换段落文本失败: {}", e.getMessage());
        }
    }

    /**
     * 尝试在特定的runs中精确替换文本，不影响图片runs
     */
    private static boolean replaceTextInSpecificRuns(XWPFParagraph paragraph, String placeholder, String value) {
        try {
            List<XWPFRun> runs = paragraph.getRuns();
            boolean found = false;

            for (XWPFRun run : runs) {
                if (!hasImages(run)) {
                    String runText = run.getText(0);
                    if (runText != null && runText.contains(placeholder)) {
                        String newText = runText.replace(placeholder, value != null ? value : "");

                        // 保存原有格式
                        Integer fontSize = run.getFontSize();
                        Boolean isBold = run.isBold();
                        Boolean isItalic = run.isItalic();
                        String color = run.getColor();

                        // 清除当前run的文本
                        run.setText("", 0);

                        // 按字符重新设置文本和字体
                        StringBuilder currentText = new StringBuilder();
                        for (int i = 0; i < newText.length(); i++) {
                            char c = newText.charAt(i);
                            currentText.append(c);
                        }

                        // 简化处理：直接设置文本，使用混合字体策略
                        run.setText(newText, 0);

                        // 设置字体（这里简化处理，使用宋体作为主要字体）
                        run.setFontFamily("宋体");

                        // 恢复其他格式
                        if (fontSize != null && fontSize > 0) {
                            run.setFontSize(fontSize);
                        }
                        if (isBold != null) {
                            run.setBold(isBold);
                        }
                        if (isItalic != null) {
                            run.setItalic(isItalic);
                        }
                        if (color != null && !color.isEmpty()) {
                            run.setColor(color);
                        }

                        found = true;
                    }
                }
            }

            return found;
        } catch (Exception e) {
            log.warn("精确替换文本失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查run是否包含图片
     */
    private static boolean hasImages(XWPFRun run) {
        try {
            return run.getEmbeddedPictures() != null && !run.getEmbeddedPictures().isEmpty();
        } catch (Exception e) {
            // 如果检查失败，保守起见认为可能包含图片
            return false;
        }
    }

    /**
     * 判断字符是否为中文或中文标点
     */
    private static boolean isChineseOrPunctuation(char c) {
        // 中文字符范围
        if (c >= 0x4E00 && c <= 0x9FFF) {
            return true;
        }
        
        // 中文标点符号
        String chinesePunctuation = "，。！？；：\"\"''（）【】《》、…—";
        return chinesePunctuation.indexOf(c) != -1;
    }

    /**
     * 替换Word文档中的标准占位符（包括报告编号）
     * 
     * @param document Word文档对象
     * @param bookType 分册类型（PCI/RQI/RDI/PBI/SRI）
     * @param titleName 页眉标题名称
     * @param checkName 检测人员姓名
     * @param reviewName 审核人员姓名
     * @param road 路线对象（用于获取reportNo）
     */
    public static void replaceStandardPlaceholders(XWPFDocument document, String bookType, 
                                                  String titleName, String checkName, String reviewName, Road road) {
        try {
            // 获取分册编号
            String bookNumber = getBookNumber(bookType);
            
            // 替换分册编号（使用页眉特殊字体处理）
            replaceHeaderPlaceholderWithSpecialFont(document, "${number}", bookNumber);
            
            // 替换页眉标题（使用页眉特殊字体处理）
            if (titleName != null && !titleName.trim().isEmpty()) {
                replaceHeaderPlaceholderWithSpecialFont(document, "${titleName}", titleName);
            }
            
            // 替换检测人员（使用页脚特殊字体处理）
            if (checkName != null && !checkName.trim().isEmpty()) {
                replaceTextInDocument(document, "${checkName}", checkName);
            }
            
            // 替换审核人员（使用页脚特殊字体处理）
            if (reviewName != null && !reviewName.trim().isEmpty()) {
                replaceTextInDocument(document, "${reviewName}", reviewName);
            }
            
            // 替换报告编号（使用页眉特殊字体处理）
            if (road != null && road.getReportNo() != null && !road.getReportNo().trim().isEmpty()) {
                replaceHeaderPlaceholderWithSpecialFont(document, "${reportNo}", road.getReportNo());
            } else {
                // 如果没有设置报告编号，可以设置为空或默认值
                replaceHeaderPlaceholderWithSpecialFont(document, "${reportNo}", "");
            }
            
            log.info("完成标准占位符替换：分册={}, 编号={}, 报告编号={}", bookType, bookNumber, 
                    road != null ? road.getReportNo() : "未设置");
            
        } catch (Exception e) {
            log.error("替换标准占位符失败", e);
        }
    }

    /**
     * 替换Word文档中的标准占位符（兼容旧版本，不包含reportNo）
     * 
     * @param document Word文档对象
     * @param bookType 分册类型（PCI/RQI/RDI/PBI/SRI）
     * @param titleName 页眉标题名称
     * @param checkName 检测人员姓名
     * @param reviewName 审核人员姓名
     */
    public static void replaceStandardPlaceholders(XWPFDocument document, String bookType,
                                                  String titleName, String checkName, String reviewName) {
        replaceStandardPlaceholders(document, bookType, titleName, checkName, reviewName, null);
    }

    /**
     * 安全设置表格单元格文本并强制垂直居中（专用于符号显示）
     *
     * @param row 表格行
     * @param cellIndex 单元格索引
     * @param text 要设置的文本
     */
    public static void setCellTextWithVerticalCenter(XWPFTableRow row, int cellIndex, String text) {
        try {
            // 确保有足够的单元格
            while (row.getTableCells().size() <= cellIndex) {
                row.createCell();
            }

            XWPFTableCell cell = row.getCell(cellIndex);
            if (cell != null) {
                // 设置单元格边框
                setCellBorders(cell);

                // 设置行高以确保垂直居中效果更明显
                if (row.getHeight() == 0) {
                    row.setHeight(400); // 设置行高为400 twips (约0.7cm)
                }

                // 获取或创建段落
                XWPFParagraph paragraph;
                if (cell.getParagraphs().isEmpty()) {
                    paragraph = cell.addParagraph();
                } else {
                    paragraph = cell.getParagraphs().get(0);
                    // 清空段落中的所有run
                    for (int i = paragraph.getRuns().size() - 1; i >= 0; i--) {
                        paragraph.removeRun(i);
                    }
                }

                // 设置段落居中对齐
                paragraph.setAlignment(ParagraphAlignment.CENTER);

                // 设置段落间距为0，确保文本紧贴单元格中心
                paragraph.setSpacingBefore(0);
                paragraph.setSpacingAfter(0);
                paragraph.setSpacingBetween(1.0);

                // 使用分段字体写入：字母/数字及常用符号使用 Times New Roman，其他中文使用宋体
                appendTextWithMixedFonts(paragraph, text != null ? text : "", 10, null, null, null);

                // 强制设置单元格垂直居中对齐
                cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);

                // 通过CTTc设置更精确的垂直对齐
                if (cell.getCTTc().getTcPr() == null) {
                    cell.getCTTc().addNewTcPr();
                }
                if (cell.getCTTc().getTcPr().getVAlign() == null) {
                    cell.getCTTc().getTcPr().addNewVAlign();
                }
                cell.getCTTc().getTcPr().getVAlign().setVal(
                    org.openxmlformats.schemas.wordprocessingml.x2006.main.STVerticalJc.CENTER);
            }
        } catch (Exception e) {
            log.warn("设置单元格垂直居中文本失败 [行:{}, 列:{}]: {}",
                    row != null ? row.getTableCells().size() : "null", cellIndex, e.getMessage());
        }
    }

    /**
     * 设置单元格边框
     */
    private static void setCellBorders(XWPFTableCell cell) {
        try {
            // 获取或创建单元格属性
            if (cell.getCTTc().getTcPr() == null) {
                cell.getCTTc().addNewTcPr();
            }

            // 获取或创建边框属性
            if (cell.getCTTc().getTcPr().getTcBorders() == null) {
                cell.getCTTc().getTcPr().addNewTcBorders();
            }

            // 设置所有边框（上、下、左、右）
            org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder border =
                org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder.Factory.newInstance();
            border.setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STBorder.SINGLE);
            border.setSz(java.math.BigInteger.valueOf(4)); // 边框宽度
            border.setSpace(java.math.BigInteger.valueOf(0));
            border.setColor("000000"); // 黑色边框

            // 设置四个边的边框
            cell.getCTTc().getTcPr().getTcBorders().setTop(border);
            cell.getCTTc().getTcPr().getTcBorders().setBottom((org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder) border.copy());
            cell.getCTTc().getTcPr().getTcBorders().setLeft((org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder) border.copy());
            cell.getCTTc().getTcPr().getTcBorders().setRight((org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder) border.copy());

        } catch (Exception e) {
            log.warn("设置单元格边框失败: {}", e.getMessage());
        }
    }

    /**
     * 将文本按"字母/数字/常见符号"和"中文/其他"分段写入段落，分别设置字体。
     * - 字母/数字/符号（. % + - ( ) ~ / :）使用 Times New Roman
     * - 其他文字使用 宋体
     * 可选择性地继承字号/加粗/斜体/颜色
     */
    private static void appendTextWithMixedFonts(XWPFParagraph paragraph, String text, Integer inheritFontSize,
                                                Boolean inheritBold, Boolean inheritItalic, String inheritColor) {
        if (text == null) {
            return;
        }
        StringBuilder segment = new StringBuilder();
        Boolean currentLatin = null;
        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);
            boolean isLatin = isLatinDigitOrSymbol(c);
            if (currentLatin == null) {
                currentLatin = isLatin;
            }
            if (isLatin != currentLatin) {
                // flush previous segment
                createRunWithMixedFont(paragraph, segment.toString(), currentLatin, inheritFontSize, inheritBold, inheritItalic, inheritColor);
                segment.setLength(0);
                currentLatin = isLatin;
            }
            segment.append(c);
        }
        // flush last
        createRunWithMixedFont(paragraph, segment.toString(), currentLatin != null ? currentLatin : false,
                inheritFontSize, inheritBold, inheritItalic, inheritColor);
    }

    /**
     * 判断字符是否为拉丁字母、数字或常见符号
     */
    private static boolean isLatinDigitOrSymbol(char c) {
        if (Character.isDigit(c)) return true;
        if ((c >= 'A' && c <= 'Z') || (c >= 'a' && c <= 'z')) return true;
        switch (c) {
            case '.':
            case '%':
            case '+':
            case '-':
            case '(':
            case ')':
            case '~':
            case '/':
            case ':':
                return true;
            default:
                return false;
        }
    }

    /**
     * 创建带有混合字体的文本运行
     */
    private static void createRunWithMixedFont(XWPFParagraph paragraph, String content, boolean latin,
                                              Integer inheritFontSize, Boolean inheritBold, Boolean inheritItalic, String inheritColor) {
        if (content == null || content.isEmpty()) {
            return;
        }
        XWPFRun run = paragraph.createRun();
        run.setText(content);
        // 字体家族
        run.setFontFamily(latin ? "Times New Roman" : "宋体");
        // 字号
        if (inheritFontSize != null && inheritFontSize > 0) {
            run.setFontSize(inheritFontSize);
        } else {
            run.setFontSize(10); // 默认10号
        }
        // 继承样式
        if (inheritBold != null) run.setBold(inheritBold);
        if (inheritItalic != null) run.setItalic(inheritItalic);
        if (inheritColor != null && !inheritColor.isEmpty()) run.setColor(inheritColor);
    }


    /**
     * 生成对齐的目录行
     *
     * @param roadName 路线名称
     * @param direction 方向
     * @param pageOne 第一行页码
     * @param pageTwo 第二行起始页码
     * @param totalPages 总页数
     * @return 包含两行目录内容的数组
     */
    public static  String[] generateAlignedCatalogLines(String roadName, String direction, int pageOne, int pageTwo, int totalPages) {
        try {
            // 第一行：${roadName}公路路面公里跳车PBI评定汇总表..1～${pageOne}
            String line1Left = "1、"+roadName + "公路路面公里跳车PBI评定汇总表";
            String line1Right = "1～" + pageOne;

            // 第二行：${roadName}公路路面跳车PBI检测表（${direction}）..${pageTwo}～${pages}
            String line2Left = "2、"+roadName + "公路路面跳车PBI检测表（" + direction + "）";
            String line2Right = pageTwo + "～" + totalPages;

            // 计算对齐所需的点数，确保两行宽度一致
            String line1 = generateAlignedLine(line1Left, line1Right);
            String line2 = generateAlignedLine(line2Left, line2Right);

            // 确保两行长度一致，以较长的为准
            int maxLength = Math.max(line1.length(), line2.length());
            line1 = adjustLineLength(line1, line1Left, line1Right, maxLength);
            line2 = adjustLineLength(line2, line2Left, line2Right, maxLength);

            log.info("生成目录行：\n第一行: {}\n第二行: {}", line1, line2);

            return new String[]{line1, line2};

        } catch (Exception e) {
            log.error("生成对齐目录行失败", e);
            // 返回默认格式
            return new String[]{
                    roadName + "公路路面公里跳车PBI评定汇总表..1～" + pageOne,
                    roadName + "公路路面跳车PBI检测表（" + direction + "）.." + pageTwo + "～" + totalPages
            };
        }
    }




    /**
     * 替换Word文档中的占位符，使用混合字体（中文宋体，英文Times New Roman）
     * 字号为四号
     *
     * @param document Word文档对象
     * @param placeholder 占位符
     * @param value 替换值
     */
    public static  void replaceTextInDocumentWithMixedFont(XWPFDocument document, String placeholder, String value) {
        try {
            // 替换段落中的文本
            for (XWPFParagraph paragraph : document.getParagraphs()) {
                replaceParagraphTextWithMixedFont(paragraph, placeholder, value);
            }

            // 替换表格中的文本
            for (XWPFTable table : document.getTables()) {
                for (XWPFTableRow row : table.getRows()) {
                    for (XWPFTableCell cell : row.getTableCells()) {
                        for (XWPFParagraph paragraph : cell.getParagraphs()) {
                            replaceParagraphTextWithMixedFont(paragraph, placeholder, value);
                        }
                    }
                }
            }

            // 替换页眉中的文本
            for (XWPFHeader header : document.getHeaderList()) {
                for (XWPFParagraph paragraph : header.getParagraphs()) {
                    replaceParagraphTextWithMixedFont(paragraph, placeholder, value);
                }

                for (XWPFTable table : header.getTables()) {
                    for (XWPFTableRow row : table.getRows()) {
                        for (XWPFTableCell cell : row.getTableCells()) {
                            for (XWPFParagraph paragraph : cell.getParagraphs()) {
                                replaceParagraphTextWithMixedFont(paragraph, placeholder, value);
                            }
                        }
                    }
                }
            }

            // 替换页脚中的文本
            for (XWPFFooter footer : document.getFooterList()) {
                for (XWPFParagraph paragraph : footer.getParagraphs()) {
                    replaceParagraphTextWithMixedFont(paragraph, placeholder, value);
                }

                for (XWPFTable table : footer.getTables()) {
                    for (XWPFTableRow row : table.getRows()) {
                        for (XWPFTableCell cell : row.getTableCells()) {
                            for (XWPFParagraph paragraph : cell.getParagraphs()) {
                                replaceParagraphTextWithMixedFont(paragraph, placeholder, value);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("替换文档占位符失败 [{}]: {}", placeholder, e.getMessage());
        }
    }


    /**
     * 生成单行对齐内容
     *
     * @param leftText 左侧文本
     * @param rightText 右侧文本
     * @return 对齐后的完整行
     */
    public static  String generateAlignedLine(String leftText, String rightText) {
        // 基础长度：假设四号字体下，一行大约能容纳42个中文字符的宽度
        int targetWidth = 42;

        // 计算左右文本的显示宽度（中文字符宽度为2，英文数字宽度为1）
        int leftWidth = calculateDisplayWidth(leftText);
        int rightWidth = calculateDisplayWidth(rightText);

        // 计算需要的点数
        int dotsNeeded = Math.max(3, targetWidth - leftWidth - rightWidth);

        // 生成点
        StringBuilder dots = new StringBuilder();
        for (int i = 0; i < dotsNeeded; i++) {
            dots.append(".");
        }

        return leftText + dots.toString() + rightText;
    }

    /**
     * 调整行长度使其达到目标长度
     *
     * @param currentLine 当前行
     * @param leftText 左侧文本
     * @param rightText 右侧文本
     * @param targetLength 目标长度
     * @return 调整后的行
     */
    public static  String adjustLineLength(String currentLine, String leftText, String rightText, int targetLength) {
        if (currentLine.length() >= targetLength) {
            return currentLine;
        }

        // 计算需要增加的点数
        int additionalDots = targetLength - currentLine.length();

        // 在现有点的基础上增加
        int dotStart = leftText.length();
        int dotEnd = currentLine.length() - rightText.length();

        StringBuilder result = new StringBuilder();
        result.append(leftText);

        // 添加原有的点加上额外的点
        for (int i = dotStart; i < dotEnd + additionalDots; i++) {
            result.append(".");
        }

        result.append(rightText);

        return result.toString();
    }

    /**
     * 计算文本的显示宽度
     * 中文字符、中文标点宽度为2，英文字符、数字、英文标点宽度为1
     *
     * @param text 文本
     * @return 显示宽度
     */
    public static  int calculateDisplayWidth(String text) {
        if (text == null) {
            return 0;
        }

        int width = 0;
        for (char c : text.toCharArray()) {
            if (isChineseCharacter(c)) {
                width += 2; // 中文字符宽度为2
            } else {
                width += 1; // 英文、数字、符号宽度为1
            }
        }
        return width;
    }

    /**
     * 判断是否为中文字符
     *
     * @param c 字符
     * @return 是否为中文字符
     */
    public static  boolean isChineseCharacter(char c) {
        // 中文字符的Unicode范围
        return (c >= 0x4E00 && c <= 0x9FFF) ||  // 基本中文字符
                (c >= 0x3400 && c <= 0x4DBF) ||  // 扩展A
                (c >= 0x20000 && c <= 0x2A6DF) || // 扩展B
                (c >= 0x2A700 && c <= 0x2B73F) || // 扩展C
                (c >= 0x2B740 && c <= 0x2B81F) || // 扩展D
                (c >= 0x2B820 && c <= 0x2CEAF) || // 扩展E
                (c >= 0x3000 && c <= 0x303F) ||  // 中文标点符号
                (c >= 0xFF00 && c <= 0xFFEF);    // 全角字符
    }
}