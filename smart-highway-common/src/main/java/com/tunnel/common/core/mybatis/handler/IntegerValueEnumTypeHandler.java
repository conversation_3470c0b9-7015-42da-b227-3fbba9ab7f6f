package com.tunnel.common.core.mybatis.handler;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * 整型值枚举类型句柄
 */
public class IntegerValueEnumTypeHandler<E extends Enum<E> & IntegerValueEnum> extends BaseTypeHandler<E> {
    private final Class<E> type;

    public IntegerValueEnumTypeHandler(Class<E> type) {
        if (type == null) {
            throw new IllegalArgumentException("Type argument cannot be null");
        } else {
            this.type = type;
            if (type.getEnumConstants() == null) {
                throw new IllegalArgumentException(type.getSimpleName() + " does not represent an enum type.");
            }
        }
    }

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, E parameter, JdbcType jdbcType) throws SQLException {
        ps.setInt(i, parameter.getValue());
    }

    @Override
    public E getNullableResult(ResultSet rs, String columnName) throws SQLException {
        int i = rs.getInt(columnName);
        if (rs.wasNull()) {
            return null;
        } else {
            return this.getEnumIntegerInstance(i);
        }
    }

    @Override
    public E getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        int i = rs.getInt(columnIndex);
        if (rs.wasNull()) {
            return null;
        } else {
            return this.getEnumIntegerInstance(i);
        }
    }

    @Override
    public E getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        int i = cs.getInt(columnIndex);
        if (cs.wasNull()) {
            return null;
        } else {
            return this.getEnumIntegerInstance(i);
        }
    }

    private E getEnumIntegerInstance(int value) {
        E[] enums = this.type.getEnumConstants();
        for (E e : enums) {
            if (e.getValue() == value) {
                return e;
            }
        }

        throw new Error("未知的枚举类型");
    }
}
