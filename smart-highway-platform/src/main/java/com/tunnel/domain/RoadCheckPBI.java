package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 路面跳车检测信息对象 road_check_bump
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RoadCheckPBI extends BaseRoadCheckEntity {
    private static final long serialVersionUID = 1L;

    // 基础字段已在BaseRoadCheckEntity中定义

    /** BPL - 低频跳车 */
    @Excel(name = "BPL", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal bpl;

    /** BPM - 中频跳车 */
    @Excel(name = "BPM", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal bpm;

    /** BPH - 高频跳车 */
    @Excel(name = "BPH", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal bph;

    /** 跳车高度差(cm) */
    @Excel(name = "跳车△h(cm)", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal bumpHeight;

    /** PBI - 路面平整度指数 */
    @Excel(name = "PBI", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal pbi;

    // 路面类型、方向、时间字段等已在BaseRoadCheckEntity中定义

    /** 道路名称 - 非数据库字段，用于前端展示 */
    private String roadName;
} 