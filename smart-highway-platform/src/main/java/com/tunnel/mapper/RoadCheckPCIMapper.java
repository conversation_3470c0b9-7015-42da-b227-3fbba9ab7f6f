package com.tunnel.mapper;

import com.tunnel.domain.RoadCheckPCI;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 路面破损信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-28
 */
public interface RoadCheckPCIMapper {
    /**
     * 查询路面破损信息
     *
     * @param id 路面破损信息主键
     * @return 路面破损信息
     */
    public RoadCheckPCI selectRoadCheckRecordById(Long id);

    /**
     * 查询路面破损信息列表
     *
     * @param roadCheckPCI 路面破损信息
     * @return 路面破损信息集合
     */
    public List<RoadCheckPCI> selectRoadCheckRecordList(RoadCheckPCI roadCheckPCI);

    /**
     * 新增路面破损信息
     *
     * @param roadCheckPCI 路面破损信息
     * @return 结果
     */
    public int insertRoadCheckRecord(RoadCheckPCI roadCheckPCI);

    /**
     * 修改路面破损信息
     *
     * @param roadCheckPCI 路面破损信息
     * @return 结果
     */
    public int updateRoadCheckRecord(RoadCheckPCI roadCheckPCI);

    /**
     * 删除路面破损信息
     *
     * @param id 路面破损信息主键
     * @return 结果
     */
    public int deleteRoadCheckRecordById(Long id);

    /**
     * 批量删除路面破损信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRoadCheckRecordByIds(Long[] ids);

    /**
     * 批量新增路面破损数据
     *
     * @param list 路面破损信息列表
     * @return 结果
     */
    public int batchInsert(List<RoadCheckPCI> list);
    
    /**
     * 根据道路ID获取路面破损信息
     *
     * @param roadId 道路ID
     * @return 路面破损信息集合
     */
    public List<RoadCheckPCI> selectRoadCheckRecordByRoadId(Long roadId);
    
    /**
     * 分页查询路面破损信息列表，用于大数据量导出
     *
     * @param query 查询条件
     * @param offset 起始位置
     * @param pageSize 每页数量
     * @return 路面破损信息集合
     */
    public List<RoadCheckPCI> selectRoadCheckRecordListByPage(@Param("query") RoadCheckPCI query,
                                                              @Param("offset") int offset,
                                                              @Param("pageSize") int pageSize);
    
    /**
     * 获取符合条件的总记录数
     *
     * @param roadCheckPCI 查询条件
     * @return 总记录数
     */
    public int countRoadCheckRecord(RoadCheckPCI roadCheckPCI);

    /**
     * 根据道路ID删除路面破损信息
     *
     * @param roadId 道路ID
     * @return 结果
     */
    public int deleteRoadCheckRecordByRoadId(Long roadId);
} 