package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 路面破损信息对象 road_check_record
 *
 * <AUTHOR>
 * @date 2024-11-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RoadCheckPCI extends BaseRoadCheckEntity {
    private static final long serialVersionUID = 1L;

    // 基础字段已在BaseRoadCheckEntity中定义

    /** 龟裂面积(m²) */
    @Excel(name = "龟裂(m²)", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal crackArea;

    /** 块状裂缝面积(m²) */
    @Excel(name = "块状裂缝(m²)", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal blockCrackArea;

    /** 纵向裂缝面积(m²) */
    @Excel(name = "纵向裂缝(m²)", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal longitudinalCrackArea;

    /** 横向裂缝面积(m²) */
    @Excel(name = "横向裂缝(m²)", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal transverseCrackArea;

    /** 沉陷面积(m²) */
    @Excel(name = "沉陷(m²)", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal sinkArea;

    /** 车辙面积(m²) */
    @Excel(name = "车辙(m²)", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal rutArea;

    /** 波浪拥包面积(m²) */
    @Excel(name = "波浪拥包(m²)", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal waveBumpArea;

    /** 坑槽面积(m²) */
    @Excel(name = "坑槽(m²)", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal pitArea;

    /** 松散面积(m²) */
    @Excel(name = "松散(m²)", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal looseArea;

    /** 泛油面积(m²) */
    @Excel(name = "泛油(m²)", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal bleedingArea;

    /** 修补面积(m²) */
    @Excel(name = "修补-块状(m2)", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal patchAreaPart;

    @Excel(name = "修补-条状(m2)", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal patchAreaStrip;

    /** 破损率DR(%) */
    @Excel(name = "破损率DR(%)", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal damageRate;

    /** PCI指数 */
    @Excel(name = "PCI", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal pci;

    /** 备注 */
    private String remark;

    /** 道路名称 - 非数据库字段，用于前端展示 */
    private String roadName;
} 