package com.tunnel.service.impl;

import com.tunnel.common.core.domain.entity.SysUser;
import com.tunnel.common.utils.CollectUtil;
import com.tunnel.common.utils.FileUtil;
import com.tunnel.common.utils.SecurityUtils;
import com.tunnel.common.utils.StakeCodeUtil;
import com.tunnel.common.utils.WordDocumentUtils;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.BatchAddResponse;
import com.tunnel.domain.RoadCheckPBI;
import com.tunnel.domain.Road;
import com.tunnel.mapper.RoadCheckPBIMapper;
import com.tunnel.mapper.RoadMapper;
import com.tunnel.service.RoadCheckPBIService;
import com.tunnel.service.CheckTeamService;
import com.tunnel.service.CheckUserService;
import com.tunnel.domain.CheckUser;
import com.tunnel.domain.CheckTeam;
import java.util.Map;
import java.util.LinkedHashMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.usermodel.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.util.CollectionUtils;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge;
import org.springframework.core.io.ClassPathResource;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.net.URLEncoder;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import java.util.Set;
import java.util.HashSet;

/**
 * 路面跳车检测信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Service
@Slf4j
public class RoadCheckPBIServiceImpl implements RoadCheckPBIService {
    @Autowired
    private RoadCheckPBIMapper roadCheckPBIMapper;

    @Autowired
    private RoadMapper roadMapper;

    @Autowired
    private CheckTeamService checkTeamService;

    @Autowired
    private CheckUserService checkUserService;

    // 每页导出数量
    private static final int PAGE_SIZE = 5000;

    // 导出线程池大小
    private static final int EXPORT_THREADS = 5;

    /**
     * 查询路面跳车检测信息
     *
     * @param id 路面跳车检测信息主键
     * @return 路面跳车检测信息
     */
    @Override
    public RoadCheckPBI selectRoadCheckBumpById(Long id) {
        return roadCheckPBIMapper.selectRoadCheckBumpById(id);
    }

    /**
     * 查询路面跳车检测信息列表
     *
     * @param roadCheckPBI 路面跳车检测信息
     * @return 路面跳车检测信息
     */
    @Override
    public List<RoadCheckPBI> selectRoadCheckBumpList(RoadCheckPBI roadCheckPBI) {
        return roadCheckPBIMapper.selectRoadCheckBumpList(roadCheckPBI);
    }

    /**
     * 新增路面跳车检测信息
     *
     * @param roadCheckPBI 路面跳车检测信息
     * @return 结果
     */
    @Override
    public int insertRoadCheckBump(RoadCheckPBI roadCheckPBI) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        roadCheckPBI.setCreator(user.getUserId());
        roadCheckPBI.setModifier(user.getUserId());

        // 计算百米段和公里段
        if (roadCheckPBI.getStartCode() != null && !roadCheckPBI.getStartCode().isEmpty()) {
            roadCheckPBI.setHundredSection(StakeCodeUtil.calculateHundredSection(roadCheckPBI.getStartCode()));
            roadCheckPBI.setThousandSection(StakeCodeUtil.calculateThousandSection(roadCheckPBI.getStartCode()));
        }

        return roadCheckPBIMapper.insertRoadCheckBump(roadCheckPBI);
    }

    /**
     * 修改路面跳车检测信息
     *
     * @param roadCheckPBI 路面跳车检测信息
     * @return 结果
     */
    @Override
    public int updateRoadCheckBump(RoadCheckPBI roadCheckPBI) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        roadCheckPBI.setModifier(user.getUserId());

        // 计算百米段和公里段
        if (roadCheckPBI.getStartCode() != null && !roadCheckPBI.getStartCode().isEmpty()) {
            roadCheckPBI.setHundredSection(StakeCodeUtil.calculateHundredSection(roadCheckPBI.getStartCode()));
            roadCheckPBI.setThousandSection(StakeCodeUtil.calculateThousandSection(roadCheckPBI.getStartCode()));
        }

        return roadCheckPBIMapper.updateRoadCheckBump(roadCheckPBI);
    }

    /**
     * 批量删除路面跳车检测信息
     *
     * @param ids 需要删除的路面跳车检测信息主键
     * @return 结果
     */
    @Override
    public int deleteRoadCheckBumpByIds(Long[] ids) {
        return roadCheckPBIMapper.deleteRoadCheckBumpByIds(ids);
    }

    /**
     * 删除路面跳车检测信息信息
     *
     * @param id 路面跳车检测信息主键
     * @return 结果
     */
    @Override
    public int deleteRoadCheckBumpById(Long id) {
        return roadCheckPBIMapper.deleteRoadCheckBumpById(id);
    }

    /**
     * 根据道路ID获取路面跳车检测信息
     *
     * @param roadId 道路ID
     * @return 路面跳车检测信息集合
     */
    @Override
    public List<RoadCheckPBI> selectRoadCheckBumpByRoadId(Long roadId) {
        return roadCheckPBIMapper.selectRoadCheckBumpByRoadId(roadId);
    }

    /**
     * 批量导入路面跳车检测数据
     *
     * @param file 导入文件
     * @param roadId 道路ID
     * @return 导入结果
     */
    @Override
    @Transactional
    public BatchAddResponse batchImport(MultipartFile file, Long roadId) {
        BatchAddResponse addResponse = new BatchAddResponse();
        InputStream fileStream = null;
        try {
            // 导入前先删除该道路的所有PBI数据
            int deletedCount = deleteRoadCheckBumpByRoadId(roadId);
            log.info("导入前删除道路ID {} 的PBI数据：{} 条", roadId, deletedCount);
            
            // 生成本地缓存路径
            String localFile = FileUtil.saveExcelFile(file);
            fileStream = new FileInputStream(localFile);
            BufferedInputStream bufferedInputStream = new BufferedInputStream(fileStream);

            // 读取Excel文件
            Workbook workbook;
            if (file.getOriginalFilename().endsWith(".xlsx")) {
                workbook = WorkbookFactory.create(bufferedInputStream);
            } else {
                addResponse.setStatus(1);
                addResponse.setMsg("请使用Excel 2007及以上版本的文件（.xlsx格式）！");
                addResponse.generateSummary();
                return addResponse;
            }

            List<RoadCheckPBI> resultList = new ArrayList<>();

            // 处理Sheet1（上行数据）
            Sheet sheet1 = workbook.getSheetAt(0);
            if (sheet1 != null) {
                List<RoadCheckPBI> upDirectionData = processSheetWithValidation(sheet1, roadId, "上行", "Sheet1", addResponse);
                resultList.addAll(upDirectionData);
                log.info("Sheet1(上行)处理数据：{} 条", upDirectionData.size());
            }

            // 处理Sheet2（下行数据）
            if (workbook.getNumberOfSheets() > 1) {
                Sheet sheet2 = workbook.getSheetAt(1);
                if (sheet2 != null) {
                    List<RoadCheckPBI> downDirectionData = processSheetWithValidation(sheet2, roadId, "下行", "Sheet2", addResponse);
                    resultList.addAll(downDirectionData);
                    log.info("Sheet2(下行)处理数据：{} 条", downDirectionData.size());
                }
            }

            // 根据roadType分组并处理段位信息
            if (!resultList.isEmpty()) {
                log.info("开始处理PBI数据的段位信息，总计 {} 条记录", resultList.size());
                StakeCodeUtil.processSectionsByRoadType(resultList);
            }

            // 只插入校验通过的数据
            if (!resultList.isEmpty()) {
                List<List<RoadCheckPBI>> splitList = CollectUtil.splitList(resultList, 1000);
                for (List<RoadCheckPBI> tempList : splitList) {
                    roadCheckPBIMapper.batchInsert(tempList);
                }
                addResponse.addSuccessCount(resultList.size());
                log.info("成功导入数据：{} 条", resultList.size());

                // 输出最后一行数据的段位信息用于验证
                if (!resultList.isEmpty()) {
                    RoadCheckPBI lastRecord = resultList.get(resultList.size() - 1);
                    log.info("导入数据最后一条记录段位信息：起始桩号={}, 结束桩号={}, 百米段={}, 公里段={}",
                            lastRecord.getStartCode(), lastRecord.getEndCode(),
                            lastRecord.getHundredSection(), lastRecord.getThousandSection());
                }
            }

            // 设置最终状态
            if (addResponse.getSuccessCount() > 0 && addResponse.getFailCount() == 0) {
                addResponse.setStatus(0);
                addResponse.setMsg("导入成功");
            } else if (addResponse.getSuccessCount() > 0 && addResponse.getFailCount() > 0) {
                addResponse.setStatus(0);
                addResponse.setMsg("部分导入成功");
            } else {
                addResponse.setStatus(1);
                addResponse.setMsg("导入失败");
            }

            addResponse.generateSummary();
            workbook.close();

        } catch (IOException e) {
            log.error("文件解析失败", e);
            addResponse.setStatus(1);
            addResponse.setMsg("文件解析失败: " + e.getMessage());
            addResponse.generateSummary();
        } catch (Exception e) {
            log.error("导入数据失败", e);
            addResponse.setStatus(1);
            addResponse.setMsg("导入数据失败: " + e.getMessage());
            addResponse.generateSummary();
        } finally {
            try {
                if (fileStream != null) {
                    fileStream.close();
                }
            } catch (IOException e) {
                log.error("excel文件读取失败, 失败原因：{}", e);
            }
        }
        return addResponse;
    }

    /**
     * 处理单个工作表的数据（包含详细校验）
     *
     * @param sheet 工作表
     * @param roadId 道路ID
     * @param direction 行驶方向（上行/下行）
     * @param sheetName 工作表名称
     * @param addResponse 响应对象
     * @return 解析后的数据列表
     */
    private List<RoadCheckPBI> processSheetWithValidation(Sheet sheet, Long roadId, String direction,
                                                          String sheetName, BatchAddResponse addResponse) {
        List<RoadCheckPBI> resultList = new ArrayList<>();

        // 从第3行开始读取数据（索引从0开始，第3行对应索引2），跳过表头和标题行
        int startRow = 2;
        int rowCount = sheet.getPhysicalNumberOfRows();

        if (rowCount <= startRow) {
            log.warn("工作表 {} 中没有找到有效数据行", sheetName);
            return resultList;
        }

        // 将方向字符串转换为Integer值
        Integer directionValue = "上行".equals(direction) ? 1 : 2;

        // 预处理：收集所有有效的数据行，用于判断最后一行
        List<Integer> validRowIndices = new ArrayList<>();
        for (int i = startRow; i < rowCount; i++) {
            Row row = sheet.getRow(i);
            if (row == null) {
                continue;
            }

            // 检查第一列是否为空（起始桩号）
            Cell stakeCell = row.getCell(0);
            if (stakeCell == null || getCellStringValue(stakeCell).trim().isEmpty()) {
                // 检查是否为空行
                boolean isEmptyRow = true;
                for (int j = 0; j < 8; j++) { // PBI有8列数据（包括路面类型）
                    Cell cell = row.getCell(j);
                    if (cell != null && !getCellStringValue(cell).trim().isEmpty()) {
                        isEmptyRow = false;
                        break;
                    }
                }
                if (!isEmptyRow) {
                    validRowIndices.add(i); // 非空行但起始桩号为空，仍算作有效行（会报错）
                }
            } else {
                validRowIndices.add(i); // 有起始桩号的行
            }
        }

        // 遍历数据行
        for (int idx = 0; idx < validRowIndices.size(); idx++) {
            int i = validRowIndices.get(idx);
            Row row = sheet.getRow(i);
            if (row == null) {
                continue;
            }

            int excelRowNum = i + 1; // Excel行号从1开始
            boolean rowHasError = false;

            // 检查第一列是否为空（起始桩号）
            Cell stakeCell = row.getCell(0);
            if (stakeCell == null || getCellStringValue(stakeCell).trim().isEmpty()) {
                // 跳过前4行的错误报告（表头行）
                if (excelRowNum > 4) {
                    addResponse.addError(sheetName, excelRowNum, "起始桩号", "起始桩号不能为空");
                }
                rowHasError = true;
            }

            try {
                RoadCheckPBI record = new RoadCheckPBI();

                // 校验和设置起始桩号
                String startCode = getCellStringValue(row.getCell(0));
                String endCode = getCellStringValue(row.getCell(1));

                if (startCode != null && !startCode.trim().isEmpty()) {
                    // 格式化桩号为标准格式
                    String formattedStartCode = StakeCodeUtil.formatStakeCode(startCode.trim());
                    record.setStartCode(formattedStartCode);

                    // 格式化结束桩号
                    String formattedEndCode = null;
                    if (endCode != null && !endCode.trim().isEmpty()) {
                        formattedEndCode = StakeCodeUtil.formatStakeCode(endCode.trim());
                        record.setEndCode(formattedEndCode);
                    }
                }

                // 校验和设置终止桩号
                if (endCode != null && !endCode.trim().isEmpty()) {
                    // 格式化桩号为标准格式
                    if (record.getEndCode() == null) {
                        String formattedEndCode = StakeCodeUtil.formatStakeCode(endCode.trim());
                        record.setEndCode(formattedEndCode);
                    }
                } else {
                    // 跳过前4行的错误报告（表头行）
                    if (excelRowNum > 4) {
                        addResponse.addError(sheetName, excelRowNum, "终止桩号", "终止桩号不能为空");
                    }
                    rowHasError = true;
                }

                // 校验和设置BPL - 低频跳车
                BigDecimal bpl = validateAndGetDecimalValue(row.getCell(2), sheetName, excelRowNum, "BPL", addResponse);
                if (bpl != null) {
                    record.setBpl(bpl);
                } else {
                    rowHasError = true;
                }

                // 校验和设置BPM - 中频跳车
                BigDecimal bpm = validateAndGetDecimalValue(row.getCell(3), sheetName, excelRowNum, "BPM", addResponse);
                if (bpm != null) {
                    record.setBpm(bpm);
                } else {
                    rowHasError = true;
                }

                // 校验和设置BPH - 高频跳车
                BigDecimal bph = validateAndGetDecimalValue(row.getCell(4), sheetName, excelRowNum, "BPH", addResponse);
                if (bph != null) {
                    record.setBph(bph);
                } else {
                    rowHasError = true;
                }

                // 校验和设置跳车高度差
                BigDecimal bumpHeight = validateAndGetDecimalValue(row.getCell(5), sheetName, excelRowNum, "跳车高度差", addResponse);
                if (bumpHeight != null) {
                    record.setBumpHeight(bumpHeight);
                } else {
                    rowHasError = true;
                }

                // 校验和设置PBI - 路面平整度指数
                BigDecimal pbi = validateAndGetDecimalValue(row.getCell(6), sheetName, excelRowNum, "PBI", addResponse);
                if (pbi != null) {
                    record.setPbi(pbi);
                } else {
                    rowHasError = true;
                }

                // 校验和设置路面类型（第8列，索引7）
                String roadType = getCellStringValue(row.getCell(7));
                if (roadType != null && !roadType.trim().isEmpty()) {
                    record.setRoadType(roadType.trim());
                } else {
                    // 如果路面类型为空，设置默认值
                    record.setRoadType("沥青路面");
                }

                // 只有当行没有错误时才添加到结果列表
                if (!rowHasError) {
                    record.setDirection(directionValue);
                    record.setRoadId(roadId);
                    record.setCreateTime(new Date());

                    SysUser user = SecurityUtils.getLoginUser().getUser();
                    record.setCreator(user.getUserId());
                    record.setModifier(user.getUserId());

                    resultList.add(record);
                }

            } catch (Exception e) {
                log.error("处理第{}行数据时发生错误", excelRowNum, e);
                addResponse.addError(sheetName, excelRowNum, "数据处理", "处理数据时发生错误：" + e.getMessage());
            }
        }

        return resultList;
    }



    /**
     * 校验并获取BigDecimal值
     */
    private BigDecimal validateAndGetDecimalValue(Cell cell, String sheetName, int rowNum,
                                                  String fieldName, BatchAddResponse addResponse) {
        if (cell == null) {
            // 跳过前4行的错误报告（表头行）
            if (rowNum > 4) {
                addResponse.addError(sheetName, rowNum, fieldName, fieldName + "不能为空");
            }
            return null;
        }

        String cellValue = getCellStringValue(cell);
        if (cellValue.trim().isEmpty()) {
            // 跳过前4行的错误报告（表头行）
            if (rowNum > 4) {
                addResponse.addError(sheetName, rowNum, fieldName, fieldName + "不能为空", cellValue);
            }
            return null;
        }

        try {
            BigDecimal value = getCellDecimalValue(cell);
            if (value.compareTo(BigDecimal.ZERO) < 0) {
                // 跳过前4行的错误报告（表头行）
                if (rowNum > 4) {
                    addResponse.addError(sheetName, rowNum, fieldName, fieldName + "不能为负数", cellValue);
                }
                return null;
            }
            return value;
        } catch (Exception e) {
            // 跳过前4行的错误报告（表头行）
            if (rowNum > 4) {
                addResponse.addError(sheetName, rowNum, fieldName, fieldName + "格式不正确，应为数值", cellValue);
            }
            return null;
        }
    }

    /**
     * 获取单元格的字符串值
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        try {
            switch (cell.getCellType()) {
                case STRING:
                    return cell.getStringCellValue().trim();
                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        return new SimpleDateFormat("yyyy-MM-dd").format(cell.getDateCellValue());
                    }
                    // 对于数字，转为字符串并去除小数点后的零
                    String value = String.valueOf(cell.getNumericCellValue());
                    if (value.endsWith(".0")) {
                        value = value.substring(0, value.length() - 2);
                    }
                    return value;
                case BOOLEAN:
                    return String.valueOf(cell.getBooleanCellValue());
                case FORMULA:
                    try {
                        return String.valueOf(cell.getNumericCellValue());
                    } catch (Exception e) {
                        return cell.getStringCellValue();
                    }
                default:
                    return "";
            }
        } catch (Exception e) {
            log.warn("获取单元格字符串值失败: {}", e.getMessage());
            return "";
        }
    }

    /**
     * 获取单元格的BigDecimal值
     */
    private BigDecimal getCellDecimalValue(Cell cell) {
        if (cell == null) {
            return BigDecimal.ZERO;
        }

        try {
            switch (cell.getCellType()) {
                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        return null;
                    }
                    return BigDecimal.valueOf(cell.getNumericCellValue());
                case STRING:
                    String str = cell.getStringCellValue().trim();
                    if (str.isEmpty()) {
                        return BigDecimal.ZERO;
                    }
                    try {
                        return new BigDecimal(str);
                    } catch (NumberFormatException e) {
                        return BigDecimal.ZERO;
                    }
                case BOOLEAN:
                    return cell.getBooleanCellValue() ? BigDecimal.ONE : BigDecimal.ZERO;
                case FORMULA:
                    try {
                        return BigDecimal.valueOf(cell.getNumericCellValue());
                    } catch (Exception e) {
                        return BigDecimal.ZERO;
                    }
                default:
                    return BigDecimal.ZERO;
            }
        } catch (Exception e) {
            log.warn("获取单元格数值失败: {}", e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    /**
     * 优化导出路面跳车检测信息
     *
     * @param response HTTP响应
     * @param roadCheckPBI 查询条件
     */
    @Override
    public void exportOptimized(HttpServletResponse response, RoadCheckPBI roadCheckPBI) {
        List<RoadCheckPBI> list = roadCheckPBIMapper.selectRoadCheckBumpList(roadCheckPBI);
        ExcelUtil<RoadCheckPBI> util = new ExcelUtil<>(RoadCheckPBI.class);
        util.exportExcel(response, list, "路面跳车检测信息数据");
    }

    /**
     * 获取路面跳车检测信息记录数
     *
     * @param roadCheckPBI 查询条件
     * @return 记录数
     */
    @Override
    public int countRoadCheckBump(RoadCheckPBI roadCheckPBI) {
        return roadCheckPBIMapper.countRoadCheckBump(roadCheckPBI);
    }

    @Override
    public void exportPBIByDirection(HttpServletResponse response, Long roadId) {
        try {
            // 查询路线信息
            Road road = roadMapper.selectRoadById(roadId);
            String roadName = road != null ? road.getRoadName() : "路线";

            // 查询指定路线的PBI数据
            RoadCheckPBI query = new RoadCheckPBI();
            query.setRoadId(roadId);
            query.setDirection(1); // 上行
            List<RoadCheckPBI> upData = roadCheckPBIMapper.selectRoadCheckBumpList(query);

            query.setDirection(2); // 下行
            List<RoadCheckPBI> downData = roadCheckPBIMapper.selectRoadCheckBumpList(query);

            // 检查是否有数据可以导出
            if (CollectionUtils.isEmpty(upData) && CollectionUtils.isEmpty(downData)) {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500, \"msg\":\"没有PBI数据可以导出\"}");
                return;
            }

            // 创建新的工作簿
            SXSSFWorkbook workbook = new SXSSFWorkbook(100);

            // 创建上行数据Sheet
            Sheet upSheet = workbook.createSheet("上行");
            createPBIHeader(workbook, upSheet, "上行", roadName);
            fillPBIData(upSheet, upData);

            // 创建下行数据Sheet  
            Sheet downSheet = workbook.createSheet("下行");
            createPBIHeader(workbook, downSheet, "下行", roadName);
            fillPBIData(downSheet, downData);

            // 创建上行百米统计Sheet
            Sheet upHundredSheet = workbook.createSheet("上行-百米统计");
            createPBIHundredHeader(workbook, upHundredSheet, "上行", roadName);
            List<RoadCheckPBI> upHundredData = processHundredMeterData(upData);
            fillPBIHundredData(upHundredSheet, upHundredData);

            // 创建下行百米统计Sheet
            Sheet downHundredSheet = workbook.createSheet("下行-百米统计");
            createPBIHundredHeader(workbook, downHundredSheet, "下行", roadName);
            List<RoadCheckPBI> downHundredData = processHundredMeterData(downData);
            fillPBIHundredData(downHundredSheet, downHundredData);

            // 创建汇总Sheet
            Sheet summarySheet = workbook.createSheet("汇总");
            createSummaryHeader(workbook, summarySheet, roadName, upData, downData);
            List<RoadCheckPBI> summaryData = processThousandMeterData(upData, downData);
            fillSummaryData(summarySheet, summaryData, upData, downData);

            // 设置响应头
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String fileName = "路面跳车检测原始数据_" + sdf.format(new Date()) + ".xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));

            // 写入响应流
            workbook.write(response.getOutputStream());
            workbook.dispose();

        } catch (Exception e) {
            log.error("导出PBI数据失败", e);
            try {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500, \"msg\":\"导出失败：" + e.getMessage() + "\"}");
            } catch (IOException ioException) {
                log.error("导出错误响应失败", ioException);
            }
        }
    }

    /**
     * 生成上行和下行两个独立的PBI Word文档
     */
    private void generateSeparatePBIWordDocuments(HttpServletResponse response,
                                                  List<RoadCheckPBI> upData, List<RoadCheckPBI> downData,Road road,
                                                  Long teamId, String dateTime, String monthDate, String titleName, String checkName, String reviewName) {
        try {
            // 创建ZIP输出流
            response.setContentType("application/zip");
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String zipFileName = "路面PBI检测报告_" + sdf.format(new Date()) + ".zip";
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(zipFileName, "UTF-8"));

            ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream());

            // 使用并发工具类
            ExecutorService executor = Executors.newFixedThreadPool(2);
            CountDownLatch latch = new CountDownLatch(2);

            // 用于存储生成的文档和文件名
            final XWPFDocument[] documents = new XWPFDocument[2];
            final String[] fileNames = new String[2];
            final Exception[] exceptions = new Exception[2];

            // 并行生成上行文档
            if (!CollectionUtils.isEmpty(upData)) {
                executor.submit(() -> {
                    try {
                        log.info("开始生成上行PBI Word文档...");
                        documents[0] = createSingleDirectionPBIDocument(upData, downData, road, "上行", teamId, dateTime, monthDate, titleName, checkName, reviewName);
                        fileNames[0] = "路面PBI检测报告_上行_" + sdf.format(new Date()) + ".docx";
                        log.info("上行PBI Word文档生成完成");
                    } catch (Exception e) {
                        log.error("生成上行PBI Word文档失败", e);
                        exceptions[0] = e;
                    } finally {
                        latch.countDown();
                    }
                });
            } else {
                latch.countDown();
            }

            // 并行生成下行文档
            if (!CollectionUtils.isEmpty(downData)) {
                executor.submit(() -> {
                    try {
                        log.info("开始生成下行PBI Word文档...");
                        documents[1] = createSingleDirectionPBIDocument(upData, downData, road, "下行", teamId, dateTime, monthDate, titleName, checkName, reviewName);
                        fileNames[1] = "路面PBI检测报告_下行_" + sdf.format(new Date()) + ".docx";
                        log.info("下行PBI Word文档生成完成");
                    } catch (Exception e) {
                        log.error("生成下行PBI Word文档失败", e);
                        exceptions[1] = e;
                    } finally {
                        latch.countDown();
                    }
                });
            } else {
                latch.countDown();
            }

            // 等待所有文档生成完成
            latch.await();
            executor.shutdown();

            // 检查是否有异常发生
            if (exceptions[0] != null) {
                throw new RuntimeException("生成上行PBI Word文档失败", exceptions[0]);
            }
            if (exceptions[1] != null) {
                throw new RuntimeException("生成下行PBI Word文档失败", exceptions[1]);
            }

            // 将生成的文档写入ZIP
            if (documents[0] != null && fileNames[0] != null) {
                ZipEntry upEntry = new ZipEntry(fileNames[0]);
                zipOut.putNextEntry(upEntry);
                documents[0].write(zipOut);
                zipOut.closeEntry();
                documents[0].close();
                log.info("上行文档已添加到ZIP文件");
            }

            if (documents[1] != null && fileNames[1] != null) {
                ZipEntry downEntry = new ZipEntry(fileNames[1]);
                zipOut.putNextEntry(downEntry);
                documents[1].write(zipOut);
                zipOut.closeEntry();
                documents[1].close();
                log.info("下行文档已添加到ZIP文件");
            }

            zipOut.close();
            log.info("PBI ZIP文件生成完成");

        } catch (Exception e) {
            log.error("生成分离的PBI Word文档失败", e);
            throw new RuntimeException("生成分离的PBI Word文档失败", e);
        }
    }

    /**
     * 创建单个方向的PBI Word文档
     */
    private XWPFDocument createSingleDirectionPBIDocument(List<RoadCheckPBI> upData, List<RoadCheckPBI> downData,
                                                          Road road, String direction,
                                                          Long teamId, String dateTime, String monthDate, String titleName, String checkName, String reviewName) {
        InputStream templateStream = null;
        try {
            // 解决POI安全限制：设置ZIP文件的最小压缩比率，避免Zip bomb检测误报
            org.apache.poi.openxml4j.util.ZipSecureFile.setMinInflateRatio(0.001);
            
            // 加载模板
            ClassPathResource templateResource = new ClassPathResource("static/word/pbi-template.docx");
            templateStream = templateResource.getInputStream();
            XWPFDocument document = new XWPFDocument(templateStream);
            log.info("成功加载PBI Word模板文件");

            // 准备汇总数据 - 使用千米段进行汇总
            List<RoadCheckPBI> summaryData = processThousandMeterData(upData, downData);

            // 计算各等级的公里数
            double totalDistanceSum = 0.0;
            double[] upGradeDistances = new double[5];
            double[] downGradeDistances = new double[5];

            for (RoadCheckPBI record : summaryData) {
                double distance = calculateDistance(record.getStartCode(), record.getEndCode());
                totalDistanceSum += distance;

                // 计算上行等级
                BigDecimal upPbiScore = calculateThousandPBIScore(record.getThousandSection(), upData);
                String upGrade = getGradeByScore(upPbiScore);
                upGradeDistances[getGradeIndex(upGrade)] += distance;

                // 计算下行等级
                BigDecimal downPbiScore = calculateThousandPBIScore(record.getThousandSection(), downData);
                String downGrade = getGradeByScore(downPbiScore);
                downGradeDistances[getGradeIndex(downGrade)] += distance;
            }

            // 填充第三个表格（汇总表格）
            if (document.getTables().size() >= 3) {
                XWPFTable summaryTable = document.getTables().get(2); // 第三个表格
                fillPBISummaryTable(summaryTable, summaryData, upData, downData);
                log.info("成功填充PBI汇总表格，共 {} 行数据", summaryData.size());
            } else {
                log.warn("未找到PBI Word模板中的汇总表格");
            }

            // 填充最后一个表格（十米详细数据）
            if (document.getTables().size() >= 4) {
                XWPFTable detailTable = document.getTables().get(document.getTables().size() - 1); // 最后一个表格

                // 根据方向选择对应的详细数据
                List<RoadCheckPBI> detailData = new ArrayList<>();
                if ("上行".equals(direction) && upData != null && !upData.isEmpty()) {
                    detailData.addAll(upData);
                } else if ("下行".equals(direction) && downData != null && !downData.isEmpty()) {
                    detailData.addAll(downData);
                }

                // 按起始桩号排序
                detailData.sort((a, b) -> a.getStartCode().compareTo(b.getStartCode()));

                // 填充详细数据到表格
                fillPBIDetailTable(detailTable, detailData);

                log.info("成功填充{}详细数据表格，共 {} 行数据", direction, detailData.size());
            } else {
                log.warn("未找到详细数据表格");
            }

            // 填充检测人员信息到第二个表格
            if (teamId != null && document.getTables().size() >= 2) {
                checkTeamService.fillCheckTeamUserTable(document.getTables().get(1), teamId);
            }

            // 获取路线信息用于报告编号
            Road roadInfo = roadMapper.selectRoadById(upData != null && !upData.isEmpty() ? upData.get(0).getRoadId() : 
                                                     (downData != null && !downData.isEmpty() ? downData.get(0).getRoadId() : null));
            
            // 使用公共工具类替换标准占位符（包括报告编号）
            WordDocumentUtils.replaceStandardPlaceholders(document, "PBI", titleName, checkName, reviewName, roadInfo);
            
            // 替换其他占位符
            WordDocumentUtils.replaceTextInDocument(document, "${roadName}", road.getRoadName());
            WordDocumentUtils.replaceTextInDocument(document, "${startCode}", road.getStartCode());
            WordDocumentUtils.replaceTextInDocument(document, "${endCode}", road.getEndCode());
            WordDocumentUtils.replaceTextInDocumentWithFontStyle(document, "${roadNames}", road.getRoadName(),"黑体",24);
            WordDocumentUtils.replaceTextInDocumentWithFontStyle(document, "${startCodes}", road.getStartCode(),"黑体",24);
            WordDocumentUtils.replaceTextInDocumentWithFontStyle(document, "${endCodes}", road.getEndCode(),"黑体",24);
            WordDocumentUtils.replaceTextInDocumentWithFontStyle(document, "${year}", String.valueOf(road.getYear()),"黑体",24);
            WordDocumentUtils.replaceTextInDocument(document, "${direction}", direction);
            WordDocumentUtils.replaceTextInDocument(document, "${companyName}", road.getCompanyName());
            WordDocumentUtils.replaceTextInDocument(document, "${projectName}", road.getProjectName());
            WordDocumentUtils.replaceTextInDocument(document, "${roadNameTitle}", road.getRoadName());
            WordDocumentUtils.replaceTextInDocument(document, "${reportNo}", road.getReportNo());
            WordDocumentUtils.replaceTextInDocument(document, "${number}", "04");
            
            // 替换日期相关占位符
            if (dateTime != null && !dateTime.isEmpty()) {
                replacePBITextInDocument(document, "${dateTime}", dateTime);
            }
            if (monthDate != null && !monthDate.isEmpty()) {
                replacePBITextInDocument(document, "${monthDate}", monthDate);
            }

            // 根据dateTime参数生成dateTimeStr并替换
            if (dateTime != null && !dateTime.isEmpty()) {
                String dateTimeStr = dateTime.replace("年", ".").replace("月", ".").replace("日", "");
                replacePBITextInDocument(document, "${dateTimeStr}", dateTimeStr);
            }

            // 计算文档页数并替换页数占位符
            int[] pageCalculations = calculatePBIDocumentPagesWithDetails(summaryData, upData, downData, direction);
            int totalPages = pageCalculations[0];
            int pageOne = pageCalculations[1];
            int pageTwo = pageCalculations[2];

            WordDocumentUtils.replaceTextInDocument(document, "${pages}", String.valueOf(totalPages));
            WordDocumentUtils.replaceTextInDocument(document, "${pageOne}", String.valueOf(pageOne));
            WordDocumentUtils.replaceTextInDocument(document, "${pageTwo}", String.valueOf(pageTwo));

            // 生成对齐的目录内容并替换占位符
            String[] catalogLines = WordDocumentUtils.generateAlignedCatalogLines(road.getRoadName(), direction, pageOne, pageTwo, totalPages);
            WordDocumentUtils.replaceTextInDocumentWithMixedFont(document, "${mulu1}", catalogLines[0]);
            WordDocumentUtils.replaceTextInDocumentWithMixedFont(document, "${mulu2}", catalogLines[1]);

            log.info("{}方向PBI文档共 {} 页, pageOne: {}, pageTwo: {}", direction, totalPages, pageOne, pageTwo);
            log.info("{}方向文档共 {} 页", direction, totalPages);

            return document;

        } catch (Exception e) {
            log.error("创建{}方向PBI Word文档失败", direction, e);
            throw new RuntimeException("创建" + direction + "方向PBI Word文档失败", e);
        } finally {
            // 确保流被关闭
            if (templateStream != null) {
                try {
                    templateStream.close();
                } catch (IOException e) {
                    log.warn("关闭模板流失败: {}", e.getMessage());
                }
            }
        }
    }



    /**
     * 填充PBI汇总表格
     */
    private void fillPBISummaryTable(XWPFTable table, List<RoadCheckPBI> summaryData,
                                     List<RoadCheckPBI> upData, List<RoadCheckPBI> downData) {
        try {
            // 设置表格整体边框
            setPBITableBorders(table);
            
            // 清空表格中除前2行表头外的所有行（汇总表格只有2行表头）
            while (table.getRows().size() > 2) {
                table.removeRow(2);
            }

            // 统计各等级里程数据
            double[] upGradeDistances = new double[5]; // 优良中次差的实际里程
            double[] downGradeDistances = new double[5];
            double totalDistance = 0;

            // 从第3行开始填充数据行（索引从0开始，第3行对应索引2）
            int currentRowIndex = 2;

            // 填充数据行
            for (RoadCheckPBI record : summaryData) {
                // 创建新的数据行（表格现在只有2行表头）
                XWPFTableRow dataRow = table.createRow();
                currentRowIndex++;
                int colIndex = 0;

                // A列：起始桩号
                safeSetPBICellText(dataRow, colIndex++, record.getStartCode() != null ? record.getStartCode() : "");

                // B列：~符号
                WordDocumentUtils.setCellTextWithVerticalCenter(dataRow, colIndex++, "~");

                // C列：结束桩号
                safeSetPBICellText(dataRow, colIndex++, record.getEndCode() != null ? record.getEndCode() : "");

                // D列：计算间距(米)
                double distance = calculateDistance(record.getStartCode(), record.getEndCode());
                totalDistance += distance;
                safeSetPBICellText(dataRow, colIndex++, String.valueOf((int) distance));

                // E列：上行PBI分数
                BigDecimal upPbiScore = calculateThousandPBIScore(record.getThousandSection(), upData);
                safeSetPBICellText(dataRow, colIndex++, String.format("%.2f", upPbiScore.doubleValue()));

                // F列：上行等级
                String upGrade = getGradeByScore(upPbiScore);
                safeSetPBICellText(dataRow, colIndex++, upGrade);

                // 统计上行等级里程
                upGradeDistances[getGradeIndex(upGrade)] += distance;

                // G列：下行PBI分数
                BigDecimal downPbiScore = calculateThousandPBIScore(record.getThousandSection(), downData);
                safeSetPBICellText(dataRow, colIndex++, String.format("%.2f", downPbiScore.doubleValue()));

                // H列：下行等级
                String downGrade = getGradeByScore(downPbiScore);
                safeSetPBICellText(dataRow, colIndex, downGrade);

                // 统计下行等级里程
                downGradeDistances[getGradeIndex(downGrade)] += distance;
            }

            // 添加合计行
            XWPFTableRow totalRow = table.createRow();
            int totalRowIndex = currentRowIndex; // 记录合计行的索引
            currentRowIndex++;
            int colIndex = 0;

            // 合并A-C列显示"合计"
            safeSetPBICellText(totalRow, colIndex++, "合计");
            safeSetPBICellText(totalRow, colIndex++, "");
            safeSetPBICellText(totalRow, colIndex++, "");

            // D列：总里程
            safeSetPBICellText(totalRow, colIndex++, String.valueOf((int) totalDistance));

            // E列：上行总体PBI分数
            BigDecimal upTotalPBI = calculateOverallPBIScore(upData);
            safeSetPBICellText(totalRow, colIndex++, String.format("%.2f", upTotalPBI.doubleValue()));

            // F列：上行总体等级
            safeSetPBICellText(totalRow, colIndex++, getGradeByScore(upTotalPBI));

            // G列：下行总体PBI分数
            BigDecimal downTotalPBI = calculateOverallPBIScore(downData);
            safeSetPBICellText(totalRow, colIndex++, String.format("%.2f", downTotalPBI.doubleValue()));

            // H列：下行总体等级
            safeSetPBICellText(totalRow, colIndex, getGradeByScore(downTotalPBI));

            // 合并合计行的前三列
            mergePBISummaryRowCells(table, totalRowIndex);

            // 添加各等级统计行
            String[] grades = {"优", "良", "中", "次", "差"};
            double totalUpDistance = java.util.Arrays.stream(upGradeDistances).sum();
            double totalDownDistance = java.util.Arrays.stream(downGradeDistances).sum();

            for (int i = 0; i < grades.length; i++) {
                XWPFTableRow gradeRow = table.createRow();
                int gradeRowIndex = currentRowIndex; // 记录等级行的索引
                currentRowIndex++;
                colIndex = 0;

                // 合并A-C列显示等级
                safeSetPBICellText(gradeRow, colIndex++, grades[i] + " (%)");
                safeSetPBICellText(gradeRow, colIndex++, "");
                safeSetPBICellText(gradeRow, colIndex++, "");

                // D列为空
                safeSetPBICellText(gradeRow, colIndex++, "");

                // E列：上行该等级里程（实际里程）
                safeSetPBICellText(gradeRow, colIndex++, String.valueOf((int) upGradeDistances[i]));

                // F列：上行该等级占比
                double upGradeRatio = totalUpDistance > 0 ? (upGradeDistances[i] / totalUpDistance) * 100 : 0;
                safeSetPBICellText(gradeRow, colIndex++, String.format("%.2f%%", upGradeRatio));

                // G列：下行该等级里程（实际里程）
                safeSetPBICellText(gradeRow, colIndex++, String.valueOf((int) downGradeDistances[i]));

                // H列：下行该等级占比
                double downGradeRatio = totalDownDistance > 0 ? (downGradeDistances[i] / totalDownDistance) * 100 : 0;
                safeSetPBICellText(gradeRow, colIndex, String.format("%.2f%%", downGradeRatio));

                // 合并等级行的前三列
                mergePBISummaryRowCells(table, gradeRowIndex);
            }

            log.info("完成填充PBI汇总表格，共 {} 行数据行 + 1行合计 + 5行等级统计", summaryData.size());
        } catch (Exception e) {
            log.error("填充PBI汇总表格失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 填充PBI详细数据表格
     */
    private void fillPBIDetailTable(XWPFTable table, List<RoadCheckPBI> dataList) {
        try {
            // 设置表格整体边框
            setPBITableBorders(table);
            
            // 清空表格中除表头外的所有行（假设前2行是表头）
            while (table.getRows().size() > 2) {
                table.removeRow(2);
            }

            int processedRows = 0;
            for (RoadCheckPBI record : dataList) {
                XWPFTableRow dataRow = table.createRow();
                int colIndex = 0;

                // A列：起始桩号(km)
                safeSetPBICellText(dataRow, colIndex++, record.getStartCode() != null ? record.getStartCode() : "");

                // B列：终点桩号(km)
                safeSetPBICellText(dataRow, colIndex++, record.getEndCode() != null ? record.getEndCode() : "");

                // C列：BPL
                safeSetPBICellText(dataRow, colIndex++, formatPBIDecimalValue(record.getBpl()));

                // D列：BPM
                safeSetPBICellText(dataRow, colIndex++, formatPBIDecimalValue(record.getBpm()));

                // E列：BPH
                safeSetPBICellText(dataRow, colIndex++, formatPBIDecimalValue(record.getBph()));

                // F列：跳车△h (cm)
                safeSetPBICellText(dataRow, colIndex++, formatPBIDecimalValue(record.getBumpHeight()));

                // G列：PBI
                safeSetPBICellText(dataRow, colIndex, formatPBIDecimalValue(record.getPbi()));

                processedRows++;
            }

            log.info("完成填充 {} 行数据到PBI详细表格", processedRows);
        } catch (Exception e) {
            log.error("填充PBI详细数据表格失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 安全设置PBI表格单元格文本（用于检测人员信息表格）
     */
    private void safeSetPBICellText(XWPFTableRow row, int cellIndex, String text) {
        try {
            // 确保有足够的单元格
            while (row.getTableCells().size() <= cellIndex) {
                row.createCell();
            }

            XWPFTableCell cell = row.getCell(cellIndex);
            if (cell != null) {
                // 设置单元格边框
                setPBICellBorders(cell);
                
                // 获取或创建段落
                XWPFParagraph paragraph;
                if (cell.getParagraphs().isEmpty()) {
                    paragraph = cell.addParagraph();
                } else {
                    paragraph = cell.getParagraphs().get(0);
                    // 清空段落中的所有run
                    for (int i = paragraph.getRuns().size() - 1; i >= 0; i--) {
                        paragraph.removeRun(i);
                    }
                }

                // 设置段落居中对齐
                paragraph.setAlignment(ParagraphAlignment.CENTER);

                // 使用分段字体写入：字母/数字及常用符号使用 Times New Roman，其他中文使用宋体
                appendTextWithFonts(paragraph, text != null ? text : "", 10, null, null, null);

                // 设置单元格垂直居中对齐
                cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            }
        } catch (Exception e) {
            log.warn("设置PBI单元格文本失败 [行:{}, 列:{}]: {}",
                    row != null ? row.getTableCells().size() : "null", cellIndex, e.getMessage());
        }
    }

    /**
     * 合并PBI汇总表格行的前三列
     */
    private void mergePBISummaryRowCells(XWPFTable table, int rowIndex) {
        try {
            if (table.getRows().size() > rowIndex) {
                XWPFTableRow row = table.getRow(rowIndex);
                if (row.getTableCells().size() >= 3) {
                    // 获取前三个单元格
                    XWPFTableCell cell0 = row.getCell(0);
                    XWPFTableCell cell1 = row.getCell(1);
                    XWPFTableCell cell2 = row.getCell(2);

                    // 设置单元格合并属性
                    if (cell0.getCTTc().getTcPr() == null) {
                        cell0.getCTTc().addNewTcPr();
                    }
                    if (cell0.getCTTc().getTcPr().getHMerge() == null) {
                        cell0.getCTTc().getTcPr().addNewHMerge().setVal(STMerge.RESTART);
                    }

                    // 第二个和第三个单元格设置为继续合并
                    if (cell1.getCTTc().getTcPr() == null) {
                        cell1.getCTTc().addNewTcPr();
                    }
                    if (cell1.getCTTc().getTcPr().getHMerge() == null) {
                        cell1.getCTTc().getTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
                    }

                    if (cell2.getCTTc().getTcPr() == null) {
                        cell2.getCTTc().addNewTcPr();
                    }
                    if (cell2.getCTTc().getTcPr().getHMerge() == null) {
                        cell2.getCTTc().getTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
                    }

                    // 清空第二个和第三个单元格的内容
                    cell1.getParagraphs().clear();
                    cell1.addParagraph();
                    cell2.getParagraphs().clear();
                    cell2.addParagraph();

                    log.debug("成功合并第{}行的前三列", rowIndex + 1);
                }
            }
        } catch (Exception e) {
            log.warn("合并第{}行前三列失败: {}", rowIndex + 1, e.getMessage());
        }
    }

    /**
     * 替换PBI文档中的占位符
     */
    private void replacePBITextInDocument(XWPFDocument document, String placeholder, String value) {
        try {
            // 替换段落中的文本
            for (XWPFParagraph paragraph : document.getParagraphs()) {
                replacePBIParagraphText(paragraph, placeholder, value);
            }

            // 替换表格中的文本
            for (XWPFTable table : document.getTables()) {
                for (XWPFTableRow row : table.getRows()) {
                    for (XWPFTableCell cell : row.getTableCells()) {
                        for (XWPFParagraph paragraph : cell.getParagraphs()) {
                            replacePBIParagraphText(paragraph, placeholder, value);
                        }
                    }
                }
            }

            // 替换页眉中的文本
            for (XWPFHeader header : document.getHeaderList()) {
                for (XWPFParagraph paragraph : header.getParagraphs()) {
                    replacePBIParagraphText(paragraph, placeholder, value);
                }

                for (XWPFTable table : header.getTables()) {
                    for (XWPFTableRow row : table.getRows()) {
                        for (XWPFTableCell cell : row.getTableCells()) {
                            for (XWPFParagraph paragraph : cell.getParagraphs()) {
                                replacePBIParagraphText(paragraph, placeholder, value);
                            }
                        }
                    }
                }
            }

            // 替换页脚中的文本
            for (XWPFFooter footer : document.getFooterList()) {
                for (XWPFParagraph paragraph : footer.getParagraphs()) {
                    replacePBIParagraphText(paragraph, placeholder, value);
                }

                for (XWPFTable table : footer.getTables()) {
                    for (XWPFTableRow row : table.getRows()) {
                        for (XWPFTableCell cell : row.getTableCells()) {
                            for (XWPFParagraph paragraph : cell.getParagraphs()) {
                                replacePBIParagraphText(paragraph, placeholder, value);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("替换PBI文档占位符失败 [{}]: {}", placeholder, e.getMessage());
        }
    }

    /**
     * 替换PBI段落中的文本（参考RQI方法优化）
     */
    private void replacePBIParagraphText(XWPFParagraph paragraph, String placeholder, String value) {
        try {
            String text = paragraph.getText();
            if (text != null && text.contains(placeholder)) {
                // 对于页眉标题，使用更精确的替换方法，保持原有格式
                if (placeholder.equals("${roadNameTitle}")) {
                    replaceTextInParagraphPreservingFormat(paragraph, placeholder, value != null ? value : "");
                } else {
                    String newText = text.replace(placeholder, value != null ? value : "");

                    // 保存第一个run的字体格式信息（如果存在的话）
                    XWPFRun firstRun = null;
                    String fontFamily = null;
                    Integer fontSize = null;
                    Boolean isBold = null;
                    Boolean isItalic = null;
                    String color = null;
                    
                    if (paragraph.getRuns().size() > 0) {
                        firstRun = paragraph.getRuns().get(0);
                        fontFamily = firstRun.getFontFamily();
                        fontSize = firstRun.getFontSize();
                        isBold = firstRun.isBold();
                        isItalic = firstRun.isItalic();
                        color = firstRun.getColor();
                    }

                    // 清除所有现有的runs
                    int runs = paragraph.getRuns().size();
                    for (int i = runs - 1; i >= 0; i--) {
                        paragraph.removeRun(i);
                    }

                    // 使用分段字体写入：字母/数字及常用符号使用 Times New Roman，其他中文使用宋体
                    appendTextWithFonts(paragraph, newText, fontSize != null && fontSize > 0 ? fontSize : null,
                            isBold, isItalic, color);
                }
            }
        } catch (Exception e) {
            log.warn("替换PBI段落文本失败: {}", e.getMessage());
        }
    }

    /**
     * 在段落中替换文本，完全保持原有格式
     * 这个方法逐个run进行处理，确保格式不被破坏
     */
    private void replaceTextInParagraphPreservingFormat(XWPFParagraph paragraph, String placeholder, String replacement) {
        try {
            List<XWPFRun> runs = paragraph.getRuns();
            if (runs == null || runs.isEmpty()) {
                return;
            }

            // 构建完整的段落文本，同时记录每个字符对应的run索引
            StringBuilder fullText = new StringBuilder();
            List<Integer> charToRunMap = new ArrayList<>();
            
            for (int runIndex = 0; runIndex < runs.size(); runIndex++) {
                XWPFRun run = runs.get(runIndex);
                String runText = run.getText(0);
                if (runText != null) {
                    fullText.append(runText);
                    for (int i = 0; i < runText.length(); i++) {
                        charToRunMap.add(runIndex);
                    }
                }
            }

            String originalText = fullText.toString();
            if (!originalText.contains(placeholder)) {
                return;
            }

            // 找到占位符的位置
            int placeholderStart = originalText.indexOf(placeholder);
            // 确定占位符所在的run以获取格式
            int targetRunIndex = placeholderStart >= 0 && placeholderStart < charToRunMap.size() ? charToRunMap.get(placeholderStart) : 0;
            XWPFRun targetRun = runs.get(targetRunIndex);

            // 保存目标run的格式信息
            Integer fontSize = targetRun.getFontSize();
            Boolean isBold = targetRun.isBold();
            Boolean isItalic = targetRun.isItalic();
            String color = targetRun.getColor();

            // 计算替换后的文本
            String newText = originalText.replace(placeholder, replacement);

            // 清除所有run
            for (int i = runs.size() - 1; i >= 0; i--) {
                paragraph.removeRun(i);
            }

            // 使用分段字体写入：字母/数字及常用符号使用 Times New Roman，其他中文使用宋体
            appendTextWithFonts(paragraph, newText, fontSize != null && fontSize > 0 ? fontSize : null,
                    isBold, isItalic, color);
        } catch (Exception e) {
            log.warn("保持格式的文本替换失败: {}", e.getMessage());
            // 回退方案：简单替换
            try {
                String text = paragraph.getText();
                if (text != null && text.contains(placeholder)) {
                    String fallback = text.replace(placeholder, replacement);
                    for (int i = paragraph.getRuns().size() - 1; i >= 0; i--) {
                        paragraph.removeRun(i);
                    }
                    appendTextWithFonts(paragraph, fallback, null, null, null, null);
                }
            } catch (Exception fallbackException) {
                log.warn("备用文本替换也失败: {}", fallbackException.getMessage());
            }
        }
    }

    /**
     * 将文本按"字母/数字/常见符号"和"中文/其他"分段写入段落，分别设置字体。
     * - 字母/数字/符号（. % + - ( ) ~ / :）使用 Times New Roman
     * - 其他文字使用 宋体
     * 可选择性地继承字号/加粗/斜体/颜色
     */
    private void appendTextWithFonts(XWPFParagraph paragraph, String text, Integer inheritFontSize,
                                     Boolean inheritBold, Boolean inheritItalic, String inheritColor) {
        if (text == null) {
            return;
        }
        StringBuilder segment = new StringBuilder();
        Boolean currentLatin = null;
        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);
            boolean isLatin = isLatinDigitOrSymbol(c);
            if (currentLatin == null) {
                currentLatin = isLatin;
            }
            if (isLatin != currentLatin) {
                // flush previous segment
                createRunWithFont(paragraph, segment.toString(), currentLatin, inheritFontSize, inheritBold, inheritItalic, inheritColor);
                segment.setLength(0);
                currentLatin = isLatin;
            }
            segment.append(c);
        }
        // flush last
        createRunWithFont(paragraph, segment.toString(), currentLatin != null ? currentLatin : false,
                inheritFontSize, inheritBold, inheritItalic, inheritColor);
    }

    private boolean isLatinDigitOrSymbol(char c) {
        if (Character.isDigit(c)) return true;
        if ((c >= 'A' && c <= 'Z') || (c >= 'a' && c <= 'z')) return true;
        switch (c) {
            case '.':
            case '%':
            case '+':
            case '-':
            case '(':
            case ')':
            case '~':
            case '/':
            case ':':
                return true;
            default:
                return false;
        }
    }

    private void createRunWithFont(XWPFParagraph paragraph, String content, boolean latin,
                                   Integer inheritFontSize, Boolean inheritBold, Boolean inheritItalic, String inheritColor) {
        if (content == null || content.isEmpty()) {
            return;
        }
        XWPFRun run = paragraph.createRun();
        run.setText(content);
        // 字体家族
        run.setFontFamily(latin ? "Times New Roman" : "宋体");
        // 字号
        if (inheritFontSize != null && inheritFontSize > 0) {
            run.setFontSize(inheritFontSize);
        } else {
            run.setFontSize(10); // 默认10号
        }
        // 继承样式
        if (inheritBold != null) run.setBold(inheritBold);
        if (inheritItalic != null) run.setItalic(inheritItalic);
        if (inheritColor != null && !inheritColor.isEmpty()) run.setColor(inheritColor);
    }

    /**
     * 格式化PBI小数值
     */
    private String formatPBIDecimalValue(BigDecimal value) {
        if (value == null) {
            return "0.000";
        }
        return String.format("%.3f", value.doubleValue());
    }

    /**
     * 创建PBI Excel表头
     */
    private void createPBIHeader(SXSSFWorkbook workbook, Sheet sheet, String direction, String roadName) {
        // 创建标题样式（中文字体）
        CellStyle titleStyle = createCellStyleWithFont(workbook, true);
        Font titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 16);
        titleFont.setFontName("宋体");
        titleStyle.setFont(titleFont);

        // 创建表头样式（中文字体）
        CellStyle headerStyle = createCellStyleWithFont(workbook, true);
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 11);
        headerFont.setFontName("宋体");
        headerStyle.setFont(headerFont);

        // 创建数据样式（中文字体）
        CellStyle dataStyle = createCellStyleWithFont(workbook, true);

        // 第一行：标题行
        Row titleRow = sheet.createRow(0);
        titleRow.setHeight((short) 600);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue(roadName + "路面跳车十米统计信息表（" + direction + "行车道）");
        titleCell.setCellStyle(titleStyle);

        // 合并A-G列
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 6));
        
        // 为其他被合并的单元格也创建相同的样式
        for (int i = 1; i <= 6; i++) {
            Cell cell = titleRow.createCell(i);
            cell.setCellStyle(titleStyle);
        }

        // 第二行：表头行
        Row headerRow = sheet.createRow(1);
        headerRow.setHeight((short) 400);
        String[] headers = {"起始桩号(km)", "终点桩号(km)", "BPL", "BPM", "BPH", "跳车△h (cm)", "PBI"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        // 设置列宽
        sheet.setColumnWidth(0, 4000);  // 起始桩号
        sheet.setColumnWidth(1, 4000);  // 终点桩号
        sheet.setColumnWidth(2, 3000);  // BPL
        sheet.setColumnWidth(3, 3000);  // BPM
        sheet.setColumnWidth(4, 3000);  // BPH
        sheet.setColumnWidth(5, 4000);  // 跳车△h
        sheet.setColumnWidth(6, 3000);  // PBI


    }

    /**
     * 填充PBI数据
     */
    private void fillPBIData(Sheet sheet, List<RoadCheckPBI> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }

        // 创建数据样式（中文字体用于文本列）
        CellStyle dataStyle = createCellStyleWithFont((SXSSFWorkbook) sheet.getWorkbook(), true);

        // 创建数值样式 - 整数格式（CDE列）- 数字字母字体
        CellStyle integerStyle = createCellStyleWithFont((SXSSFWorkbook) sheet.getWorkbook(), false, "0");

        // 创建数值样式 - 2位小数格式（F列）- 数字字母字体
        CellStyle decimalStyle = createCellStyleWithFont((SXSSFWorkbook) sheet.getWorkbook(), false, "0.00");

        int rowIndex = 2; // 从第3行开始填充数据
        for (RoadCheckPBI record : dataList) {
            Row row = sheet.createRow(rowIndex++);
            row.setHeight((short) 350);
            int colIndex = 0;

            // 起始桩号
            Cell startCell = row.createCell(colIndex++);
            startCell.setCellValue(record.getStartCode() != null ? record.getStartCode() : "");
            startCell.setCellStyle(dataStyle);

            // 结束桩号
            Cell endCell = row.createCell(colIndex++);
            endCell.setCellValue(record.getEndCode() != null ? record.getEndCode() : "");
            endCell.setCellStyle(dataStyle);

            // BPL - C列，保留整数
            Cell bplCell = row.createCell(colIndex++);
            if (record.getBpl() != null) {
                bplCell.setCellValue(record.getBpl().doubleValue());
                bplCell.setCellStyle(integerStyle);
            } else {
                bplCell.setCellValue("");
                bplCell.setCellStyle(dataStyle);
            }

            // BPM - D列，保留整数
            Cell bpmCell = row.createCell(colIndex++);
            if (record.getBpm() != null) {
                bpmCell.setCellValue(record.getBpm().doubleValue());
                bpmCell.setCellStyle(integerStyle);
            } else {
                bpmCell.setCellValue("");
                bpmCell.setCellStyle(dataStyle);
            }

            // BPH - E列，保留整数
            Cell bphCell = row.createCell(colIndex++);
            if (record.getBph() != null) {
                bphCell.setCellValue(record.getBph().doubleValue());
                bphCell.setCellStyle(integerStyle);
            } else {
                bphCell.setCellValue("");
                bphCell.setCellStyle(dataStyle);
            }

            // 跳车高度差 - F列，保留2位小数
            Cell bumpHeightCell = row.createCell(colIndex++);
            if (record.getBumpHeight() != null) {
                bumpHeightCell.setCellValue(record.getBumpHeight().doubleValue());
                bumpHeightCell.setCellStyle(decimalStyle);
            } else {
                bumpHeightCell.setCellValue("");
                bumpHeightCell.setCellStyle(dataStyle);
            }

            // PBI
            Cell pbiCell = row.createCell(colIndex++);
            if (record.getPbi() != null) {
                pbiCell.setCellValue(record.getPbi().doubleValue());
                pbiCell.setCellStyle(decimalStyle);
            } else {
                pbiCell.setCellValue("");
                pbiCell.setCellStyle(dataStyle);
            }
        }
    }

    /**
     * 创建PBI百米统计表头
     */
    private void createPBIHundredHeader(SXSSFWorkbook workbook, Sheet sheet, String direction, String roadName) {
        // 创建标题样式（中文字体）
        CellStyle titleStyle = createCellStyleWithFont(workbook, true);
        Font titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 16);
        titleFont.setFontName("宋体");
        titleStyle.setFont(titleFont);

        // 创建表头样式（中文字体）
        CellStyle headerStyle = createCellStyleWithFont(workbook, true);

        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 11);
        headerFont.setFontName("宋体");
        headerStyle.setFont(headerFont);

        // 第一行：标题行
        Row titleRow = sheet.createRow(0);
        titleRow.setHeight((short) 600);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue(roadName + "路面跳车十米统计信息表（" + direction + "行车道）");
        titleCell.setCellStyle(titleStyle);

        // 合并A-F列
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 5));
        
        // 为其他被合并的单元格也创建相同的样式
        for (int i = 1; i <= 5; i++) {
            Cell cell = titleRow.createCell(i);
            cell.setCellStyle(titleStyle);
        }

        // 第二行：表头行
        Row headerRow = sheet.createRow(1);
        headerRow.setHeight((short) 400);
        String[] headers = {"起始桩号(km)", "终点桩号(km)", "BPL", "BPM", "BPH", "PBI"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        // 设置列宽
        sheet.setColumnWidth(0, 4000);  // 起始桩号
        sheet.setColumnWidth(1, 4000);  // 终点桩号
        sheet.setColumnWidth(2, 3000);  // BPL
        sheet.setColumnWidth(3, 3000);  // BPM
        sheet.setColumnWidth(4, 3000);  // BPH
        sheet.setColumnWidth(5, 3000);  // PBI
    }

    /**
     * 处理百米数据
     */
    private List<RoadCheckPBI> processHundredMeterData(List<RoadCheckPBI> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return new ArrayList<>();
        }

        return dataList.stream()
                .filter(record -> record.getHundredSection() != null)
                .collect(Collectors.groupingBy(RoadCheckPBI::getHundredSection))
                .entrySet().stream()
                .map(entry -> {
                    List<RoadCheckPBI> sectionData = entry.getValue();
                    RoadCheckPBI summary = new RoadCheckPBI();

                    // 使用第一条记录的起始桩号和最后一条记录的结束桩号
                    sectionData.sort((a, b) -> a.getStartCode().compareTo(b.getStartCode()));
                    summary.setStartCode(sectionData.get(0).getStartCode());
                    summary.setEndCode(sectionData.get(sectionData.size() - 1).getEndCode());

                    // 求和BPL、BPM、BPH
                    BigDecimal bplSum = sectionData.stream()
                            .map(RoadCheckPBI::getBpl)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    BigDecimal bpmSum = sectionData.stream()
                            .map(RoadCheckPBI::getBpm)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    BigDecimal bphSum = sectionData.stream()
                            .map(RoadCheckPBI::getBph)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    summary.setBpl(bplSum);
                    summary.setBpm(bpmSum);
                    summary.setBph(bphSum);

                    // 计算PBI分数：100 - BPH*50 - BPM*25，最低0分
                    BigDecimal pbiScore = BigDecimal.valueOf(100)
                            .subtract(bphSum.multiply(BigDecimal.valueOf(50)))
                            .subtract(bpmSum.multiply(BigDecimal.valueOf(25)));

                    if (pbiScore.compareTo(BigDecimal.ZERO) < 0) {
                        pbiScore = BigDecimal.ZERO;
                    }
                    summary.setPbi(pbiScore);

                    return summary;
                })
                .sorted((a, b) -> a.getStartCode().compareTo(b.getStartCode()))
                .collect(Collectors.toList());
    }

    /**
     * 填充PBI百米统计数据
     */
    private void fillPBIHundredData(Sheet sheet, List<RoadCheckPBI> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }

        // 创建数据样式（中文字体用于文本列）
        CellStyle dataStyle = createCellStyleWithFont((SXSSFWorkbook) sheet.getWorkbook(), true);

        // 创建数值样式 - 整数格式（CDE列）- 数字字母字体
        CellStyle integerStyle = createCellStyleWithFont((SXSSFWorkbook) sheet.getWorkbook(), false, "0");

        // 创建数值样式 - 2位小数格式（F列）- 数字字母字体
        CellStyle decimalStyle = createCellStyleWithFont((SXSSFWorkbook) sheet.getWorkbook(), false, "0.00");

        int rowIndex = 2; // 从第3行开始填充数据
        for (RoadCheckPBI record : dataList) {
            Row row = sheet.createRow(rowIndex++);
            row.setHeight((short) 350);
            int colIndex = 0;

            // 起始桩号
            Cell startCell = row.createCell(colIndex++);
            startCell.setCellValue(record.getStartCode() != null ? record.getStartCode() : "");
            startCell.setCellStyle(dataStyle);

            // 结束桩号
            Cell endCell = row.createCell(colIndex++);
            endCell.setCellValue(record.getEndCode() != null ? record.getEndCode() : "");
            endCell.setCellStyle(dataStyle);

            // BPL - C列，保留整数
            Cell bplCell = row.createCell(colIndex++);
            if (record.getBpl() != null) {
                bplCell.setCellValue(record.getBpl().doubleValue());
                bplCell.setCellStyle(integerStyle);
            } else {
                bplCell.setCellValue("");
                bplCell.setCellStyle(dataStyle);
            }

            // BPM - D列，保留整数
            Cell bpmCell = row.createCell(colIndex++);
            if (record.getBpm() != null) {
                bpmCell.setCellValue(record.getBpm().doubleValue());
                bpmCell.setCellStyle(integerStyle);
            } else {
                bpmCell.setCellValue("");
                bpmCell.setCellStyle(dataStyle);
            }

            // BPH - E列，保留整数
            Cell bphCell = row.createCell(colIndex++);
            if (record.getBph() != null) {
                bphCell.setCellValue(record.getBph().doubleValue());
                bphCell.setCellStyle(integerStyle);
            } else {
                bphCell.setCellValue("");
                bphCell.setCellStyle(dataStyle);
            }

            // PBI - F列，保留2位小数
            Cell pbiCell = row.createCell(colIndex++);
            if (record.getPbi() != null) {
                pbiCell.setCellValue(record.getPbi().doubleValue());
                pbiCell.setCellStyle(decimalStyle);
            } else {
                pbiCell.setCellValue("");
                pbiCell.setCellStyle(dataStyle);
            }
        }
    }

    /**
     * 创建汇总表头
     */
    private void createSummaryHeader(SXSSFWorkbook workbook, Sheet sheet, String roadName,
                                     List<RoadCheckPBI> upData, List<RoadCheckPBI> downData) {
        // 获取桩号范围
        List<RoadCheckPBI> allData = new ArrayList<>();
        if (upData != null) allData.addAll(upData);
        if (downData != null) allData.addAll(downData);

        String startCode = "";
        String endCode = "";
        if (!allData.isEmpty()) {
            List<String> stakeCodes = allData.stream()
                    .map(RoadCheckPBI::getStartCode)
                    .filter(code -> code != null && !code.trim().isEmpty())
                    .sorted()
                    .collect(Collectors.toList());

            if (!stakeCodes.isEmpty()) {
                startCode = stakeCodes.get(0);
                Optional<String> maxEndCode = allData.stream()
                        .map(RoadCheckPBI::getEndCode)
                        .filter(code -> code != null && !code.trim().isEmpty())
                        .max(String::compareTo);
                endCode = maxEndCode.orElse(stakeCodes.get(stakeCodes.size() - 1));
            }
        }

        // 创建样式（中文字体）
        CellStyle titleStyle = createCellStyleWithFont(workbook, true);
        Font titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 16);
        titleFont.setFontName("宋体");
        titleStyle.setFont(titleFont);

        CellStyle subtitleStyle = workbook.createCellStyle();
        subtitleStyle.setAlignment(HorizontalAlignment.RIGHT);
        subtitleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        subtitleStyle.setBorderTop(BorderStyle.THIN);
        subtitleStyle.setBorderRight(BorderStyle.THIN);
        subtitleStyle.setBorderBottom(BorderStyle.THIN);
        subtitleStyle.setBorderLeft(BorderStyle.THIN);
        Font subtitleFont = workbook.createFont();
        subtitleFont.setFontHeightInPoints((short) 11);
        subtitleFont.setFontName("宋体");
        subtitleStyle.setFont(subtitleFont);

        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 11);
        headerFont.setFontName("宋体");
        headerStyle.setFont(headerFont);

        // 第1行：路线名称 + "路面跳车指数汇总表"
        Row row1 = sheet.createRow(0);
        row1.setHeight((short) 600);
        Cell cell1 = row1.createCell(0);
        cell1.setCellValue(roadName + "路面跳车指数汇总表");
        cell1.setCellStyle(titleStyle);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 7)); // 合并A-H列
        
        // 为合并的单元格设置样式
        for (int i = 1; i <= 7; i++) {
            Cell cell = row1.createCell(i);
            cell.setCellStyle(titleStyle);
        }

        // 第2行：桩号范围
        Row row2 = sheet.createRow(1);
        row2.setHeight((short) 400);
        Cell cell2 = row2.createCell(0);
        cell2.setCellValue("桩号：" + startCode + "~" + endCode);
        cell2.setCellStyle(subtitleStyle);
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, 7)); // 合并A-H列
        
        // 为合并的单元格设置样式
        for (int i = 1; i <= 7; i++) {
            Cell cell = row2.createCell(i);
            cell.setCellStyle(subtitleStyle);
        }

        // 第3行：主表头
        Row row3 = sheet.createRow(2);
        row3.setHeight((short) 400);
        
        // A-C列：桩号（与第4行合并）
        Cell cellA3 = row3.createCell(0);
        cellA3.setCellValue("桩号");
        cellA3.setCellStyle(headerStyle);
        
        Cell cellB3 = row3.createCell(1);
        cellB3.setCellStyle(headerStyle);
        
        Cell cellC3 = row3.createCell(2);
        cellC3.setCellStyle(headerStyle);

        // D列：段落长度（与第4行合并）
        Cell cellD3 = row3.createCell(3);
        cellD3.setCellValue("段落长度\n(m)");
        cellD3.setCellStyle(headerStyle);

        // E-F列：上行跳车
        Cell cellE3 = row3.createCell(4);
        cellE3.setCellValue("上行跳车");
        cellE3.setCellStyle(headerStyle);
        
        Cell cellF3 = row3.createCell(5);
        cellF3.setCellStyle(headerStyle);

        // G-H列：下行跳车
        Cell cellG3 = row3.createCell(6);
        cellG3.setCellValue("下行跳车");
        cellG3.setCellStyle(headerStyle);
        
        Cell cellH3 = row3.createCell(7);
        cellH3.setCellStyle(headerStyle);

        // 合并第3行的相关单元格
        sheet.addMergedRegion(new CellRangeAddress(2, 3, 0, 2)); // A-C列与第4行合并
        sheet.addMergedRegion(new CellRangeAddress(2, 3, 3, 3)); // D列与第4行合并
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 4, 5)); // E-F列（上行跳车）
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 6, 7)); // G-H列（下行跳车）

        // 第4行：详细表头
        Row row4 = sheet.createRow(3);
        row4.setHeight((short) 400);
        
        // A-D列已被合并，设置样式
        Cell cellA4 = row4.createCell(0);
        cellA4.setCellStyle(headerStyle);
        Cell cellB4 = row4.createCell(1);
        cellB4.setCellStyle(headerStyle);
        Cell cellC4 = row4.createCell(2);
        cellC4.setCellStyle(headerStyle);
        Cell cellD4 = row4.createCell(3);
        cellD4.setCellStyle(headerStyle);

        // E列：路面跳车指数(PBI)
        Cell cellE4 = row4.createCell(4);
        cellE4.setCellValue("指数(PBI)");
        cellE4.setCellStyle(headerStyle);

        // F列：等级
        Cell cellF4 = row4.createCell(5);
        cellF4.setCellValue("等级");
        cellF4.setCellStyle(headerStyle);

        // G列：路面跳车指数(PBI)
        Cell cellG4 = row4.createCell(6);
        cellG4.setCellValue("指数(PBI)");
        cellG4.setCellStyle(headerStyle);

        // H列：等级
        Cell cellH4 = row4.createCell(7);
        cellH4.setCellValue("等级");
        cellH4.setCellStyle(headerStyle);

        // 设置列宽
        sheet.setColumnWidth(0, 4000);  // 起始桩号
        sheet.setColumnWidth(1, 1500);  // ~
        sheet.setColumnWidth(2, 4000);  // 结束桩号
        sheet.setColumnWidth(3, 4500);  // 段落长度
        sheet.setColumnWidth(4, 5250);  // 上行PBI
        sheet.setColumnWidth(5, 3000);  // 上行等级
        sheet.setColumnWidth(6, 5250);  // 下行PBI
        sheet.setColumnWidth(7, 3000);  // 下行等级
    }

    /**
     * 处理公里段数据
     */
    private List<RoadCheckPBI> processThousandMeterData(List<RoadCheckPBI> upData, List<RoadCheckPBI> downData) {
        // 合并上下行数据来获取所有不同的公里段
        Set<String> thousandSections = new HashSet<>();
        if (upData != null) {
            thousandSections.addAll(upData.stream()
                    .map(RoadCheckPBI::getThousandSection)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet()));
        }
        if (downData != null) {
            thousandSections.addAll(downData.stream()
                    .map(RoadCheckPBI::getThousandSection)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet()));
        }

        List<RoadCheckPBI> summaryData = new ArrayList<>();
        for (String section : thousandSections) {
            RoadCheckPBI summary = new RoadCheckPBI();
            summary.setThousandSection(section);

            // 获取该公里段的所有数据来确定起始和结束桩号
            List<RoadCheckPBI> sectionData = new ArrayList<>();
            if (upData != null) {
                sectionData.addAll(upData.stream()
                        .filter(record -> section.equals(record.getThousandSection()))
                        .collect(Collectors.toList()));
            }
            if (downData != null) {
                sectionData.addAll(downData.stream()
                        .filter(record -> section.equals(record.getThousandSection()))
                        .collect(Collectors.toList()));
            }

            if (!sectionData.isEmpty()) {
                // 排序并获取起始和结束桩号
                sectionData.sort((a, b) -> a.getStartCode().compareTo(b.getStartCode()));
                summary.setStartCode(sectionData.get(0).getStartCode());
                summary.setEndCode(sectionData.get(sectionData.size() - 1).getEndCode());
            }

            summaryData.add(summary);
        }

        // 按起始桩号排序
        summaryData.sort((a, b) -> {
            if (a.getStartCode() != null && b.getStartCode() != null) {
                return a.getStartCode().compareTo(b.getStartCode());
            }
            return 0;
        });

        return summaryData;
    }

    /**
     * 填充汇总数据
     */
    private void fillSummaryData(Sheet sheet, List<RoadCheckPBI> summaryData,
                                 List<RoadCheckPBI> upData, List<RoadCheckPBI> downData) {
        // 创建数据样式（中文字体用于文本列）
        CellStyle dataStyle = createCellStyleWithFont((SXSSFWorkbook) sheet.getWorkbook(), true);

        // 创建数值样式 - 整数格式（D列）- 数字字母字体
        CellStyle integerStyle = createCellStyleWithFont((SXSSFWorkbook) sheet.getWorkbook(), false, "0");

        // 创建数值样式 - 2位小数格式（其他列）- 数字字母字体
        CellStyle decimalStyle = createCellStyleWithFont((SXSSFWorkbook) sheet.getWorkbook(), false, "0.00");

        // 创建合计行样式（中文字体，加粗）
        CellStyle totalStyle = createCellStyleWithFont((SXSSFWorkbook) sheet.getWorkbook(), true);
        Font totalFont = sheet.getWorkbook().createFont();
        totalFont.setBold(true);
        totalFont.setFontHeightInPoints((short) 10);
        totalFont.setFontName("宋体");
        totalStyle.setFont(totalFont);

        int rowIndex = 4; // 从第5行开始填充数据

        // 统计各等级里程数据
        double[] upGradeDistances = new double[5]; // 优良中次差的实际里程
        double[] downGradeDistances = new double[5];
        double totalDistance = 0;

        // 填充数据行
        for (RoadCheckPBI record : summaryData) {
            Row dataRow = sheet.createRow(rowIndex++);
            dataRow.setHeight((short) 350);
            int colIndex = 0;

            // A列：起始桩号
            Cell cellA = dataRow.createCell(colIndex++);
            cellA.setCellValue(record.getStartCode() != null ? record.getStartCode() : "");
            cellA.setCellStyle(dataStyle);

            // B列：~符号
            Cell cellB = dataRow.createCell(colIndex++);
            cellB.setCellValue("~");
            cellB.setCellStyle(dataStyle);

            // C列：结束桩号
            Cell cellC = dataRow.createCell(colIndex++);
            cellC.setCellValue(record.getEndCode() != null ? record.getEndCode() : "");
            cellC.setCellStyle(dataStyle);

            // D列：计算间距(米) - 保留整数
            double distance = calculateDistance(record.getStartCode(), record.getEndCode());
            totalDistance += distance;
            Cell cellD = dataRow.createCell(colIndex++);
            cellD.setCellValue(distance);
            cellD.setCellStyle(integerStyle);

            // E列：上行PBI分数
            BigDecimal upPbiScore = calculateThousandPBIScore(record.getThousandSection(), upData);
            Cell cellE = dataRow.createCell(colIndex++);
            cellE.setCellValue(upPbiScore.doubleValue());
            cellE.setCellStyle(decimalStyle);

            // F列：上行等级
            String upGrade = getGradeByScore(upPbiScore);
            Cell cellF = dataRow.createCell(colIndex++);
            cellF.setCellValue(upGrade);
            cellF.setCellStyle(dataStyle);

            // 统计上行等级里程
            upGradeDistances[getGradeIndex(upGrade)] += distance;

            // G列：下行PBI分数
            BigDecimal downPbiScore = calculateThousandPBIScore(record.getThousandSection(), downData);
            Cell cellG = dataRow.createCell(colIndex++);
            cellG.setCellValue(downPbiScore.doubleValue());
            cellG.setCellStyle(decimalStyle);

            // H列：下行等级
            String downGrade = getGradeByScore(downPbiScore);
            Cell cellH = dataRow.createCell(colIndex++);
            cellH.setCellValue(downGrade);
            cellH.setCellStyle(dataStyle);

            // 统计下行等级里程
            downGradeDistances[getGradeIndex(downGrade)] += distance;
        }

        // 添加合计行
        Row totalRow = sheet.createRow(rowIndex++);
        totalRow.setHeight((short) 400);
        
        Cell totalCellA = totalRow.createCell(0);
        totalCellA.setCellValue("合计");
        totalCellA.setCellStyle(totalStyle);
        
        Cell totalCellB = totalRow.createCell(1);
        totalCellB.setCellValue("");
        totalCellB.setCellStyle(totalStyle);
        
        Cell totalCellC = totalRow.createCell(2);
        totalCellC.setCellValue("");
        totalCellC.setCellStyle(totalStyle);
        
        Cell totalCellD = totalRow.createCell(3);
        totalCellD.setCellValue(totalDistance);
        totalCellD.setCellStyle(totalStyle);

        // 计算总体PBI分数和等级
        BigDecimal upTotalPBI = calculateOverallPBIScore(upData);
        Cell totalCellE = totalRow.createCell(4);
        totalCellE.setCellValue(upTotalPBI.doubleValue());
        totalCellE.setCellStyle(totalStyle);
        
        Cell totalCellF = totalRow.createCell(5);
        totalCellF.setCellValue(getGradeByScore(upTotalPBI));
        totalCellF.setCellStyle(totalStyle);

        BigDecimal downTotalPBI = calculateOverallPBIScore(downData);
        Cell totalCellG = totalRow.createCell(6);
        totalCellG.setCellValue(downTotalPBI.doubleValue());
        totalCellG.setCellStyle(totalStyle);
        
        Cell totalCellH = totalRow.createCell(7);
        totalCellH.setCellValue(getGradeByScore(downTotalPBI));
        totalCellH.setCellStyle(totalStyle);

        // 添加各等级统计行
        String[] grades = {"优", "良", "中", "次", "差"};
        double totalUpDistance = java.util.Arrays.stream(upGradeDistances).sum();
        double totalDownDistance = java.util.Arrays.stream(downGradeDistances).sum();

        for (int i = 0; i < grades.length; i++) {
            Row gradeRow = sheet.createRow(rowIndex++);
            gradeRow.setHeight((short) 350);
            
            Cell gradeCellA = gradeRow.createCell(0);
            gradeCellA.setCellValue(grades[i] + " (%)");
            gradeCellA.setCellStyle(dataStyle);
            
            Cell gradeCellB = gradeRow.createCell(1);
            gradeCellB.setCellValue("");
            gradeCellB.setCellStyle(dataStyle);
            
            Cell gradeCellC = gradeRow.createCell(2);
            gradeCellC.setCellValue("");
            gradeCellC.setCellStyle(dataStyle);
            
            Cell gradeCellD = gradeRow.createCell(3);
            gradeCellD.setCellValue("");
            gradeCellD.setCellStyle(dataStyle);

            // 上行该等级里程和占比
            Cell gradeCellE = gradeRow.createCell(4);
            gradeCellE.setCellValue(upGradeDistances[i]);
            gradeCellE.setCellStyle(decimalStyle);
            
            double upGradeRatio = totalUpDistance > 0 ? (upGradeDistances[i] / totalUpDistance) * 100 : 0;
            Cell gradeCellF = gradeRow.createCell(5);
            gradeCellF.setCellValue(upGradeRatio);
            gradeCellF.setCellStyle(decimalStyle);

            // 下行该等级里程和占比
            Cell gradeCellG = gradeRow.createCell(6);
            gradeCellG.setCellValue(downGradeDistances[i]);
            gradeCellG.setCellStyle(decimalStyle);
            
            double downGradeRatio = totalDownDistance > 0 ? (downGradeDistances[i] / totalDownDistance) * 100 : 0;
            Cell gradeCellH = gradeRow.createCell(7);
            gradeCellH.setCellValue(downGradeRatio);
            gradeCellH.setCellStyle(decimalStyle);
        }
    }

    /**
     * 计算桩号距离
     */
    private double calculateDistance(String startCode, String endCode) {
        if (startCode == null || endCode == null) {
            return 1000.0; // 默认1公里
        }

        try {
            // 使用 StakeCodeUtil 中的静态方法
            long startMeters = StakeCodeUtil.parseStakeCodeToMeters(startCode);
            long endMeters = StakeCodeUtil.parseStakeCodeToMeters(endCode);
            if (startMeters >= 0 && endMeters >= 0) {
                return Math.abs(endMeters - startMeters);
            }
            return 1000.0; // 默认1公里
        } catch (Exception e) {
            log.warn("计算桩号距离失败: {} to {}", startCode, endCode);
            return 1000.0; // 默认1公里
        }
    }



    /**
     * 计算千米段的PBI分数
     */
    private BigDecimal calculateThousandPBIScore(String thousandSection, List<RoadCheckPBI> dataList) {
        if (dataList == null || dataList.isEmpty() || thousandSection == null) {
            return BigDecimal.valueOf(100); // 默认100分
        }

        // 筛选该千米段的数据
        List<RoadCheckPBI> sectionData = dataList.stream()
                .filter(record -> thousandSection.equals(record.getThousandSection()))
                .collect(Collectors.toList());

        if (sectionData.isEmpty()) {
            return BigDecimal.valueOf(100);
        }

        // 求和BPM和BPH
        BigDecimal bpmSum = sectionData.stream()
                .map(RoadCheckPBI::getBpm)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal bphSum = sectionData.stream()
                .map(RoadCheckPBI::getBph)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 计算PBI分数：100 - BPH*50 - BPM*25，最低0分
        BigDecimal pbiScore = BigDecimal.valueOf(100)
                .subtract(bphSum.multiply(BigDecimal.valueOf(50)))
                .subtract(bpmSum.multiply(BigDecimal.valueOf(25)));

        if (pbiScore.compareTo(BigDecimal.ZERO) < 0) {
            pbiScore = BigDecimal.ZERO;
        }

        return pbiScore;
    }

    /**
     * 计算总体PBI分数
     */
    private BigDecimal calculateOverallPBIScore(List<RoadCheckPBI> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return BigDecimal.valueOf(100);
        }

        // 求所有数据的BPM和BPH总和
        BigDecimal bpmSum = dataList.stream()
                .map(RoadCheckPBI::getBpm)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal bphSum = dataList.stream()
                .map(RoadCheckPBI::getBph)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 计算PBI分数：100 - BPH*50 - BPM*25，最低0分
        BigDecimal pbiScore = BigDecimal.valueOf(100)
                .subtract(bphSum.multiply(BigDecimal.valueOf(50)))
                .subtract(bpmSum.multiply(BigDecimal.valueOf(25)));

        if (pbiScore.compareTo(BigDecimal.ZERO) < 0) {
            pbiScore = BigDecimal.ZERO;
        }

        return pbiScore;
    }

    /**
     * 根据PBI分数获取等级
     */
    private String getGradeByScore(BigDecimal score) {
        if (score == null) {
            return "差";
        }

        double scoreValue = score.doubleValue();
        if (scoreValue >= 90) {
            return "优";
        } else if (scoreValue >= 80) {
            return "良";
        } else if (scoreValue >= 70) {
            return "中";
        } else if (scoreValue >= 60) {
            return "次";
        } else {
            return "差";
        }
    }

    /**
     * 获取等级对应的索引
     */
    private int getGradeIndex(String grade) {
        switch (grade) {
            case "优": return 0;
            case "良": return 1;
            case "中": return 2;
            case "次": return 3;
            case "差": return 4;
            default: return 4;
        }
    }

    /**
     * 导出Word版PBI报告（分上行下行）
     */
    @Override
    public void exportWordPBIByDirection(HttpServletResponse response, Long roadId, Long teamId, String dateTime, String monthDate, String titleName, String checkName, String reviewName) {
        try {
            // 查询指定路线的PBI数据
            RoadCheckPBI query = new RoadCheckPBI();
            query.setRoadId(roadId);
            query.setDirection(1); // 上行
            List<RoadCheckPBI> upData = roadCheckPBIMapper.selectRoadCheckBumpList(query);

            query.setDirection(2); // 下行
            List<RoadCheckPBI> downData = roadCheckPBIMapper.selectRoadCheckBumpList(query);

            // 检查是否有数据可以导出
            if (CollectionUtils.isEmpty(upData) && CollectionUtils.isEmpty(downData)) {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500, \"msg\":\"没有PBI数据可以导出\"}");
                return;
            }

            Road road = roadMapper.selectRoadById(roadId);
            if (Objects.isNull(road)) {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500, \"msg\":\"路线不存在\"}");
                return;
            }

            // 合并上行和下行数据来获取完整的桩号范围
            List<RoadCheckPBI> allData = new ArrayList<>();
            if (upData != null && !upData.isEmpty()) allData.addAll(upData);
            if (downData != null && !downData.isEmpty()) allData.addAll(downData);
            // 生成上行和下行两个独立的Word文档，并打包成ZIP
            generateSeparatePBIWordDocuments(response, upData, downData,road, teamId, dateTime, monthDate, titleName, checkName, reviewName);

        } catch (Exception e) {
            log.error("导出PBI Word报告失败", e);
            try {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500, \"msg\":\"导出失败：" + e.getMessage() + "\"}");
            } catch (IOException ioException) {
                log.error("导出错误响应失败", ioException);
            }
        }
    }

    /**
     * 设置PBI表格整体边框
     */
    private void setPBITableBorders(XWPFTable table) {
        try {
            // 获取或创建表格属性
            if (table.getCTTbl().getTblPr() == null) {
                table.getCTTbl().addNewTblPr();
            }

            // 获取或创建表格边框属性
            if (table.getCTTbl().getTblPr().getTblBorders() == null) {
                table.getCTTbl().getTblPr().addNewTblBorders();
            }

            // 创建边框样式
            org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder border = 
                org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder.Factory.newInstance();
            border.setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STBorder.SINGLE);
            border.setSz(java.math.BigInteger.valueOf(6)); // 外边框稍微粗一点
            border.setSpace(java.math.BigInteger.valueOf(0));
            border.setColor("000000"); // 黑色边框

            // 设置表格外边框
            table.getCTTbl().getTblPr().getTblBorders().setTop(border);
            table.getCTTbl().getTblPr().getTblBorders().setBottom((org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder) border.copy());
            table.getCTTbl().getTblPr().getTblBorders().setLeft((org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder) border.copy());
            table.getCTTbl().getTblPr().getTblBorders().setRight((org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder) border.copy());

            // 创建内边框样式（稍微细一点）
            org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder insideBorder = 
                org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder.Factory.newInstance();
            insideBorder.setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STBorder.SINGLE);
            insideBorder.setSz(java.math.BigInteger.valueOf(4));
            insideBorder.setSpace(java.math.BigInteger.valueOf(0));
            insideBorder.setColor("000000");

            // 设置表格内边框
            table.getCTTbl().getTblPr().getTblBorders().setInsideH(insideBorder);
            table.getCTTbl().getTblPr().getTblBorders().setInsideV((org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder) insideBorder.copy());

        } catch (Exception e) {
            log.warn("设置PBI表格边框失败: {}", e.getMessage());
        }
    }

    /**
     * 设置PBI单元格边框
     */
    private void setPBICellBorders(XWPFTableCell cell) {
        try {
            // 获取或创建单元格属性
            if (cell.getCTTc().getTcPr() == null) {
                cell.getCTTc().addNewTcPr();
            }

            // 获取或创建边框属性
            if (cell.getCTTc().getTcPr().getTcBorders() == null) {
                cell.getCTTc().getTcPr().addNewTcBorders();
            }

            // 设置所有边框（上、下、左、右）
            org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder border = 
                org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder.Factory.newInstance();
            border.setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STBorder.SINGLE);
            border.setSz(java.math.BigInteger.valueOf(4)); // 边框宽度
            border.setSpace(java.math.BigInteger.valueOf(0));
            border.setColor("000000"); // 黑色边框

            // 设置四个边的边框
            cell.getCTTc().getTcPr().getTcBorders().setTop(border);
            cell.getCTTc().getTcPr().getTcBorders().setBottom((org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder) border.copy());
            cell.getCTTc().getTcPr().getTcBorders().setLeft((org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder) border.copy());
            cell.getCTTc().getTcPr().getTcBorders().setRight((org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder) border.copy());

        } catch (Exception e) {
            log.warn("设置PBI单元格边框失败: {}", e.getMessage());
        }
    }

    @Override
    public int deleteRoadCheckBumpByRoadId(Long roadId) {
        return roadCheckPBIMapper.deleteRoadCheckBumpByRoadId(roadId);
    }

    /**
     * 计算PBI Word文档的页数（返回详细信息）
     * @return int数组 [总页数, pageOne, pageTwo]
     */
    private int[] calculatePBIDocumentPagesWithDetails(List<RoadCheckPBI> summaryData, List<RoadCheckPBI> upData, 
                                                      List<RoadCheckPBI> downData, String direction) {
        try {
            // 基础页数
            int basePages = 0;
            
            // 计算汇总表格页数
            int summaryRows = summaryData != null ? summaryData.size() : 0;
            int summaryPages = calculateTablePages(summaryRows + 6, 38, 41); // +6是固定的合计行和等级行
            
            // 根据方向确定当前数据行数
            List<RoadCheckPBI> currentDirectionData = "上行".equals(direction) ? upData : downData;
            int dataRows = currentDirectionData != null ? currentDirectionData.size() : 0;
            
            // 计算数据表格页数
            int dataPages = calculateTablePages(dataRows, 40, 42);
            
            // 计算 pageOne = 汇总表格的总页码 + 6
            int pageOne = summaryPages + basePages;
            
            // 计算 pageTwo = pageOne + 1
            int pageTwo = pageOne + 1;
            
            // 总页数计算
            int totalPages = basePages + summaryPages + dataPages;
            
            log.info("PBI页数计算详情：基础页数={}, 汇总行数={}, 汇总页数={}, {}数据行数={}, 数据页数={}, 总页数={}, pageOne={}, pageTwo={}", 
                     basePages, summaryRows, summaryPages, direction, dataRows, dataPages, totalPages, pageOne, pageTwo);
            
            return new int[]{totalPages, pageOne, pageTwo};
            
        } catch (Exception e) {
            log.warn("计算PBI文档页数失败，使用默认值: {}", e.getMessage());
            return new int[]{10, 13, 14}; // 默认返回页数
        }
    }

    /**
     * 计算PBI Word文档的页数（保持向后兼容性）
     * 考虑第一页的特殊行数限制：
     * - 汇总表格第一页只有38行数据，后续页41行
     * - 上/下行表格第一页只有40行数据，后续页42行
     * 
     * @param summaryData 汇总数据
     * @param upData 上行数据
     * @param downData 下行数据
     * @param direction 当前方向
     * @return 文档页数
     */
    private int calculatePBIDocumentPages(List<RoadCheckPBI> summaryData, List<RoadCheckPBI> upData, 
                                         List<RoadCheckPBI> downData, String direction) {
        int[] calculations = calculatePBIDocumentPagesWithDetails(summaryData, upData, downData, direction);
        return calculations[0]; // 返回总页数
    }

    /**
     * 计算表格页数，考虑第一页和后续页的不同行数限制
     * 
     * @param totalRows 总行数
     * @param firstPageRows 第一页可容纳的行数
     * @param otherPageRows 后续页可容纳的行数
     * @return 所需页数
     */
    private int calculateTablePages(int totalRows, int firstPageRows, int otherPageRows) {
        if (totalRows <= 0) {
            return 0;
        }
        
        if (totalRows <= firstPageRows) {
            // 第一页就能容纳所有数据
            return 1;
        } else {
            // 需要多页
            int remainingRows = totalRows - firstPageRows; // 除第一页外的剩余行数
            int additionalPages = (int) Math.ceil((double) remainingRows / otherPageRows); // 额外需要的页数
            return 1 + additionalPages; // 第一页 + 额外页数
        }
    }

    /**
     * 创建带字体的单元格样式
     * @param workbook 工作簿
     * @param isChinese 是否为中文字体（宋体），false为数字字母字体（新罗马）
     * @param dataFormat 数据格式，如"0.000"、"0.00"等
     * @return 单元格样式
     */
    private CellStyle createCellStyleWithFont(SXSSFWorkbook workbook, boolean isChinese, String dataFormat) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        
        // 创建字体
        Font font = workbook.createFont();
        if (isChinese) {
            // 中文字体：宋体
            font.setFontName("宋体");
        } else {
            // 数字字母字体：Times New Roman（新罗马）
            font.setFontName("Times New Roman");
        }
        font.setFontHeightInPoints((short) 10);
        style.setFont(font);
        
        // 设置数据格式
        if (dataFormat != null && !dataFormat.isEmpty()) {
            DataFormat format = workbook.createDataFormat();
            style.setDataFormat(format.getFormat(dataFormat));
        }
        
        return style;
    }

    /**
     * 创建带字体的单元格样式（无数据格式）
     * @param workbook 工作簿
     * @param isChinese 是否为中文字体（宋体），false为数字字母字体（新罗马）
     * @return 单元格样式
     */
    private CellStyle createCellStyleWithFont(SXSSFWorkbook workbook, boolean isChinese) {
        return createCellStyleWithFont(workbook, isChinese, null);
    }

}