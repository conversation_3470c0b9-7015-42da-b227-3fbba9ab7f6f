<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.CheckTeamUserMapper">
    
    <resultMap type="com.tunnel.domain.CheckTeamUser" id="CheckTeamUserResult">
        <result property="id"                  column="id"                  />
        <result property="teamId"              column="team_id"             />
        <result property="userId"              column="user_id"             />
        <result property="sortNum"             column="sort_num"            />
        <result property="type"                column="type"                />
        <result property="teamName"            column="team_name"           />
        <result property="userName"            column="user_name"           />
        <result property="userPosition"        column="user_position"       />
        <result property="userTitle"           column="user_title"          />
        <result property="userCertificateNo"   column="user_certificate_no" />
        <result property="userPhone"           column="user_phone"          />
        <result property="userEmail"           column="user_email"          />
        <result property="createTime"          column="create_time"         />
        <result property="updateTime"          column="update_time"         />
    </resultMap>

    <resultMap type="com.tunnel.domain.CheckUser" id="CheckUserResult">
        <result property="id"            column="id"            />
        <result property="userName"      column="user_name"     />
        <result property="position"      column="position"      />
        <result property="certificateNo" column="certificate_no"/>
        <result property="title"         column="title"         />
        <result property="signature"     column="signature"     />
        <result property="phone"         column="phone"         />
        <result property="email"         column="email"         />
        <result property="sortNum"       column="sort_num"      />
        <result property="type"          column="type"          />
        <result property="creator"       column="creator"       />
        <result property="createTime"    column="create_time"   />
        <result property="updateTime"    column="update_time"   />
    </resultMap>

    <sql id="selectCheckTeamUserVo">
        select tu.id, tu.team_id, tu.user_id, tu.sort_num, tu.type, tu.create_time, tu.update_time,
               t.team_name,
               u.user_name, u.position as user_position, u.title as user_title, 
               u.certificate_no as user_certificate_no, u.phone as user_phone, u.email as user_email
        from sc_check_team_user tu
        left join sc_check_team t on tu.team_id = t.id
        left join sc_check_user u on tu.user_id = u.id
    </sql>

    <select id="selectCheckTeamUserList" parameterType="com.tunnel.domain.CheckTeamUser" resultMap="CheckTeamUserResult">
        <include refid="selectCheckTeamUserVo"/>
        <where>
            <if test="teamId != null "> and tu.team_id = #{teamId}</if>
            <if test="userId != null "> and tu.user_id = #{userId}</if>
            <if test="userName != null and userName != ''"> and u.user_name like concat('%', #{userName}, '%')</if>
            <if test="userPosition != null and userPosition != ''"> and u.position like concat('%', #{userPosition}, '%')</if>
            <if test="type != null"> and tu.type = #{type}</if>
        </where>
        order by tu.team_id asc, tu.sort_num asc, tu.create_time desc
    </select>
    
    <select id="selectCheckTeamUserById" parameterType="Long" resultMap="CheckTeamUserResult">
        <include refid="selectCheckTeamUserVo"/>
        where tu.id = #{id}
    </select>
    
    <select id="selectUsersByTeamId" parameterType="Long" resultMap="CheckTeamUserResult">
        <include refid="selectCheckTeamUserVo"/>
        where tu.team_id = #{teamId}
        <if test="type != null"> and tu.type = #{type}</if>
        order by tu.sort_num asc, tu.create_time desc
    </select>
    
    <select id="selectCheckUsersByTeamId" parameterType="Long" resultMap="CheckUserResult">
        select u.id, u.user_name, u.position, u.certificate_no, u.title, u.signature, 
               u.phone, u.email, tu.sort_num, u.type, u.creator, u.create_time, u.update_time
        from sc_check_team_user tu
        inner join sc_check_user u on tu.user_id = u.id
        where tu.team_id = #{teamId}
        <if test="type != null"> and tu.type = #{type}</if>
        order by tu.sort_num asc, u.create_time desc
    </select>
    
    <select id="selectCheckUsersByTeamIdAndPosition" resultMap="CheckUserResult">
        select u.id, u.user_name, u.position, u.certificate_no, u.title, u.signature, 
               u.phone, u.email, tu.sort_num, u.type, u.creator, u.create_time, u.update_time
        from sc_check_team_user tu
        inner join sc_check_user u on tu.user_id = u.id
        where tu.team_id = #{teamId} and u.position = #{position}
        <if test="type != null"> and tu.type = #{type}</if>
        order by tu.sort_num asc, u.create_time desc
    </select>
    
    <select id="selectTeamsByUserId" parameterType="Long" resultMap="CheckTeamUserResult">
        <include refid="selectCheckTeamUserVo"/>
        where tu.user_id = #{userId}
        <if test="type != null"> and tu.type = #{type}</if>
        order by t.team_name asc, tu.sort_num asc
    </select>
    
    <select id="checkTeamUserExists" resultMap="CheckTeamUserResult">
        <include refid="selectCheckTeamUserVo"/>
        where tu.team_id = #{teamId} and tu.user_id = #{userId}
        limit 1
    </select>
    
    <select id="getNextSortNumByTeamId" parameterType="Long" resultType="Integer">
        select IFNULL(MAX(sort_num), 0) + 1 from sc_check_team_user 
        where team_id = #{teamId}
    </select>
        
    <insert id="insertCheckTeamUser" parameterType="com.tunnel.domain.CheckTeamUser" useGeneratedKeys="true" keyProperty="id">
        insert into sc_check_team_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teamId != null">team_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="sortNum != null">sort_num,</if>
            <if test="type != null">type,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teamId != null">#{teamId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="sortNum != null">#{sortNum},</if>
            <if test="type != null">#{type},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCheckTeamUser" parameterType="com.tunnel.domain.CheckTeamUser">
        update sc_check_team_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="teamId != null">team_id = #{teamId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="sortNum != null">sort_num = #{sortNum},</if>
            <if test="type != null">type = #{type},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCheckTeamUserById" parameterType="Long">
        delete from sc_check_team_user where id = #{id}
    </delete>

    <delete id="deleteCheckTeamUserByIds" parameterType="String">
        delete from sc_check_team_user where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteCheckTeamUserByTeamId" parameterType="Long">
        delete from sc_check_team_user where team_id = #{teamId}
    </delete>

    <delete id="deleteCheckTeamUserByUserId" parameterType="Long">
        delete from sc_check_team_user where user_id = #{userId}
    </delete>

    <insert id="batchInsertCheckTeamUser" parameterType="java.util.List">
        insert into sc_check_team_user (team_id, user_id, sort_num, type, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.teamId}, #{item.userId}, #{item.sortNum}, #{item.type}, #{item.createTime})
        </foreach>
    </insert>

    <update id="batchUpdateSort">
        update sc_check_team_user
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="sort_num = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id = #{item.id} then #{item.sortNum}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>

</mapper> 