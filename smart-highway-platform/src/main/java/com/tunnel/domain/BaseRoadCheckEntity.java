package com.tunnel.domain;

import java.util.Date;

/**
 * 路面检测数据基础实体类
 * 包含所有检测数据实体类的公共属性
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
public abstract class BaseRoadCheckEntity {
    
    /** 主键ID */
    private Long id;
    
    /** 道路ID */
    private Long roadId;
    
    /** 起始桩号 */
    private String startCode;
    
    /** 结束桩号 */
    private String endCode;
    
    /** 百米段 */
    private String hundredSection;
    
    /** 公里段 */
    private String thousandSection;
    
    /** 行驶方向（1=上行，2=下行） */
    private Integer direction;
    
    /** 路面类型 */
    private String roadType;
    
    /** 创建时间 */
    private Date createTime;
    
    /** 更新时间 */
    private Date updateTime;
    
    /** 创建者ID */
    private Long creator;
    
    /** 修改者ID */
    private Long modifier;

    // Getter and Setter methods
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getRoadId() {
        return roadId;
    }

    public void setRoadId(Long roadId) {
        this.roadId = roadId;
    }

    public String getStartCode() {
        return startCode;
    }

    public void setStartCode(String startCode) {
        this.startCode = startCode;
    }

    public String getEndCode() {
        return endCode;
    }

    public void setEndCode(String endCode) {
        this.endCode = endCode;
    }

    public String getHundredSection() {
        return hundredSection;
    }

    public void setHundredSection(String hundredSection) {
        this.hundredSection = hundredSection;
    }

    public String getThousandSection() {
        return thousandSection;
    }

    public void setThousandSection(String thousandSection) {
        this.thousandSection = thousandSection;
    }

    public Integer getDirection() {
        return direction;
    }

    public void setDirection(Integer direction) {
        this.direction = direction;
    }

    public String getRoadType() {
        return roadType;
    }

    public void setRoadType(String roadType) {
        this.roadType = roadType;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreator() {
        return creator;
    }

    public void setCreator(Long creator) {
        this.creator = creator;
    }

    public Long getModifier() {
        return modifier;
    }

    public void setModifier(Long modifier) {
        this.modifier = modifier;
    }
} 