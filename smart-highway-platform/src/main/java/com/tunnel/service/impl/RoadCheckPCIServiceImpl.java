package com.tunnel.service.impl;

import com.tunnel.common.core.domain.entity.SysUser;
import com.tunnel.common.utils.*;
import com.tunnel.domain.BatchAddResponse;
import com.tunnel.domain.Road;
import com.tunnel.domain.RoadCheckPCI;
import com.tunnel.mapper.RoadCheckPCIMapper;
import com.tunnel.mapper.RoadMapper;
import com.tunnel.service.CheckTeamService;
import com.tunnel.service.RoadCheckPCIService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 路面破损信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-28
 */
@Service
@Slf4j
public class RoadCheckPCIServiceImpl implements RoadCheckPCIService {
    @Autowired
    private RoadCheckPCIMapper roadCheckPCIMapper;

    // 每页导出数量
    private static final int PAGE_SIZE = 5000;

    // 导出线程池大小
    private static final int EXPORT_THREADS = 5;
    @Autowired
    private RoadMapper roadMapper;

    @Autowired
    private CheckTeamService checkTeamService;

    /**
     * 查询路面破损信息
     *
     * @param id 路面破损信息主键
     * @return 路面破损信息
     */
    @Override
    public RoadCheckPCI selectRoadCheckRecordById(Long id) {
        return roadCheckPCIMapper.selectRoadCheckRecordById(id);
    }

    /**
     * 查询路面破损信息列表
     *
     * @param roadCheckPCI 路面破损信息
     * @return 路面破损信息
     */
    @Override
    public List<RoadCheckPCI> selectRoadCheckRecordList(RoadCheckPCI roadCheckPCI) {
        return roadCheckPCIMapper.selectRoadCheckRecordList(roadCheckPCI);
    }

    /**
     * 新增路面破损信息
     *
     * @param roadCheckPCI 路面破损信息
     * @return 结果
     */
    @Override
    public int insertRoadCheckRecord(RoadCheckPCI roadCheckPCI) {
        roadCheckPCI.setCreateTime(DateUtils.getNowDate());
        roadCheckPCI.setUpdateTime(DateUtils.getNowDate());

        // 格式化桩号为标准格式
        if (roadCheckPCI.getStartCode() != null && !roadCheckPCI.getStartCode().isEmpty()) {
            String formattedStartCode = StakeCodeUtil.formatStakeCode(roadCheckPCI.getStartCode());
            roadCheckPCI.setStartCode(formattedStartCode);
            roadCheckPCI.setHundredSection(StakeCodeUtil.calculateHundredSection(formattedStartCode));
            roadCheckPCI.setThousandSection(StakeCodeUtil.calculateThousandSection(formattedStartCode));
        }

        if (roadCheckPCI.getEndCode() != null && !roadCheckPCI.getEndCode().isEmpty()) {
            String formattedEndCode = StakeCodeUtil.formatStakeCode(roadCheckPCI.getEndCode());
            roadCheckPCI.setEndCode(formattedEndCode);
        }

        return roadCheckPCIMapper.insertRoadCheckRecord(roadCheckPCI);
    }

    /**
     * 修改路面破损信息
     *
     * @param roadCheckPCI 路面破损信息
     * @return 结果
     */
    @Override
    public int updateRoadCheckRecord(RoadCheckPCI roadCheckPCI) {
        roadCheckPCI.setUpdateTime(DateUtils.getNowDate());

        // 格式化桩号为标准格式
        if (roadCheckPCI.getStartCode() != null && !roadCheckPCI.getStartCode().isEmpty()) {
            String formattedStartCode = StakeCodeUtil.formatStakeCode(roadCheckPCI.getStartCode());
            roadCheckPCI.setStartCode(formattedStartCode);
            roadCheckPCI.setHundredSection(StakeCodeUtil.calculateHundredSection(formattedStartCode));
            roadCheckPCI.setThousandSection(StakeCodeUtil.calculateThousandSection(formattedStartCode));
        }

        if (roadCheckPCI.getEndCode() != null && !roadCheckPCI.getEndCode().isEmpty()) {
            String formattedEndCode = StakeCodeUtil.formatStakeCode(roadCheckPCI.getEndCode());
            roadCheckPCI.setEndCode(formattedEndCode);
        }

        return roadCheckPCIMapper.updateRoadCheckRecord(roadCheckPCI);
    }

    /**
     * 批量删除路面破损信息
     *
     * @param ids 需要删除的路面破损信息主键
     * @return 结果
     */
    @Override
    public int deleteRoadCheckRecordByIds(Long[] ids) {
        return roadCheckPCIMapper.deleteRoadCheckRecordByIds(ids);
    }

    /**
     * 删除路面破损信息信息
     *
     * @param id 路面破损信息主键
     * @return 结果
     */
    @Override
    public int deleteRoadCheckRecordById(Long id) {
        return roadCheckPCIMapper.deleteRoadCheckRecordById(id);
    }

    /**
     * 根据道路ID获取路面破损信息
     *
     * @param roadId 道路ID
     * @return 路面破损信息集合
     */
    @Override
    public List<RoadCheckPCI> selectRoadCheckRecordByRoadId(Long roadId) {
        return roadCheckPCIMapper.selectRoadCheckRecordByRoadId(roadId);
    }

    /**
     * 批量导入路面破损状况检测数据
     * 适配"路面破损状况评定（PCI）检测记录"表格
     *
     * @param file 导入文件
     * @return 导入结果
     */
    @Override
    @Transactional
    public BatchAddResponse batchImport(MultipartFile file, Long roadId) {
        BatchAddResponse addResponse = new BatchAddResponse();
        InputStream fileStream = null;
        try {
            // 导入前先删除该道路的所有PCI数据
            int deletedCount = deleteRoadCheckRecordByRoadId(roadId);
            log.info("导入前删除道路ID {} 的PCI数据：{} 条", roadId, deletedCount);

            // 生成本地缓存路径
            String localFile = FileUtil.saveExcelFile(file);
            fileStream = new FileInputStream(localFile);
            BufferedInputStream bufferedInputStream = new BufferedInputStream(fileStream);

            // 读取Excel文件
            Workbook workbook;
            if (file.getOriginalFilename().endsWith(".xlsx")) {
                workbook = WorkbookFactory.create(bufferedInputStream);
            } else {
                addResponse.setStatus(1);
                addResponse.setMsg("请使用Excel 2007及以上版本的文件（.xlsx格式）！");
                addResponse.generateSummary();
                return addResponse;
            }

            List<RoadCheckPCI> resultList = new ArrayList<>();

            // 处理Sheet1（上行数据）
            Sheet sheet1 = workbook.getSheetAt(0);
            if (sheet1 != null) {
                List<RoadCheckPCI> upDirectionData = processSheetWithValidation(sheet1, roadId, "上行", "Sheet1", addResponse);
                resultList.addAll(upDirectionData);
                log.info("Sheet1(上行)处理数据：{} 条", upDirectionData.size());
            }

            // 处理Sheet2（下行数据）
            if (workbook.getNumberOfSheets() > 1) {
                Sheet sheet2 = workbook.getSheetAt(1);
                if (sheet2 != null) {
                    List<RoadCheckPCI> downDirectionData = processSheetWithValidation(sheet2, roadId, "下行", "Sheet2", addResponse);
                    resultList.addAll(downDirectionData);
                    log.info("Sheet2(下行)处理数据：{} 条", downDirectionData.size());
                }
            }

            // 根据roadType分组并处理段位信息
            if (!resultList.isEmpty()) {
                log.info("开始处理PCI数据的段位信息，总计 {} 条记录", resultList.size());
                StakeCodeUtil.processSectionsByRoadType(resultList);
            }


            // 只插入校验通过的数据
            if (!resultList.isEmpty()) {
                List<List<RoadCheckPCI>> splitList = CollectUtil.splitList(resultList, 1000);
                for (List<RoadCheckPCI> tempList : splitList) {
                    roadCheckPCIMapper.batchInsert(tempList);
                }
                addResponse.addSuccessCount(resultList.size());
                log.info("成功导入数据：{} 条", resultList.size());

                // 输出最后一行数据的段位信息用于验证
                if (!resultList.isEmpty()) {
                    RoadCheckPCI lastRecord = resultList.get(resultList.size() - 1);
                    log.info("导入数据最后一条记录段位信息：起始桩号={}, 结束桩号={}, 百米段={}, 公里段={}",
                            lastRecord.getStartCode(), lastRecord.getEndCode(),
                            lastRecord.getHundredSection(), lastRecord.getThousandSection());
                }
            }

            // 设置最终状态
            if (addResponse.getSuccessCount() > 0 && addResponse.getFailCount() == 0) {
                addResponse.setStatus(0);
                addResponse.setMsg("导入成功");
            } else if (addResponse.getSuccessCount() > 0 && addResponse.getFailCount() > 0) {
                addResponse.setStatus(0);
                addResponse.setMsg("部分导入成功");
            } else {
                addResponse.setStatus(1);
                addResponse.setMsg("导入失败");
            }

            addResponse.generateSummary();
            workbook.close();

        } catch (IOException e) {
            log.error("文件解析失败", e);
            addResponse.setStatus(1);
            addResponse.setMsg("文件解析失败: " + e.getMessage());
            addResponse.generateSummary();
        } catch (Exception e) {
            log.error("导入数据失败", e);
            addResponse.setStatus(1);
            addResponse.setMsg("导入数据失败: " + e.getMessage());
            addResponse.generateSummary();
        } finally {
            try {
                if (fileStream != null) {
                    fileStream.close();
                }
            } catch (IOException e) {
                log.error("excel文件读取失败, 失败原因：{}", e);
            }
        }
        return addResponse;
    }

    /**
     * 处理单个工作表的数据（包含详细校验）
     * 只负责读取和校验字段，不处理段位信息
     *
     * @param sheet 工作表
     * @param roadId 道路ID
     * @param direction 行驶方向（上行/下行）
     * @param sheetName 工作表名称
     * @param addResponse 响应对象
     * @return 解析后的数据列表
     */
    private List<RoadCheckPCI> processSheetWithValidation(Sheet sheet, Long roadId, String direction,
                                                          String sheetName, BatchAddResponse addResponse) {
        List<RoadCheckPCI> resultList = new ArrayList<>();

        // 从第3行开始读取数据（索引从0开始，第3行对应索引2），跳过表头和标题行
        int startRow = 2;
        int rowCount = sheet.getPhysicalNumberOfRows();

        if (rowCount <= startRow) {
            log.warn("工作表 {} 中没有找到有效数据行", sheetName);
            return resultList;
        }

        // 将方向字符串转换为Integer值
        Integer directionValue = "上行".equals(direction) ? 1 : 2;

        // 预处理：收集所有有效的数据行，用于判断最后一行
        List<Integer> validRowIndices = new ArrayList<>();
        for (int i = startRow; i < rowCount; i++) {
            Row row = sheet.getRow(i);
            if (row == null) {
                continue;
            }

            // 检查第一列是否为空（起始桩号）
            Cell stakeCell = row.getCell(0);
            if (stakeCell == null || getCellStringValue(stakeCell).trim().isEmpty()) {
                // 检查是否为空行
                boolean isEmptyRow = true;
                for (int j = 0; j < 17; j++) { // PCI现在有17列数据（新增了路面类型列）
                    Cell cell = row.getCell(j);
                    if (cell != null && !getCellStringValue(cell).trim().isEmpty()) {
                        isEmptyRow = false;
                        break;
                    }
                }
                if (!isEmptyRow) {
                    validRowIndices.add(i); // 非空行但起始桩号为空，仍算作有效行（会报错）
                }
            } else {
                validRowIndices.add(i); // 有起始桩号的行
            }
        }

        // 遍历数据行
        for (int idx = 0; idx < validRowIndices.size(); idx++) {
            int i = validRowIndices.get(idx);

            Row row = sheet.getRow(i);
            if (row == null) {
                continue;
            }

            int excelRowNum = i + 1; // Excel行号从1开始
            boolean rowHasError = false;

            // 检查第一列是否为空（起始桩号）
            Cell stakeCell = row.getCell(0);
            if (stakeCell == null || getCellStringValue(stakeCell).trim().isEmpty()) {
                // 跳过前4行的错误报告（表头行）
                if (excelRowNum > 4) {
                    addResponse.addError(sheetName, excelRowNum, "起始桩号", "起始桩号不能为空");
                }
                rowHasError = true;
            }

            try {
                RoadCheckPCI record = new RoadCheckPCI();

                // 校验和设置起始桩号（只格式化，不计算段位）
                String startCode = getCellStringValue(row.getCell(0));
                String endCode = getCellStringValue(row.getCell(1));

                if (startCode != null && !startCode.trim().isEmpty()) {
                    // 格式化桩号为标准格式
                    String formattedStartCode = StakeCodeUtil.formatStakeCode(startCode.trim());
                    record.setStartCode(formattedStartCode);

                    // 格式化结束桩号
                    if (endCode != null && !endCode.trim().isEmpty()) {
                        String formattedEndCode = StakeCodeUtil.formatStakeCode(endCode.trim());
                        record.setEndCode(formattedEndCode);
                    }
                } else {
                    // 跳过前4行的错误报告（表头行）
                    if (excelRowNum > 4) {
                        addResponse.addError(sheetName, excelRowNum, "起始桩号", "起始桩号不能为空");
                    }
                    rowHasError = true;
                }

                // 校验和设置终止桩号
                if (endCode != null && !endCode.trim().isEmpty()) {
                    // 格式化桩号为标准格式
                    if (record.getEndCode() == null) {
                        String formattedEndCode = StakeCodeUtil.formatStakeCode(endCode.trim());
                        record.setEndCode(formattedEndCode);
                    }
                } else {
                    // 跳过前4行的错误报告（表头行）
                    if (excelRowNum > 4) {
                        addResponse.addError(sheetName, excelRowNum, "终止桩号", "终止桩号不能为空");
                    }
                    rowHasError = true;
                }

                // 校验各类破损面积数据
                BigDecimal crackArea = validateAndGetDecimalValue(row.getCell(2), sheetName, excelRowNum, "裂缝面积", addResponse);
                if (crackArea != null) record.setCrackArea(crackArea); else rowHasError = true;

                BigDecimal blockCrackArea = validateAndGetDecimalValue(row.getCell(3), sheetName, excelRowNum, "网状开裂面积", addResponse);
                if (blockCrackArea != null) record.setBlockCrackArea(blockCrackArea); else rowHasError = true;

                BigDecimal longitudinalCrackArea = validateAndGetDecimalValue(row.getCell(4), sheetName, excelRowNum, "纵向裂缝面积", addResponse);
                if (longitudinalCrackArea != null) record.setLongitudinalCrackArea(longitudinalCrackArea); else rowHasError = true;

                BigDecimal transverseCrackArea = validateAndGetDecimalValue(row.getCell(5), sheetName, excelRowNum, "横向裂缝面积", addResponse);
                if (transverseCrackArea != null) record.setTransverseCrackArea(transverseCrackArea); else rowHasError = true;

                BigDecimal sinkArea = validateAndGetDecimalValue(row.getCell(6), sheetName, excelRowNum, "沉陷面积", addResponse);
                if (sinkArea != null) record.setSinkArea(sinkArea); else rowHasError = true;

                BigDecimal rutArea = validateAndGetDecimalValue(row.getCell(7), sheetName, excelRowNum, "车辙面积", addResponse);
                if (rutArea != null) record.setRutArea(rutArea); else rowHasError = true;

                BigDecimal waveBumpArea = validateAndGetDecimalValue(row.getCell(8), sheetName, excelRowNum, "波浪拥包面积", addResponse);
                if (waveBumpArea != null) record.setWaveBumpArea(waveBumpArea); else rowHasError = true;

                BigDecimal pitArea = validateAndGetDecimalValue(row.getCell(9), sheetName, excelRowNum, "坑槽面积", addResponse);
                if (pitArea != null) record.setPitArea(pitArea); else rowHasError = true;

                BigDecimal looseArea = validateAndGetDecimalValue(row.getCell(10), sheetName, excelRowNum, "松散面积", addResponse);
                if (looseArea != null) record.setLooseArea(looseArea); else rowHasError = true;

                BigDecimal bleedingArea = validateAndGetDecimalValue(row.getCell(11), sheetName, excelRowNum, "泛油面积", addResponse);
                if (bleedingArea != null) record.setBleedingArea(bleedingArea); else rowHasError = true;

                BigDecimal patchAreaPart = validateAndGetDecimalValue(row.getCell(12), sheetName, excelRowNum, "修补面积（部分）", addResponse);
                if (patchAreaPart != null) record.setPatchAreaPart(patchAreaPart); else rowHasError = true;

                BigDecimal patchAreaStrip = validateAndGetDecimalValue(row.getCell(13), sheetName, excelRowNum, "修补面积（条带）", addResponse);
                if (patchAreaStrip != null) record.setPatchAreaStrip(patchAreaStrip); else rowHasError = true;

                // 校验破损率和PCI
                BigDecimal damageRate = validateAndGetDecimalValue(row.getCell(14), sheetName, excelRowNum, "破损率DR(%)", addResponse);
                if (damageRate != null) record.setDamageRate(damageRate); else rowHasError = true;

                BigDecimal pci = validateAndGetDecimalValue(row.getCell(15), sheetName, excelRowNum, "PCI", addResponse);
                if (pci != null) record.setPci(pci); else rowHasError = true;

                // 校验和设置路面类型（第17列，索引16）
                String roadType = getCellStringValue(row.getCell(16));
                if (roadType != null && !roadType.trim().isEmpty()) {
                    record.setRoadType(roadType.trim());
                } else {
                    // 如果路面类型为空，设置默认值
                    record.setRoadType("沥青路面");
                }

                // 只有当行没有错误时才添加到结果列表
                if (!rowHasError) {
                    record.setDirection(directionValue);
                    record.setRoadId(roadId);
                    record.setCreateTime(new Date());

                    SysUser user = SecurityUtils.getLoginUser().getUser();
                    record.setCreator(user.getUserId());
                    record.setModifier(user.getUserId());

                    resultList.add(record);
                }

            } catch (Exception e) {
                log.warn("处理第 {} 行数据时出错: {}", excelRowNum, e.getMessage());
                addResponse.addError(sheetName, excelRowNum, "数据处理", "处理数据时发生错误：" + e.getMessage());
            }
        }
        return resultList;
    }



    /**
     * 校验并获取BigDecimal值
     */
    private BigDecimal validateAndGetDecimalValue(Cell cell, String sheetName, int rowNum,
                                                  String fieldName, BatchAddResponse addResponse) {
        if (cell == null) {
            // 跳过前4行的错误报告（表头行）
            if (rowNum > 4) {
                addResponse.addError(sheetName, rowNum, fieldName, fieldName + "不能为空");
            }
            return null;
        }

        String cellValue = getCellStringValue(cell);
        if (cellValue.trim().isEmpty()) {
            // 跳过前4行的错误报告（表头行）
            if (rowNum > 4) {
                addResponse.addError(sheetName, rowNum, fieldName, fieldName + "不能为空", cellValue);
            }
            return null;
        }

        try {
            BigDecimal value = getCellBigDecimalValue(cell);
            if (value.compareTo(BigDecimal.ZERO) < 0) {
                // 跳过前4行的错误报告（表头行）
                if (rowNum > 4) {
                    addResponse.addError(sheetName, rowNum, fieldName, fieldName + "不能为负数", cellValue);
                }
                return null;
            }
            return value;
        } catch (Exception e) {
            // 跳过前4行的错误报告（表头行）
            if (rowNum > 4) {
                addResponse.addError(sheetName, rowNum, fieldName, fieldName + "格式不正确，应为数值", cellValue);
            }
            return null;
        }
    }

    /**
     * 获取符合条件的总记录数
     *
     * @param roadCheckPCI 查询条件
     * @return 总记录数
     */
    @Override
    public int countRoadCheckRecord(RoadCheckPCI roadCheckPCI) {
        return roadCheckPCIMapper.countRoadCheckRecord(roadCheckPCI);
    }

    /**
     * 导出路面破损信息列表（大数据量优化版）
     *
     * 优化策略：
     * 1. 使用SXSSFWorkbook提高导出性能
     * 2. 分页查询数据，避免一次性加载大量数据到内存
     * 3. 使用多线程并行处理数据，提高导出速度
     *
     * @param response HTTP响应
     * @param roadCheckPCI 查询条件
     */
    @Override
    public void exportOptimized(HttpServletResponse response, RoadCheckPCI roadCheckPCI) {
        try {
            // 获取总记录数
            int total = roadCheckPCIMapper.countRoadCheckRecord(roadCheckPCI);
            if (total <= 0) {
                response.setContentType("application/json");
                response.getWriter().write("{\"code\":500, \"msg\":\"没有数据可以导出\"}");
                return;
            }

            // 使用SXSSFWorkbook，提高大数据量导出性能
            SXSSFWorkbook workbook = new SXSSFWorkbook(100); // 内存中保留100行，其余写入临时文件
            Sheet sheet = workbook.createSheet("路面破损信息数据");

            // 创建表头
            createHeader(workbook, sheet);

            // 计算总页数
            int totalPages = (total + PAGE_SIZE - 1) / PAGE_SIZE;

            // 使用线程池和CountDownLatch并行处理数据
            ExecutorService executorService = Executors.newFixedThreadPool(Math.min(EXPORT_THREADS, totalPages));
            CountDownLatch latch = new CountDownLatch(totalPages);

            // 创建行数据同步锁，确保多线程写入Excel时顺序正确
            Object rowLock = new Object();
            final int[] rowIndex = {1}; // 从第1行开始写数据（第0行是表头）

            // 分页查询并写入数据
            for (int pageNum = 0; pageNum < totalPages; pageNum++) {
                final int offset = pageNum * PAGE_SIZE;
                executorService.execute(() -> {
                    try {
                        List<RoadCheckPCI> list = roadCheckPCIMapper.selectRoadCheckRecordListByPage(
                                roadCheckPCI, offset, PAGE_SIZE);
                        if (!list.isEmpty()) {
                            synchronized (rowLock) {
                                for (RoadCheckPCI record : list) {
                                    Row row = sheet.createRow(rowIndex[0]++);
                                    fillRowData(row, record);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("导出数据处理异常", e);
                    } finally {
                        latch.countDown();
                    }
                });
            }

            // 等待所有线程完成
            latch.await();
            executorService.shutdown();

            // 设置响应头
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String fileName = "路面破损信息_" + sdf.format(new Date()) + ".xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes("gb2312"), "ISO8859-1"));

            // 写入响应流
            workbook.write(response.getOutputStream());

            // 清理临时文件
            workbook.dispose();

        } catch (Exception e) {
            log.error("导出路面破损信息失败", e);
            try {
                response.setContentType("application/json");
                response.getWriter().write("{\"code\":500, \"msg\":\"导出失败：" + e.getMessage() + "\"}");
            } catch (IOException ioException) {
                log.error("导出错误响应失败", ioException);
            }
        }
    }

    /**
     * 创建Excel表头
     */
    private void createHeader(SXSSFWorkbook workbook, Sheet sheet) {
        // 设置列宽
        sheet.setColumnWidth(0, 15 * 256);
        sheet.setColumnWidth(1, 15 * 256);
        sheet.setColumnWidth(2, 15 * 256);
        sheet.setColumnWidth(3, 15 * 256);

        // 创建表头样式
        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);

        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);

        // 创建表头行
        Row headerRow = sheet.createRow(0);

        // 创建表头单元格
        String[] headers = {
                "起始桩号", "终止桩号", "龟裂(m²)", "块状裂缝(m²)",
                "纵向裂缝(m²)", "横向裂缝(m²)", "沉陷(m²)", "车辙(m²)",
                "波浪拥包(m²)", "坑槽(m²)", "松散(m²)", "泛油(m²)",
                "修补-块状(m²)", "修补-条状(m²)", "破损率DR(%)", "PCI", "路面类型"
        };

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
    }

    /**
     * 填充行数据
     */
    private void fillRowData(Row row, RoadCheckPCI record) {
        // 基本数据格式
        CellStyle dataStyle = row.getSheet().getWorkbook().createCellStyle();
        dataStyle.setAlignment(HorizontalAlignment.CENTER);
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);

        // 数值格式，保留3位小数
        CellStyle numberStyle = row.getSheet().getWorkbook().createCellStyle();
        numberStyle.cloneStyleFrom(dataStyle);
        DataFormat format = row.getSheet().getWorkbook().createDataFormat();
        numberStyle.setDataFormat(format.getFormat("0.000"));

        // 数值格式，保留2位小数
        CellStyle number2Style = row.getSheet().getWorkbook().createCellStyle();
        number2Style.cloneStyleFrom(dataStyle);
        number2Style.setDataFormat(format.getFormat("0.00"));

        // 填充数据
        int colIndex = 0;

        // 桩号信息
        Cell cell0 = row.createCell(colIndex++);
        cell0.setCellValue(record.getStartCode());
        cell0.setCellStyle(dataStyle);

        Cell cell1 = row.createCell(colIndex++);
        cell1.setCellValue(record.getEndCode());
        cell1.setCellStyle(dataStyle);

        // 面积数据 - 保留3位小数
        setDecimalCell(row, colIndex++, record.getCrackArea(), numberStyle);
        setDecimalCell(row, colIndex++, record.getBlockCrackArea(), numberStyle);
        setDecimalCell(row, colIndex++, record.getLongitudinalCrackArea(), numberStyle);
        setDecimalCell(row, colIndex++, record.getTransverseCrackArea(), numberStyle);
        setDecimalCell(row, colIndex++, record.getSinkArea(), numberStyle);
        setDecimalCell(row, colIndex++, record.getRutArea(), numberStyle);
        setDecimalCell(row, colIndex++, record.getWaveBumpArea(), numberStyle);
        setDecimalCell(row, colIndex++, record.getPitArea(), numberStyle);
        setDecimalCell(row, colIndex++, record.getLooseArea(), numberStyle);
        setDecimalCell(row, colIndex++, record.getBleedingArea(), numberStyle);
        setDecimalCell(row, colIndex++, record.getPatchAreaPart(), numberStyle);
        setDecimalCell(row, colIndex++, record.getPatchAreaStrip(), numberStyle);

        // 破损率和PCI - 保留2位小数
        setDecimalCell(row, colIndex++, record.getDamageRate(), number2Style);
        setDecimalCell(row, colIndex++, record.getPci(), number2Style);

        // 路面类型
        Cell roadTypeCell = row.createCell(colIndex);
        roadTypeCell.setCellValue(record.getRoadType() != null ? record.getRoadType() : "沥青路面");
        roadTypeCell.setCellStyle(dataStyle);
    }

    /**
     * 设置小数单元格
     */
    private void setDecimalCell(Row row, int colIndex, java.math.BigDecimal value, CellStyle style) {
        Cell cell = row.createCell(colIndex);
        if (value != null) {
            cell.setCellValue(value.doubleValue());
        } else {
            cell.setCellValue(0.0);
        }
        cell.setCellStyle(style);
    }

    private String getCellStringValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        try {
            switch (cell.getCellType()) {
                case STRING:
                    return cell.getStringCellValue().trim();
                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        return new SimpleDateFormat("yyyy-MM-dd").format(cell.getDateCellValue());
                    }
                    // 对于数字，转为字符串并去除小数点后的零
                    String value = String.valueOf(cell.getNumericCellValue());
                    if (value.endsWith(".0")) {
                        value = value.substring(0, value.length() - 2);
                    }
                    return value;
                case BOOLEAN:
                    return String.valueOf(cell.getBooleanCellValue());
                case FORMULA:
                    try {
                        return String.valueOf(cell.getNumericCellValue());
                    } catch (Exception e) {
                        return cell.getStringCellValue();
                    }
                default:
                    return "";
            }
        } catch (Exception e) {
            log.warn("获取单元格字符串值失败: {}", e.getMessage());
            return "";
        }
    }

    /**
     * 获取单元格的BigDecimal值
     */
    private BigDecimal getCellBigDecimalValue(Cell cell) {
        if (cell == null) {
            return BigDecimal.ZERO;
        }

        try {
            switch (cell.getCellType()) {
                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        return null;
                    }
                    return BigDecimal.valueOf(cell.getNumericCellValue());
                case STRING:
                    String str = cell.getStringCellValue().trim();
                    if (str.isEmpty()) {
                        return BigDecimal.ZERO;
                    }
                    try {
                        return new BigDecimal(str);
                    } catch (NumberFormatException e) {
                        return BigDecimal.ZERO;
                    }
                case BOOLEAN:
                    return cell.getBooleanCellValue() ? BigDecimal.ONE : BigDecimal.ZERO;
                case FORMULA:
                    try {
                        return BigDecimal.valueOf(cell.getNumericCellValue());
                    } catch (Exception e) {
                        return BigDecimal.ZERO;
                    }
                default:
                    return BigDecimal.ZERO;
            }
        } catch (Exception e) {
            log.warn("获取单元格数值失败: {}", e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    /**
     * 导出PCI数据（分上行下行Sheet）- 使用模板文件
     */
    @Override
    public void exportPCIByDirection(HttpServletResponse response, Long roadId) {
        try {
            // 查询指定路线的PCI数据
            RoadCheckPCI query = new RoadCheckPCI();
            query.setRoadId(roadId);
            query.setDirection(1);
            List<RoadCheckPCI> upData = roadCheckPCIMapper.selectRoadCheckRecordList(query);
            query.setDirection(2);
            List<RoadCheckPCI> downData = roadCheckPCIMapper.selectRoadCheckRecordList(query);

            // 检查是否有数据可以导出
            if (CollectionUtils.isEmpty(upData) && CollectionUtils.isEmpty(downData)) {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500, \"msg\":\"没有PCI数据可以导出\"}");
                return;
            }

            // 加载PCI模板文件
            ClassPathResource templateResource = new ClassPathResource("static/pci-template.xlsx");
            InputStream templateStream = templateResource.getInputStream();

            // 创建工作簿，基于模板
            Workbook templateWorkbook = WorkbookFactory.create(templateStream);

            // 创建新的工作簿用于导出
            SXSSFWorkbook workbook = new SXSSFWorkbook(100);

            // 处理上行十米数据 - 第一个Sheet
            if (!upData.isEmpty()) {
                Sheet templateSheet = templateWorkbook.getSheetAt(0); // 使用模板的第一个Sheet
                Sheet upSheet = workbook.createSheet("上行十米");
                copySheetStructure(templateSheet, upSheet, workbook);
                fillPCIDataToTemplate(upSheet, upData);
            } else {
                // 如果没有上行数据，创建空的上行Sheet
                Sheet templateSheet = templateWorkbook.getSheetAt(0);
                Sheet upSheet = workbook.createSheet("上行十米");
                copySheetStructure(templateSheet, upSheet, workbook);
                // 不填充数据，保持空Sheet但有表头
            }

            // 处理下行十米数据 - 第二个Sheet
            if (!downData.isEmpty()) {
                Sheet templateSheet = templateWorkbook.getSheetAt(1); // 使用相同的模板结构
                Sheet downSheet = workbook.createSheet("下行十米");
                copySheetStructure(templateSheet, downSheet, workbook);
                fillPCIDataToTemplate(downSheet, downData);
            } else {
                // 如果没有下行数据，创建空的下行Sheet
                Sheet templateSheet = templateWorkbook.getSheetAt(1);
                Sheet downSheet = workbook.createSheet("下行十米");
                copySheetStructure(templateSheet, downSheet, workbook);
                // 不填充数据，保持空Sheet但有表头
            }

            // 处理上行百米数据 - 第三个Sheet
            Sheet templateSheet2 = templateWorkbook.getSheetAt(2);
            Sheet upHundredSheet = workbook.createSheet("上行百米");
            copySheetStructure(templateSheet2, upHundredSheet, workbook);
            fillAggregatedPCIData(upHundredSheet, upData, "hundred_section");

            // 处理下行百米数据 - 第四个Sheet
            Sheet templateSheet3 = templateWorkbook.getSheetAt(3);
            Sheet downHundredSheet = workbook.createSheet("下行百米");
            copySheetStructure(templateSheet3, downHundredSheet, workbook);
            fillAggregatedPCIData(downHundredSheet, downData, "hundred_section");

            // 处理上行公里数据 - 第五个Sheet
            Sheet templateSheet4 = templateWorkbook.getSheetAt(4);
            Sheet upThousandSheet = workbook.createSheet("上行公里");
            copySheetStructure(templateSheet4, upThousandSheet, workbook);
            fillAggregatedPCIData(upThousandSheet, upData, "thousand_section");

            // 处理下行公里数据 - 第六个Sheet
            Sheet templateSheet5 = templateWorkbook.getSheetAt(5);
            Sheet downThousandSheet = workbook.createSheet("下行公里");
            copySheetStructure(templateSheet5, downThousandSheet, workbook);
            fillAggregatedPCIData(downThousandSheet, downData, "thousand_section");

            // 处理汇总数据 - 第6个Sheet
            Sheet summaryTemplateSheet = templateWorkbook.getSheetAt(6); // 使用模板的第三个Sheet
            Sheet summarySheet = workbook.createSheet("汇总");
            copySummarySheetStructure(summaryTemplateSheet, summarySheet, workbook);
            fillSummaryData(summarySheet, upData, downData);

            // 关闭模板工作簿
            templateWorkbook.close();
            templateStream.close();

            // 设置响应头
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String fileName = "沥青路面PCI检测汇总表_" + sdf.format(new Date()) + ".xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));

            // 写入响应流
            workbook.write(response.getOutputStream());
            workbook.dispose();

        } catch (Exception e) {
            log.error("导出PCI数据失败", e);
            try {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500, \"msg\":\"导出失败：" + e.getMessage() + "\"}");
            } catch (IOException ioException) {
                log.error("导出错误响应失败", ioException);
            }
        }
    }

    @Override
    public void exportWordPCIByDirection(HttpServletResponse response, Long roadId) {
        exportWordPCIByDirection(response, roadId, null, null, null, null, null, null);
    }

    @Override
    public void exportWordPCIByDirection(HttpServletResponse response, Long roadId, Long teamId, String dateTime, String monthDate, String titleName, String checkName, String reviewName) {
        try {
            // 查询指定路线的PCI数据
            RoadCheckPCI query = new RoadCheckPCI();
            query.setRoadId(roadId);
            query.setDirection(1);
            List<RoadCheckPCI> upData = roadCheckPCIMapper.selectRoadCheckRecordList(query);
            query.setDirection(2);
            List<RoadCheckPCI> downData = roadCheckPCIMapper.selectRoadCheckRecordList(query);

            // 检查是否有数据可以导出
            if (CollectionUtils.isEmpty(upData) && CollectionUtils.isEmpty(downData)) {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500, \"msg\":\"没有PCI数据可以导出\"}");
                return;
            }
            Road road= roadMapper.selectRoadById(roadId);
            if(Objects.isNull(road)){
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500, \"msg\":\"路线不存在\"}");
                return;
            }
            // 合并上行和下行数据来获取完整的桩号范围
            List<RoadCheckPCI> allData = new ArrayList<>();
            if (upData != null && !upData.isEmpty()) allData.addAll(upData);
            if (downData != null && !downData.isEmpty()) allData.addAll(downData);
            // 生成上行和下行两个独立的Word文档，并打包成ZIP
            generateSeparateWordDocuments(response, upData, downData, road, teamId, dateTime, monthDate, titleName, checkName, reviewName);

        } catch (Exception e) {
            log.error("导出PCI Word报告失败", e);
            try {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500, \"msg\":\"导出失败：" + e.getMessage() + "\"}");
            } catch (IOException ioException) {
                log.error("导出错误响应失败", ioException);
            }
        }
    }

    /**
     * 生成上行和下行两个独立的Word文档（多线程优化版本）
     */
    private void generateSeparateWordDocuments(HttpServletResponse response,
                                               List<RoadCheckPCI> upData, List<RoadCheckPCI> downData,
                                               Road road,
                                               Long teamId, String dateTime, String monthDate, String titleName, String checkName, String reviewName) {
        try {
            // 创建ZIP输出流
            response.setContentType("application/zip");
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String zipFileName = "路面PCI检测报告_" + sdf.format(new Date()) + ".zip";
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(zipFileName, "UTF-8"));

            ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream());

            // 使用并发工具类
            ExecutorService executor = Executors.newFixedThreadPool(2);
            CountDownLatch latch = new CountDownLatch(2);

            // 用于存储生成的文档和文件名
            final XWPFDocument[] documents = new XWPFDocument[2];
            final String[] fileNames = new String[2];
            final Exception[] exceptions = new Exception[2];

            // 并行生成上行文档
            if (!CollectionUtils.isEmpty(upData)) {
                executor.submit(() -> {
                    try {
                        log.info("开始生成上行Word文档...");
                        documents[0] = createSingleDirectionDocument(upData, downData, road, "上行", teamId, dateTime, monthDate, titleName, checkName, reviewName);
                        fileNames[0] = "路面PCI检测报告_上行_" + sdf.format(new Date()) + ".docx";
                        log.info("上行Word文档生成完成");
                    } catch (Exception e) {
                        log.error("生成上行Word文档失败", e);
                        exceptions[0] = e;
                    } finally {
                        latch.countDown();
                    }
                });
            } else {
                latch.countDown();
            }

            // 并行生成下行文档
            if (!CollectionUtils.isEmpty(downData)) {
                executor.submit(() -> {
                    try {
                        log.info("开始生成下行Word文档...");
                        documents[1] = createSingleDirectionDocument(upData, downData, road, "下行", teamId, dateTime, monthDate, titleName, checkName, reviewName);
                        fileNames[1] = "路面PCI检测报告_下行_" + sdf.format(new Date()) + ".docx";
                        log.info("下行Word文档生成完成");
                    } catch (Exception e) {
                        log.error("生成下行Word文档失败", e);
                        exceptions[1] = e;
                    } finally {
                        latch.countDown();
                    }
                });
            } else {
                latch.countDown();
            }

            // 等待所有文档生成完成
            latch.await();
            executor.shutdown();

            // 检查是否有异常发生
            if (exceptions[0] != null) {
                throw new RuntimeException("生成上行Word文档失败", exceptions[0]);
            }
            if (exceptions[1] != null) {
                throw new RuntimeException("生成下行Word文档失败", exceptions[1]);
            }

            // 将生成的文档写入ZIP
            if (documents[0] != null && fileNames[0] != null) {
                ZipEntry upEntry = new ZipEntry(fileNames[0]);
                zipOut.putNextEntry(upEntry);
                documents[0].write(zipOut);
                zipOut.closeEntry();
                documents[0].close();
                log.info("上行文档已添加到ZIP文件");
            }

            if (documents[1] != null && fileNames[1] != null) {
                ZipEntry downEntry = new ZipEntry(fileNames[1]);
                zipOut.putNextEntry(downEntry);
                documents[1].write(zipOut);
                zipOut.closeEntry();
                documents[1].close();
                log.info("下行文档已添加到ZIP文件");
            }

            zipOut.close();
            log.info("ZIP文件生成完成");

        } catch (Exception e) {
            log.error("生成分离的Word文档失败", e);
            throw new RuntimeException("生成分离的Word文档失败", e);
        }
    }

    /**
     * 创建单个方向的Word文档
     */
    private XWPFDocument createSingleDirectionDocument(List<RoadCheckPCI> upData, List<RoadCheckPCI> downData,
                                                       Road road, String direction,
                                                       Long teamId, String dateTime, String monthDate,
                                                       String titleName, String checkName, String reviewName) {
        InputStream templateStream = null;
        try {
            // 解决POI安全限制：设置ZIP文件的最小压缩比率，避免Zip bomb检测误报
            org.apache.poi.openxml4j.util.ZipSecureFile.setMinInflateRatio(0.001);

            // 加载模板
            ClassPathResource templateResource = new ClassPathResource("static/word/pci-template.docx");
            templateStream = templateResource.getInputStream();
            XWPFDocument document = new XWPFDocument(templateStream);
            log.info("成功加载Word模板文件");
            // 准备汇总数据
            Map<String, SummaryData> summaryMap = new LinkedHashMap<>();
            double totalDistanceSum = 0.0;
            double upExcellentDistance = 0.0;
            double upGoodDistance = 0.0;
            double upMediumDistance = 0.0;
            double upFairDistance = 0.0;
            double upPoorDistance = 0.0;
            double downExcellentDistance = 0.0;
            double downGoodDistance = 0.0;
            double downMediumDistance = 0.0;
            double downFairDistance = 0.0;
            double downPoorDistance = 0.0;

            // 分别处理上行和下行数据，确保两者都被正确添加到summaryMap中
            // 先处理上行数据
            if (upData != null && !upData.isEmpty()) {
                for (RoadCheckPCI record : upData) {
                    String thousandSection = record.getThousandSection();
                    if (thousandSection != null && !thousandSection.trim().isEmpty()) {
                        SummaryData summary = summaryMap.computeIfAbsent(thousandSection, SummaryData::new);
                        summary.addUpDamageRate(record.getDamageRate());
                        log.debug("Word处理上行桩号段: {}, 起始: {}, 结束: {}, 破损率: {}",
                                thousandSection, record.getStartCode(), record.getEndCode(), record.getDamageRate());
                    }
                }
            }

            // 再处理下行数据
            if (downData != null && !downData.isEmpty()) {
                for (RoadCheckPCI record : downData) {
                    String thousandSection = record.getThousandSection();
                    if (thousandSection != null && !thousandSection.trim().isEmpty()) {
                        SummaryData summary = summaryMap.computeIfAbsent(thousandSection, SummaryData::new);
                        summary.addDownDamageRate(record.getDamageRate());
                        log.debug("Word处理下行桩号段: {}, 起始: {}, 结束: {}, 破损率: {}",
                                thousandSection, record.getStartCode(), record.getEndCode(), record.getDamageRate());
                    }
                }
            }

            // 记录处理的数据情况
            log.info("Word汇总表数据处理：上行数据 {} 条，下行数据 {} 条，合并后桩号段 {} 个",
                    upData != null ? upData.size() : 0,
                    downData != null ? downData.size() : 0,
                    summaryMap.size());

            // 计算各等级的公里数
            int dataRowCount = 0;
            double upDamageRateSum = 0.0;
            double upPCISum = 0.0;
            double downDamageRateSum = 0.0;
            double downPCISum = 0.0;

            for (SummaryData summary : summaryMap.values()) {
                double actualDistance = summary.calculateActualDistance(); // 计算实际距离
                totalDistanceSum += actualDistance; // 使用实际距离
                dataRowCount++;

                // 上行数据处理
                BigDecimal upAvgDamageRate = summary.getUpAvgDamageRate();
                if (upAvgDamageRate != null) {
                    double upDamageRateValue = upAvgDamageRate.doubleValue();
                    upDamageRateSum += upDamageRateValue;
                    double upPCI = calculatePCI(upAvgDamageRate);
                    upPCISum += upPCI;

                    // 根据PCI值统计各等级公里数（使用实际距离）
                    String upGrade = getGrade(upPCI);
                    switch(upGrade) {
                        case "优": upExcellentDistance += actualDistance; break;
                        case "良": upGoodDistance += actualDistance; break;
                        case "中": upMediumDistance += actualDistance; break;
                        case "次": upFairDistance += actualDistance; break;
                        case "差": upPoorDistance += actualDistance; break;
                    }

                    log.debug("Word上行数据处理：桩号段 {}, 破损率 {}, PCI {}, 等级 {}",
                            summary.getThousandSection(), upDamageRateValue, upPCI, upGrade);
                }

                // 下行数据处理
                BigDecimal downAvgDamageRate = summary.getDownAvgDamageRate();
                if (downAvgDamageRate != null) {
                    double downDamageRateValue = downAvgDamageRate.doubleValue();
                    downDamageRateSum += downDamageRateValue;
                    double downPCI = calculatePCI(downAvgDamageRate);
                    downPCISum += downPCI;

                    // 根据PCI值统计各等级公里数（使用实际距离）
                    String downGrade = getGrade(downPCI);
                    switch(downGrade) {
                        case "优": downExcellentDistance += actualDistance; break;
                        case "良": downGoodDistance += actualDistance; break;
                        case "中": downMediumDistance += actualDistance; break;
                        case "次": downFairDistance += actualDistance; break;
                        case "差": downPoorDistance += actualDistance; break;
                    }

                    log.debug("Word下行数据处理：桩号段 {}, 破损率 {}, PCI {}, 等级 {}",
                            summary.getThousandSection(), downDamageRateValue, downPCI, downGrade);
                }

                // 记录实际距离计算结果（用于调试）
                log.debug("Word汇总数据段位: {}, 起始桩号: {}, 结束桩号: {}, 实际距离: {}米",
                        summary.getThousandSection(), summary.getStartCode(), summary.getEndCode(), (int)actualDistance);
            }

            // 计算平均值
            double avgUpDamageRate = (dataRowCount > 0) ? upDamageRateSum / dataRowCount : 0;
            double avgUpPCI = (dataRowCount > 0) ? upPCISum / dataRowCount : 0;
            double avgDownDamageRate = (dataRowCount > 0) ? downDamageRateSum / dataRowCount : 0;
            double avgDownPCI = (dataRowCount > 0) ? downPCISum / dataRowCount : 0;

            log.info("Word汇总计算结果 - 上行平均破损率: {}, 上行平均PCI: {}, 下行平均破损率: {}, 下行平均PCI: {}",
                    avgUpDamageRate, avgUpPCI, avgDownDamageRate, avgDownPCI);

            // 查找或创建汇总表格
            List<XWPFTable> tables = document.getTables();
            XWPFTable summaryTable = null;

            // 如果有模板且有表格，使用现有表格
            if (tables != null && tables.size() >= 3) {
                summaryTable = tables.get(2);  // 第3个表格，索引从0开始
            } else {
                // 如果没有表格，创建一个新的汇总表格
                log.info("未找到现有表格，创建新的汇总表格");
                summaryTable = document.createTable();

                // 创建第一行表头（主标题行）
                XWPFTableRow mainHeaderRow = summaryTable.getRow(0);
                while (mainHeaderRow.getTableCells().size() < 10) {
                    mainHeaderRow.addNewTableCell();
                }
                safeSetCellText(mainHeaderRow, 0, "桩号");
                safeSetCellText(mainHeaderRow, 1, "");
                safeSetCellText(mainHeaderRow, 2, "");
                safeSetCellText(mainHeaderRow, 3, "段落长度(m)");
                safeSetCellText(mainHeaderRow, 4, "上行行车道");
                safeSetCellText(mainHeaderRow, 5, "");
                safeSetCellText(mainHeaderRow, 6, "");
                safeSetCellText(mainHeaderRow, 7, "下行行车道");
                safeSetCellText(mainHeaderRow, 8, "");
                safeSetCellText(mainHeaderRow, 9, "");

                // 创建第二行表头（子标题行）
                XWPFTableRow subHeaderRow = summaryTable.createRow();
                while (subHeaderRow.getTableCells().size() < 10) {
                    subHeaderRow.addNewTableCell();
                }
                safeSetCellText(subHeaderRow, 0, "起始");
                safeSetCellText(subHeaderRow, 1, "");
                safeSetCellText(subHeaderRow, 2, "结束");
                safeSetCellText(subHeaderRow, 3, "");
                safeSetCellText(subHeaderRow, 4, "破损率(DR)");
                safeSetCellText(subHeaderRow, 5, "路面损坏状况指数(PCI)");
                safeSetCellText(subHeaderRow, 6, "等级");
                safeSetCellText(subHeaderRow, 7, "破损率(DR)");
                safeSetCellText(subHeaderRow, 8, "路面损坏状况指数(PCI)");
                safeSetCellText(subHeaderRow, 9, "等级");
            }

            if (summaryTable != null) {
                // 清空表格中除表头外的所有行
                while (summaryTable.getRows().size() > 2) {
                    summaryTable.removeRow(2);
                }

                // 首先添加实际的数据行（按公里段分组的数据）
                for (SummaryData summary : summaryMap.values()) {
                    XWPFTableRow dataRow = summaryTable.createRow();
                    // 确保行有足够的单元格（现在需要10列）
                    while (dataRow.getTableCells().size() < 10) {
                        dataRow.createCell();
                    }

                    // 根据新的表格模板设置数据行内容
                    // 第1列：起始桩号
                    safeSetCellText(dataRow, 0, summary.getStartCode());

                    // 第2列：分隔符"~"
                    WordDocumentUtils.setCellTextWithVerticalCenter(dataRow, 1, "~");

                    // 第3列：结束桩号
                    safeSetCellText(dataRow, 2, summary.getEndCode());

                    // 第4列：实际计算的段落长度(m)
                    double actualDistance = summary.calculateActualDistance();
                    safeSetCellText(dataRow, 3, String.valueOf((int)actualDistance));

                    // 上行数据（第5-7列）
                    BigDecimal upAvgDamageRate = summary.getUpAvgDamageRate();
                    if (upAvgDamageRate != null) {
                        double upDamageRateValue = upAvgDamageRate.doubleValue();
                        double upPCI = calculatePCI(upAvgDamageRate);
                        safeSetCellText(dataRow, 4, String.format("%.3f", upDamageRateValue)); // 上行破损率
                        safeSetCellText(dataRow, 5, String.format("%.2f", upPCI)); // 上行PCI
                        safeSetCellText(dataRow, 6, getGrade(upPCI)); // 上行等级
                    } else {
                        safeSetCellText(dataRow, 4, "0.000");
                        safeSetCellText(dataRow, 5, "100.00");
                        safeSetCellText(dataRow, 6, "优");
                    }

                    // 下行数据（第8-10列）
                    BigDecimal downAvgDamageRate = summary.getDownAvgDamageRate();
                    if (downAvgDamageRate != null) {
                        double downDamageRateValue = downAvgDamageRate.doubleValue();
                        double downPCI = calculatePCI(downAvgDamageRate);
                        safeSetCellText(dataRow, 7, String.format("%.3f", downDamageRateValue)); // 下行破损率
                        safeSetCellText(dataRow, 8, String.format("%.2f", downPCI)); // 下行PCI
                        safeSetCellText(dataRow, 9, getGrade(downPCI)); // 下行等级
                    } else {
                        safeSetCellText(dataRow, 7, "0.000");
                        safeSetCellText(dataRow, 8, "100.00");
                        safeSetCellText(dataRow, 9, "优");
                    }

                    // 为数据行的所有单元格设置实线边框
                    setWordTableCellBorders(dataRow);
                }

                // 添加合计行
                XWPFTableRow summaryRow = summaryTable.createRow();
                // 确保行有足够的单元格（现在需要10列）
                while (summaryRow.getTableCells().size() < 10) {
                    summaryRow.createCell();
                }

                // 设置合计行内容
                safeSetCellText(summaryRow, 0, "合计");
                safeSetCellText(summaryRow, 1, "");
                safeSetCellText(summaryRow, 2, "");
                safeSetCellText(summaryRow, 3, String.valueOf((int)totalDistanceSum));
                safeSetCellText(summaryRow, 4, String.format("%.3f", avgUpDamageRate));
                safeSetCellText(summaryRow, 5, String.format("%.2f", avgUpPCI));
                safeSetCellText(summaryRow, 6, getGrade(avgUpPCI));
                safeSetCellText(summaryRow, 7, String.format("%.3f", avgDownDamageRate));
                safeSetCellText(summaryRow, 8, String.format("%.2f", avgDownPCI));
                safeSetCellText(summaryRow, 9, getGrade(avgDownPCI));

                // 为合计行设置实线边框
                setWordTableCellBorders(summaryRow);

                // 合并合计行的前三列（第0,1,2列）
                int summaryRowIndex = summaryTable.getRows().size() - 1;
                mergeCellsHorizontal(summaryTable, summaryRowIndex, 0, 2);

                // 记录Word汇总表生成完成信息
                log.info("Word PCI汇总表生成完成 - 总计实际距离: {}米 (约{:.2f}公里), 数据行数: {}",
                        (int)totalDistanceSum, totalDistanceSum/1000.0, dataRowCount);

                // 添加优等级行
                addWordGradeRowSimple(summaryTable, "优 (%)",
                        upExcellentDistance, (totalDistanceSum > 0) ? upExcellentDistance / totalDistanceSum : 0,
                        downExcellentDistance, (totalDistanceSum > 0) ? downExcellentDistance / totalDistanceSum : 0);

                // 添加良等级行
                addWordGradeRowSimple(summaryTable, "良 (%)",
                        upGoodDistance, (totalDistanceSum > 0) ? upGoodDistance / totalDistanceSum : 0,
                        downGoodDistance, (totalDistanceSum > 0) ? downGoodDistance / totalDistanceSum : 0);

                // 添加中等级行
                addWordGradeRowSimple(summaryTable, "中 (%)",
                        upMediumDistance, (totalDistanceSum > 0) ? upMediumDistance / totalDistanceSum : 0,
                        downMediumDistance, (totalDistanceSum > 0) ? downMediumDistance / totalDistanceSum : 0);

                // 添加次等级行
                addWordGradeRowSimple(summaryTable, "次 (%)",
                        upFairDistance, (totalDistanceSum > 0) ? upFairDistance / totalDistanceSum : 0,
                        downFairDistance, (totalDistanceSum > 0) ? downFairDistance / totalDistanceSum : 0);

                // 添加差等级行
                addWordGradeRowSimple(summaryTable, "差 (%)",
                        upPoorDistance, (totalDistanceSum > 0) ? upPoorDistance / totalDistanceSum : 0,
                        downPoorDistance, (totalDistanceSum > 0) ? downPoorDistance / totalDistanceSum : 0);
            } else {
                log.warn("未找到Word模板中的汇总表格，跳过表格填充");
            }


            // 加载最后一页的详细数据表格并填充数据
            List<XWPFTable> allTables = document.getTables();
            if (allTables != null && allTables.size() > 3) {
                // 获取最后一页的详细数据表格（假设是第4个表格，索引为3）
                XWPFTable detailTable = allTables.get(3);

                // 清空详细表格中除前3行表头外的所有行
                while (detailTable.getRows().size() > 3) {
                    detailTable.removeRow(3);
                }

                // 根据当前文档方向选择对应的详细数据
                List<RoadCheckPCI> detailData = new ArrayList<>();
                if ("上行".equals(direction) && upData != null && !upData.isEmpty()) {
                    detailData.addAll(upData);
                } else if ("下行".equals(direction) && downData != null && !downData.isEmpty()) {
                    detailData.addAll(downData);
                }

                // 按起始桩号排序
                detailData.sort((a, b) -> a.getStartCode().compareTo(b.getStartCode()));

                // 填充详细数据到表格
                fillWordDetailTable(detailTable, detailData);

                log.info("成功填充{}详细数据表格，共 {} 行数据", direction, detailData.size());
            } else {
                // 如果没有找到第4个表格，创建一个新的详细数据表格
                log.warn("未找到详细数据表格，创建新的表格");
                List<RoadCheckPCI> currentDirectionData = new ArrayList<>();
                if ("上行".equals(direction) && upData != null) {
                    currentDirectionData.addAll(upData);
                } else if ("下行".equals(direction) && downData != null) {
                    currentDirectionData.addAll(downData);
                }
                createWordDetailTable(document, "上行".equals(direction) ? currentDirectionData : new ArrayList<>(),
                        "下行".equals(direction) ? currentDirectionData : new ArrayList<>());
            }

            // 填充检测人员信息到第二个表格（如果存在）
            if (document.getTables().size() >= 2) {
                checkTeamService.fillCheckTeamUserTable(document.getTables().get(1), teamId);
                log.info("成功填充检测人员信息表格，使用分组ID: {}", teamId);
            } else {
                log.warn("未找到检测人员信息表格（第二个表格）");
            }
            // 获取路线信息用于报告编号
            Road roadInfo = roadMapper.selectRoadById(upData != null && !upData.isEmpty() ? upData.get(0).getRoadId() :
                    (downData != null && !downData.isEmpty() ? downData.get(0).getRoadId() : null));

            // 使用公共工具类替换标准占位符（包括报告编号）
            WordDocumentUtils.replaceStandardPlaceholders(document, "PCI", titleName, checkName, reviewName, roadInfo);

            // 替换文档中的占位符
            WordDocumentUtils.replaceTextInDocument(document, "${roadName}", road.getRoadName());
            WordDocumentUtils.replaceTextInDocument(document, "${startCode}", road.getStartCode());
            WordDocumentUtils.replaceTextInDocument(document, "${endCode}", road.getEndCode());
            WordDocumentUtils.replaceTextInDocumentWithFontStyle(document, "${roadNames}", road.getRoadName(),"黑体",24);
            WordDocumentUtils.replaceTextInDocumentWithFontStyle(document, "${startCodes}", road.getStartCode(),"黑体",24);
            WordDocumentUtils.replaceTextInDocumentWithFontStyle(document, "${endCodes}", road.getEndCode(),"黑体",24);
            WordDocumentUtils.replaceTextInDocumentWithFontStyle(document, "${year}", String.valueOf(road.getYear()),"黑体",24);
            WordDocumentUtils.replaceTextInDocument(document, "${direction}", direction);
            WordDocumentUtils.replaceTextInDocument(document, "${companyName}", road.getCompanyName());
            WordDocumentUtils.replaceTextInDocument(document, "${projectName}", road.getProjectName());
            WordDocumentUtils.replaceTextInDocument(document, "${reportNo}", road.getReportNo());
            WordDocumentUtils.replaceTextInDocument(document, "${number}", "01");

            // 替换日期相关占位符
            if (dateTime != null && !dateTime.isEmpty()) {
                replaceTextInDocument(document, "${dateTime}", dateTime);
            }
            if (monthDate != null && !monthDate.isEmpty()) {
                replaceTextInDocument(document, "${monthDate}", monthDate);
            }

            // 根据dateTime参数生成dateTimeStr并替换
            if (dateTime != null && !dateTime.isEmpty()) {
                String dateTimeStr = dateTime.replace("年", ".").replace("月", ".").replace("日", "");
                replaceTextInDocument(document, "${dateTimeStr}", dateTimeStr);
            }

            // 计算文档页数并替换页数占位符
            int[] pageCalculations = calculatePCIDocumentPagesWithDetails(summaryMap.values(), upData, downData, direction);
            int totalPages = pageCalculations[0];
            int pageOne = pageCalculations[1];
            int pageTwo = pageCalculations[2];

            WordDocumentUtils.replaceTextInDocument(document, "${pages}", String.valueOf(totalPages));
            WordDocumentUtils.replaceTextInDocument(document, "${pageOne}", String.valueOf(pageOne));
            WordDocumentUtils.replaceTextInDocument(document, "${pageTwo}", String.valueOf(pageTwo));
            log.info("{}方向PCI文档共 {} 页, pageOne: {}, pageTwo: {}", direction, totalPages, pageOne, pageTwo);
            log.info("{}方向文档共 {} 页", direction, totalPages);

            return document;

        } catch (Exception e) {
            log.error("创建{}方向Word文档失败", direction, e);
            throw new RuntimeException("创建" + direction + "方向Word文档失败", e);
        } finally {
            // 确保流被关闭
            if (templateStream != null) {
                try {
                    templateStream.close();
                } catch (IOException e) {
                    log.warn("关闭模板流失败: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 在Word表格中添加等级统计行
     */
    private void addGradeRow(XWPFTable table, String gradeName,
                             double upDistance, double upPercentage,
                             double downDistance, double downPercentage) {
        XWPFTableRow row = table.createRow();

        // 确保行有足够的单元格
        while (row.getTableCells().size() < 10) {
            row.createCell();
        }

        // 安全地设置单元格内容
        safeSetCellText(row, 0, gradeName);
        safeSetCellText(row, 1, "");
        safeSetCellText(row, 2, "");
        safeSetCellText(row, 3, ""); // D列为空
        safeSetCellText(row, 4, String.valueOf((int)upDistance));
        safeSetCellText(row, 5, String.format("%.2f%%", upPercentage * 100));
        safeSetCellText(row, 6, "");
        safeSetCellText(row, 7, String.valueOf((int)downDistance));
        safeSetCellText(row, 8, String.format("%.2f%%", downPercentage * 100));
        safeSetCellText(row, 9, "");

        // 合并单元格
        try {
            int rowIndex = table.getRows().size() - 1;
            mergeCellsHorizontal(table, rowIndex, 0, 2); // 合并A,B,C列
            mergeCellsHorizontal(table, rowIndex, 5, 6); // 合并F,G列
            mergeCellsHorizontal(table, rowIndex, 8, 9); // 合并I,J列
        } catch (Exception e) {
            log.warn("合并单元格失败: {}", e.getMessage());
        }
    }

    /**
     * 安全地设置Word表格单元格文本
     */
    private void safeSetCellText(XWPFTableRow row, int cellIndex, String text) {
        try {
            if (row != null && cellIndex >= 0 && cellIndex < row.getTableCells().size()) {
                XWPFTableCell cell = row.getCell(cellIndex);
                if (cell != null) {
                    // 清除现有段落，创建新的居中段落
                    cell.removeParagraph(0);
                    XWPFParagraph paragraph = cell.addParagraph();
                    paragraph.setAlignment(ParagraphAlignment.CENTER);
                    
                    // 根据文本内容设置混合字体
                    setMixedFontForTableCell(paragraph, text != null ? text : "", 9);

                    // 设置单元格垂直居中对齐
                    cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
                }
            }
        } catch (Exception e) {
            log.warn("设置单元格文本失败 [行:{}, 列:{}]: {}",
                    row != null ? row.getTableCells().size() : "null", cellIndex, e.getMessage());
        }
    }

    /**
     * 为表格单元格设置混合字体（汉字使用宋体，数字和字母使用Times New Roman）
     */
    private void setMixedFontForTableCell(XWPFParagraph paragraph, String text, int fontSize) {
        if (text == null || text.isEmpty()) {
            return;
        }

        StringBuilder currentSegment = new StringBuilder();
        boolean isCurrentSegmentLatin = false;
        boolean isFirstChar = true;

        for (char c : text.toCharArray()) {
            boolean isCharLatin = isLatinDigitOrSymbol(c);

            if (isFirstChar) {
                // 第一个字符，确定初始状态
                currentSegment.append(c);
                isCurrentSegmentLatin = isCharLatin;
                isFirstChar = false;
            } else if (isCharLatin == isCurrentSegmentLatin) {
                // 字符类型相同，添加到当前段
                currentSegment.append(c);
            } else {
                // 字符类型改变，输出当前段并开始新段
                if (currentSegment.length() > 0) {
                    createRunWithFont(paragraph, currentSegment.toString(), isCurrentSegmentLatin, fontSize, null, null, null);
                }
                currentSegment = new StringBuilder();
                currentSegment.append(c);
                isCurrentSegmentLatin = isCharLatin;
            }
        }

        // 输出最后一段
        if (currentSegment.length() > 0) {
            createRunWithFont(paragraph, currentSegment.toString(), isCurrentSegmentLatin, fontSize, null, null, null);
        }
    }

    /**
     * 填充详细数据到Word表格
     */
    private void fillWordDetailTable(XWPFTable table, List<RoadCheckPCI> dataList) {
        try {
            for (RoadCheckPCI record : dataList) {
                XWPFTableRow dataRow = table.createRow();

                // 确保行有足够的单元格（现在需要16列，去掉路面类型）
                while (dataRow.getTableCells().size() < 16) {
                    dataRow.createCell();
                }

                // 填充数据行内容（参考图片中的表格结构）
                int colIndex = 0;

                // 第1列：起始桩号
                safeSetCellText(dataRow, colIndex++, StakeCodeUtil.formatStakeCode(record.getStartCode()));

                // 第2列：结束桩号
                safeSetCellText(dataRow, colIndex++, StakeCodeUtil.formatStakeCode(record.getEndCode()));

                // 第3-14列：各类破损面积数据
                safeSetCellText(dataRow, colIndex++, formatDecimalValue(record.getCrackArea())); // 龟裂
                safeSetCellText(dataRow, colIndex++, formatDecimalValue(record.getBlockCrackArea())); // 块状裂缝
                safeSetCellText(dataRow, colIndex++, formatDecimalValue(record.getLongitudinalCrackArea())); // 纵向裂缝
                safeSetCellText(dataRow, colIndex++, formatDecimalValue(record.getTransverseCrackArea())); // 横向裂缝
                safeSetCellText(dataRow, colIndex++, formatDecimalValue(record.getSinkArea())); // 沉陷
                safeSetCellText(dataRow, colIndex++, formatDecimalValue(record.getRutArea())); // 车辙
                safeSetCellText(dataRow, colIndex++, formatDecimalValue(record.getWaveBumpArea())); // 波浪拥包
                safeSetCellText(dataRow, colIndex++, formatDecimalValue(record.getPitArea())); // 坑槽
                safeSetCellText(dataRow, colIndex++, formatDecimalValue(record.getLooseArea())); // 松散
                safeSetCellText(dataRow, colIndex++, formatDecimalValue(record.getBleedingArea())); // 泛油
                safeSetCellText(dataRow, colIndex++, formatDecimalValue(record.getPatchAreaPart())); // 修补（部分）
                safeSetCellText(dataRow, colIndex++, formatDecimalValue(record.getPatchAreaStrip())); // 修补（条带）

                // 第15列：破损率DR(%)
                safeSetCellText(dataRow, colIndex++, formatDecimalValue(record.getDamageRate()));

                // 第16列：PCI（保留2位小数，四舍五入）
                safeSetCellText(dataRow, colIndex, formatPCIValue(record.getPci()));

                // 为数据行设置实线边框
                setWordTableCellBorders(dataRow);
            }
        } catch (Exception e) {
            log.error("填充Word详细数据表格失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 创建新的详细数据表格
     */
    private void createWordDetailTable(XWPFDocument document, List<RoadCheckPCI> upData, List<RoadCheckPCI> downData) {
        try {
            // 添加分页符
            XWPFParagraph pageBreak = document.createParagraph();
            XWPFRun run = pageBreak.createRun();
            run.addBreak(BreakType.PAGE);

            // 创建表格标题
            XWPFParagraph titleParagraph = document.createParagraph();
            titleParagraph.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun titleRun = titleParagraph.createRun();
            titleRun.setText("路面破损状况详细检测数据表");
            titleRun.setBold(true);
            titleRun.setFontSize(14);

            // 创建表格
            XWPFTable detailTable = document.createTable();

            // 创建表头（3行）
            createWordDetailTableHeaders(detailTable);

            // 合并并排序数据
            List<RoadCheckPCI> allDetailData = new ArrayList<>();
            if (upData != null && !upData.isEmpty()) {
                allDetailData.addAll(upData);
            }
            if (downData != null && !downData.isEmpty()) {
                allDetailData.addAll(downData);
            }

            // 按起始桩号和方向排序
            allDetailData.sort((a, b) -> {
                int stakeCompare = a.getStartCode().compareTo(b.getStartCode());
                if (stakeCompare != 0) {
                    return stakeCompare;
                }
                return a.getDirection().compareTo(b.getDirection());
            });

            // 填充数据
            fillWordDetailTable(detailTable, allDetailData);

        } catch (Exception e) {
            log.error("创建Word详细数据表格失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 创建详细数据表格的表头
     */
    private void createWordDetailTableHeaders(XWPFTable table) {
        // 第一行表头：主标题
        XWPFTableRow mainHeaderRow = table.getRow(0);
        while (mainHeaderRow.getTableCells().size() < 16) {
            mainHeaderRow.addNewTableCell();
        }

        String[] mainHeaders = {
                "桩号", "", "龟裂", "块状", "纵向", "横向", "沉陷", "车辙",
                "波浪", "坑槽", "松散", "泛油", "修补", "", "破损率", "PCI"
        };

        for (int i = 0; i < mainHeaders.length && i < 16; i++) {
            safeSetCellText(mainHeaderRow, i, mainHeaders[i]);
        }

        // 第二行表头：子标题
        XWPFTableRow subHeaderRow = table.createRow();
        while (subHeaderRow.getTableCells().size() < 16) {
            subHeaderRow.addNewTableCell();
        }

        String[] subHeaders = {
                "", "", "裂缝", "裂缝", "裂缝", "裂缝", "", "",
                "拥包", "", "", "", "面积", "面积", "DR", ""
        };

        for (int i = 0; i < subHeaders.length && i < 16; i++) {
            safeSetCellText(subHeaderRow, i, subHeaders[i]);
        }

        // 第三行表头：单位和具体字段名
        XWPFTableRow unitHeaderRow = table.createRow();
        while (unitHeaderRow.getTableCells().size() < 16) {
            unitHeaderRow.addNewTableCell();
        }

        String[] unitHeaders = {
                "起始桩号", "结束桩号", "(m²)", "(m²)", "(m²)", "(m²)", "(m²)", "(m²)",
                "(m²)", "(m²)", "(m²)", "(m²)", "(部分)(m²)", "(条带)(m²)", "(%)", ""
        };

        for (int i = 0; i < unitHeaders.length && i < 16; i++) {
            safeSetCellText(unitHeaderRow, i, unitHeaders[i]);
        }

        // 为表头行设置边框
        setWordTableCellBorders(mainHeaderRow);
        setWordTableCellBorders(subHeaderRow);
        setWordTableCellBorders(unitHeaderRow);
    }

    /**
     * 格式化BigDecimal值为字符串
     */
    private String formatDecimalValue(BigDecimal value) {
        if (value == null) {
            return "0.000";
        }
        return String.format("%.3f", value.doubleValue());
    }

    /**
     * 格式化PCI值为字符串（保留2位小数，四舍五入）
     */
    private String formatPCIValue(BigDecimal value) {
        if (value == null) {
            return "0.00";
        }
        return String.format("%.2f", value.doubleValue());
    }

    /**
     * 为Word表格行的所有单元格设置实线边框
     */
    private void setWordTableCellBorders(XWPFTableRow row) {
        try {
            if (row != null) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    if (cell != null && cell.getCTTc() != null) {
                        // 获取或创建单元格属性
                        if (cell.getCTTc().getTcPr() == null) {
                            cell.getCTTc().addNewTcPr();
                        }

                        // 获取或创建边框属性
                        if (cell.getCTTc().getTcPr().getTcBorders() == null) {
                            cell.getCTTc().getTcPr().addNewTcBorders();
                        }

                        // 设置四个方向的实线边框
                        org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder border =
                                org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder.Factory.newInstance();
                        border.setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STBorder.SINGLE);
                        border.setSz(java.math.BigInteger.valueOf(4)); // 边框粗细
                        border.setColor("000000"); // 黑色边框

                        // 设置上边框
                        if (cell.getCTTc().getTcPr().getTcBorders().getTop() == null) {
                            cell.getCTTc().getTcPr().getTcBorders().addNewTop();
                        }
                        cell.getCTTc().getTcPr().getTcBorders().getTop().setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STBorder.SINGLE);
                        cell.getCTTc().getTcPr().getTcBorders().getTop().setSz(java.math.BigInteger.valueOf(4));
                        cell.getCTTc().getTcPr().getTcBorders().getTop().setColor("000000");

                        // 设置右边框
                        if (cell.getCTTc().getTcPr().getTcBorders().getRight() == null) {
                            cell.getCTTc().getTcPr().getTcBorders().addNewRight();
                        }
                        cell.getCTTc().getTcPr().getTcBorders().getRight().setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STBorder.SINGLE);
                        cell.getCTTc().getTcPr().getTcBorders().getRight().setSz(java.math.BigInteger.valueOf(4));
                        cell.getCTTc().getTcPr().getTcBorders().getRight().setColor("000000");

                        // 设置下边框
                        if (cell.getCTTc().getTcPr().getTcBorders().getBottom() == null) {
                            cell.getCTTc().getTcPr().getTcBorders().addNewBottom();
                        }
                        cell.getCTTc().getTcPr().getTcBorders().getBottom().setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STBorder.SINGLE);
                        cell.getCTTc().getTcPr().getTcBorders().getBottom().setSz(java.math.BigInteger.valueOf(4));
                        cell.getCTTc().getTcPr().getTcBorders().getBottom().setColor("000000");

                        // 设置左边框
                        if (cell.getCTTc().getTcPr().getTcBorders().getLeft() == null) {
                            cell.getCTTc().getTcPr().getTcBorders().addNewLeft();
                        }
                        cell.getCTTc().getTcPr().getTcBorders().getLeft().setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STBorder.SINGLE);
                        cell.getCTTc().getTcPr().getTcBorders().getLeft().setSz(java.math.BigInteger.valueOf(4));
                        cell.getCTTc().getTcPr().getTcBorders().getLeft().setColor("000000");
                    }
                }
            }
        } catch (Exception e) {
            log.warn("设置Word表格单元格边框失败: {}", e.getMessage());
        }
    }

    /**
     * 在Word表格中添加等级统计行（简化版本，适应10列表格）
     */
    private void addWordGradeRowSimple(XWPFTable table, String gradeName,
                                       double upDistance, double upPercentage,
                                       double downDistance, double downPercentage) {
        XWPFTableRow row = table.createRow();

        // 确保行有足够的单元格
        while (row.getTableCells().size() < 10) {
            row.createCell();
        }

        // 根据新的表格模板设置等级统计行内容
        safeSetCellText(row, 0, gradeName); // 等级名称
        safeSetCellText(row, 1, ""); // 分隔符列为空
        safeSetCellText(row, 2, ""); // 结束桩号列为空
        safeSetCellText(row, 3, ""); // 段落长度列为空
        safeSetCellText(row, 4, String.valueOf((int)upDistance)); // 上行该等级的公里数
        safeSetCellText(row, 5, String.format("%.2f%%", upPercentage * 100)); // 上行该等级占比
        safeSetCellText(row, 6, ""); // 上行等级列为空
        safeSetCellText(row, 7, String.valueOf((int)downDistance)); // 下行该等级的公里数
        safeSetCellText(row, 8, String.format("%.2f%%", downPercentage * 100)); // 下行该等级占比
        safeSetCellText(row, 9, ""); // 下行等级列为空

        // 为等级统计行设置实线边框
        setWordTableCellBorders(row);

        // 获取当前行的索引并进行单元格合并
        int rowIndex = table.getRows().size() - 1;
        // 合并前三列（第0,1,2列）
        mergeCellsHorizontal(table, rowIndex, 0, 2);
        // 合并第5,6列（索引为5,6）
        mergeCellsHorizontal(table, rowIndex, 5, 6);
        // 合并第8,9列（索引为8,9）
        mergeCellsHorizontal(table, rowIndex, 8, 9);
    }

    /**
     * 在Word表格中横向合并单元格
     */
    private void mergeCellsHorizontal(XWPFTable table, int row, int fromCell, int toCell) {
        try {
            if (table == null || row < 0 || fromCell < 0 || toCell < fromCell) {
                return;
            }

            XWPFTableRow tableRow = table.getRow(row);
            if (tableRow == null) {
                return;
            }

            for (int cellIndex = fromCell; cellIndex <= toCell; cellIndex++) {
                if (cellIndex >= tableRow.getTableCells().size()) {
                    break; // 超出单元格范围，停止合并
                }

                XWPFTableCell cell = tableRow.getCell(cellIndex);
                if (cell != null) {
                    if (cellIndex == fromCell) {
                        // 第一个单元格设置合并属性
                        cell.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.RESTART);
                    } else {
                        // 其他单元格设置为合并
                        cell.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("合并单元格异常 [表格行:{}, 起始列:{}, 结束列:{}]: {}",
                    row, fromCell, toCell, e.getMessage());
        }
    }

    /**
     * 替换Word文档中的文本
     */
    private void replaceTextInDocument(XWPFDocument document, String placeholder, String value) {
        // 替换段落中的文本
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            replaceParagraphText(paragraph, placeholder, value);
        }

        // 替换表格中的文本
        for (XWPFTable table : document.getTables()) {
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    for (XWPFParagraph paragraph : cell.getParagraphs()) {
                        replaceParagraphText(paragraph, placeholder, value);
                    }
                }
            }
        }

        // 替换页眉中的文本
        for (XWPFHeader header : document.getHeaderList()) {
            for (XWPFParagraph paragraph : header.getParagraphs()) {
                replaceParagraphText(paragraph, placeholder, value);
            }

            for (XWPFTable table : header.getTables()) {
                for (XWPFTableRow row : table.getRows()) {
                    for (XWPFTableCell cell : row.getTableCells()) {
                        for (XWPFParagraph paragraph : cell.getParagraphs()) {
                            replaceParagraphText(paragraph, placeholder, value);
                        }
                    }
                }
            }
        }

        // 替换页脚中的文本
        for (XWPFFooter footer : document.getFooterList()) {
            for (XWPFParagraph paragraph : footer.getParagraphs()) {
                replaceParagraphText(paragraph, placeholder, value);
            }

            for (XWPFTable table : footer.getTables()) {
                for (XWPFTableRow row : table.getRows()) {
                    for (XWPFTableCell cell : row.getTableCells()) {
                        for (XWPFParagraph paragraph : cell.getParagraphs()) {
                            replaceParagraphText(paragraph, placeholder, value);
                        }
                    }
                }
            }
        }
    }

    /**
     * 替换段落中的文本
     */
    private void replaceParagraphText(XWPFParagraph paragraph, String placeholder, String value) {
        if (value == null) {
            value = "";
        }

        String text = paragraph.getText();
        if (text != null && text.contains(placeholder)) {
            List<XWPFRun> runs = paragraph.getRuns();
            if (runs != null) {
                for (XWPFRun run : runs) {
                    String runText = run.getText(0);
                    if (runText != null && runText.contains(placeholder)) {
                        run.setText(runText.replace(placeholder, value), 0);
                    }
                }
            }
        }
    }

    /**
     * 格式化桩号为标准K格式（例如：2320000 -> K2320+000）
     * 支持多种输入格式的自动识别和转换
     *
     * 支持的格式示例：
     * - 纯数字：2320000 -> K2320+000, 2320260 -> K2320+260
     * - K+数字：K2320000 -> K2320+000, K2320260 -> K2320+260


     /**
     * 复制汇总Sheet的结构到新Sheet（前4行表头）
     */
    private void copySummarySheetStructure(Sheet templateSheet, Sheet newSheet, SXSSFWorkbook workbook) {
        // 复制列宽
        for (int i = 0; i < 10; i++) { // 汇总表有10列
            newSheet.setColumnWidth(i, templateSheet.getColumnWidth(i));
        }

        // 复制表头行（前4行是表头）
        for (int rowIndex = 0; rowIndex < 4; rowIndex++) {
            Row templateRow = templateSheet.getRow(rowIndex);
            if (templateRow != null) {
                Row newRow = newSheet.createRow(rowIndex);
                newRow.setHeight(templateRow.getHeight());

                // 复制每个单元格
                for (int colIndex = 0; colIndex < templateRow.getLastCellNum(); colIndex++) {
                    Cell templateCell = templateRow.getCell(colIndex);
                    if (templateCell != null) {
                        Cell newCell = newRow.createCell(colIndex);

                        // 复制单元格值
                        switch (templateCell.getCellType()) {
                            case STRING:
                                newCell.setCellValue(templateCell.getStringCellValue());
                                break;
                            case NUMERIC:
                                newCell.setCellValue(templateCell.getNumericCellValue());
                                break;
                            case BOOLEAN:
                                newCell.setCellValue(templateCell.getBooleanCellValue());
                                break;
                            default:
                                break;
                        }

                        // 复制样式（表头使用中文字体）
                        CellStyle newStyle = createCellStyleWithFont(workbook, true);
                        CellStyle templateStyle = templateCell.getCellStyle();

                        // 复制样式属性
                        newStyle.setAlignment(templateStyle.getAlignment());
                        newStyle.setVerticalAlignment(templateStyle.getVerticalAlignment());
                        newStyle.setBorderTop(templateStyle.getBorderTop());
                        newStyle.setBorderRight(templateStyle.getBorderRight());
                        newStyle.setBorderBottom(templateStyle.getBorderBottom());
                        newStyle.setBorderLeft(templateStyle.getBorderLeft());
                        newStyle.setFillForegroundColor(templateStyle.getFillForegroundColor());
                        newStyle.setFillPattern(templateStyle.getFillPattern());
                        newStyle.setWrapText(templateStyle.getWrapText());

                        // 复制字体属性（保持粗体等，使用宋体）
                        if (templateStyle.getFontIndex() > 0) {
                            Font templateFont = templateSheet.getWorkbook().getFontAt(templateStyle.getFontIndex());
                            Font newFont = workbook.createFont();
                            newFont.setBold(templateFont.getBold());
                            newFont.setFontHeightInPoints(templateFont.getFontHeightInPoints());
                            // 表头使用宋体
                            newFont.setFontName("宋体");
                            newStyle.setFont(newFont);
                        }

                        newCell.setCellStyle(newStyle);
                    }
                }
            }
        }

        // 复制合并单元格区域
        for (int i = 0; i < templateSheet.getNumMergedRegions(); i++) {
            CellRangeAddress mergedRegion = templateSheet.getMergedRegion(i);
            // 只复制表头部分的合并区域（前4行）
            if (mergedRegion.getFirstRow() < 4) {
                newSheet.addMergedRegion(new CellRangeAddress(
                        mergedRegion.getFirstRow(),
                        mergedRegion.getLastRow(),
                        mergedRegion.getFirstColumn(),
                        mergedRegion.getLastColumn()
                ));
            }
        }

        // 修正表头文本：将"m2"替换为正确的平方米符号"m²"
        fixHeaderText(newSheet);
    }

    /**
     * 填充汇总数据到Sheet
     */
    private void fillSummaryData(Sheet sheet, List<RoadCheckPCI> upData, List<RoadCheckPCI> downData) {
        // 获取路线信息和桩号范围
        String roadName = "";
        String minStakeCode = "";
        String maxStakeCode = "";

        // 合并上行和下行数据来获取完整的桩号范围
        List<RoadCheckPCI> allData = new ArrayList<>();
        if (upData != null) allData.addAll(upData);
        if (downData != null) allData.addAll(downData);

        if (!allData.isEmpty()) {
            // 获取路线名称（假设所有数据的路线名称相同）
            roadName = allData.get(0).getRoadName();
            if (roadName == null || roadName.trim().isEmpty()) {
                roadName = "高速公路"; // 默认名称
            }

            // 获取桩号范围
            List<String> stakeCodes = allData.stream()
                    .map(RoadCheckPCI::getStartCode)
                    .filter(code -> code != null && !code.trim().isEmpty())
                    .sorted()
                    .collect(Collectors.toList());

            if (!stakeCodes.isEmpty()) {
                minStakeCode = stakeCodes.get(0);
                maxStakeCode = stakeCodes.get(stakeCodes.size() - 1);

                // 如果最大桩号是起始桩号，尝试获取对应的结束桩号
                Optional<String> maxEndCode = allData.stream()
                        .map(RoadCheckPCI::getEndCode)
                        .filter(code -> code != null && !code.trim().isEmpty())
                        .max(String::compareTo);

                if (maxEndCode.isPresent()) {
                    maxStakeCode = maxEndCode.get();
                }
            }
        }

        // 更新第一行标题
        Row titleRow = sheet.getRow(0);
        if (titleRow != null) {
            Cell titleCell = titleRow.getCell(0);
            if (titleCell != null) {
                String newTitle = roadName + "路面破损率及路面损坏状况指数汇总表";
                titleCell.setCellValue(newTitle);
            }
        }

        // 更新第二行桩号范围
        Row stakeRangeRow = sheet.getRow(1);
        if (stakeRangeRow != null) {
            // 查找包含"桩号"的单元格
            for (int i = 0; i < stakeRangeRow.getLastCellNum(); i++) {
                Cell cell = stakeRangeRow.getCell(i);
                if (cell != null && cell.getCellType() == CellType.STRING) {
                    String cellValue = cell.getStringCellValue();
                    if (cellValue.contains("桩号")) {
                        String newStakeRange = "桩号：" + minStakeCode + "~" + maxStakeCode;
                        cell.setCellValue(newStakeRange);
                        break;
                    }
                }
            }
        }

        // 创建数据样式（中文字体用于文本列）
        CellStyle dataStyle = createCellStyleWithFont((SXSSFWorkbook) sheet.getWorkbook(), true);

        // 数值格式，保留3位小数 - 数字字母字体
        CellStyle numberStyle = createCellStyleWithFont((SXSSFWorkbook) sheet.getWorkbook(), false, "0.000");

        // 破损率数值格式，保留4位小数（用于E列和H列）- 数字字母字体
        CellStyle damageRateNumberStyle = createCellStyleWithFont((SXSSFWorkbook) sheet.getWorkbook(), false, "0.0000");

        // PCI数值格式，保留2位小数（用于F列和I列）- 数字字母字体
        CellStyle pciNumberStyle = createCellStyleWithFont((SXSSFWorkbook) sheet.getWorkbook(), false, "0.00");

        // 按照thousand_section（公里段）分组汇总数据
        Map<String, SummaryData> summaryMap = new LinkedHashMap<>();

        // 分别处理上行和下行数据，确保两者都被正确添加到summaryMap中
        // 先处理上行数据
        if (upData != null && !upData.isEmpty()) {
            for (RoadCheckPCI record : upData) {
                String thousandSection = record.getThousandSection();
                if (thousandSection != null && !thousandSection.trim().isEmpty()) {
                    SummaryData summary = summaryMap.computeIfAbsent(thousandSection, SummaryData::new);
                    summary.addUpDamageRate(record.getDamageRate());
                    log.debug("处理上行桩号段: {}, 起始: {}, 结束: {}, 破损率: {}",
                            thousandSection, record.getStartCode(), record.getEndCode(), record.getDamageRate());
                }
            }
        }

        // 再处理下行数据
        if (downData != null && !downData.isEmpty()) {
            for (RoadCheckPCI record : downData) {
                String thousandSection = record.getThousandSection();
                if (thousandSection != null && !thousandSection.trim().isEmpty()) {
                    SummaryData summary = summaryMap.computeIfAbsent(thousandSection, SummaryData::new);
                    summary.addDownDamageRate(record.getDamageRate());
                    log.debug("处理下行桩号段: {}, 起始: {}, 结束: {}, 破损率: {}",
                            thousandSection, record.getStartCode(), record.getEndCode(), record.getDamageRate());
                }
            }
        }

        int rowIndex = 4; // 从第5行开始填充数据（前4行是表头）

        // 用于计算合计数据的变量
        double totalDistanceSum = 0.0;
        double upDamageRateSum = 0.0;
        double upPCISum = 0.0;
        double downDamageRateSum = 0.0;
        double downPCISum = 0.0;
        int dataRowCount = 0;

        // 用于统计各等级的公里数
        double upExcellentDistance = 0.0;
        double upGoodDistance = 0.0;
        double upMediumDistance = 0.0;
        double upFairDistance = 0.0;
        double upPoorDistance = 0.0;
        double downExcellentDistance = 0.0;
        double downGoodDistance = 0.0;
        double downMediumDistance = 0.0;
        double downFairDistance = 0.0;
        double downPoorDistance = 0.0;

        // 记录处理的数据情况
        log.info("汇总表数据处理：上行数据 {} 条，下行数据 {} 条，合并后桩号段 {} 个",
                upData != null ? upData.size() : 0,
                downData != null ? downData.size() : 0,
                summaryMap.size());

        for (SummaryData summary : summaryMap.values()) {
            Row row = sheet.createRow(rowIndex++);
            int colIndex = 0;

            // A列：startCode（公里段起始桩号，如K2320+000）
            Cell cellA = row.createCell(colIndex++);
            cellA.setCellValue(summary.getStartCode());
            cellA.setCellStyle(dataStyle);

            // B列："~"
            Cell cellB = row.createCell(colIndex++);
            cellB.setCellValue("~");
            cellB.setCellStyle(dataStyle);

            // C列：endCode（公里段结束桩号，如K2321+000）
            Cell cellC = row.createCell(colIndex++);
            cellC.setCellValue(summary.getEndCode());
            cellC.setCellStyle(dataStyle);

            // D列：实际计算的段落长度
            Cell cellD = row.createCell(colIndex++);
            double actualDistance = summary.calculateActualDistance();
            cellD.setCellValue(String.valueOf((int)actualDistance));
            cellD.setCellStyle(dataStyle);

            // 记录实际距离计算结果（用于调试）
            log.debug("汇总数据段位: {}, 起始桩号: {}, 结束桩号: {}, 实际距离: {}米",
                    summary.getThousandSection(), summary.getStartCode(), summary.getEndCode(), (int)actualDistance);

            // E列：上行damage_rate平均值
            Cell cellE = row.createCell(colIndex++);
            BigDecimal upAvgDamageRate = summary.getUpAvgDamageRate();
            double upDamageRateValue = 0.0;
            if (upAvgDamageRate != null) {
                upDamageRateValue = upAvgDamageRate.doubleValue();
                cellE.setCellValue(upDamageRateValue);
                log.debug("上行平均破损率: {}", upDamageRateValue);
            } else {
                cellE.setCellValue(0.0);
                log.debug("上行平均破损率为空，设置为0.0");
            }
            cellE.setCellStyle(damageRateNumberStyle);

            // F列：100-15*(E列的值)^0.412
            Cell cellF = row.createCell(colIndex++);
            double upPCI = calculatePCI(upAvgDamageRate);
            cellF.setCellValue(upPCI);
            cellF.setCellStyle(pciNumberStyle);

            // G列：等级评定
            Cell cellG = row.createCell(colIndex++);
            String upGrade = getGrade(upPCI);
            cellG.setCellValue(upGrade);
            cellG.setCellStyle(dataStyle);

            // 根据上行等级统计公里数
            double sectionDistance = actualDistance; // 使用实际计算的距离
            switch(upGrade) {
                case "优":
                    upExcellentDistance += sectionDistance;
                    break;
                case "良":
                    upGoodDistance += sectionDistance;
                    break;
                case "中":
                    upMediumDistance += sectionDistance;
                    break;
                case "次":
                    upFairDistance += sectionDistance;
                    break;
                case "差":
                    upPoorDistance += sectionDistance;
                    break;
            }

            // H列：下行damage_rate平均值
            Cell cellH = row.createCell(colIndex++);
            BigDecimal downAvgDamageRate = summary.getDownAvgDamageRate();
            double downDamageRateValue = 0.0;
            if (downAvgDamageRate != null) {
                downDamageRateValue = downAvgDamageRate.doubleValue();
                cellH.setCellValue(downDamageRateValue);
            } else {
                cellH.setCellValue(0.0);
            }
            cellH.setCellStyle(damageRateNumberStyle);

            // I列：100-15*(H列的值)^0.412
            Cell cellI = row.createCell(colIndex++);
            double downPCI = calculatePCI(downAvgDamageRate);
            cellI.setCellValue(downPCI);
            cellI.setCellStyle(pciNumberStyle);

            // J列：等级评定
            Cell cellJ = row.createCell(colIndex);
            String downGrade = getGrade(downPCI);
            cellJ.setCellValue(downGrade);
            cellJ.setCellStyle(dataStyle);

            // 根据下行等级统计公里数
            switch(downGrade) {
                case "优":
                    downExcellentDistance += sectionDistance;
                    break;
                case "良":
                    downGoodDistance += sectionDistance;
                    break;
                case "中":
                    downMediumDistance += sectionDistance;
                    break;
                case "次":
                    downFairDistance += sectionDistance;
                    break;
                case "差":
                    downPoorDistance += sectionDistance;
                    break;
            }

            // 累计合计数据
            totalDistanceSum += actualDistance; // 使用实际计算的距离
            upDamageRateSum += upDamageRateValue;
            upPCISum += upPCI;
            downDamageRateSum += downDamageRateValue;
            downPCISum += downPCI;
            dataRowCount++;
        }

        // 添加合计行
        if (dataRowCount > 0) {
            Row summaryRow = sheet.createRow(rowIndex);

            // A,B,C列合并显示"合计"
            Cell summaryCell = summaryRow.createCell(0);
            summaryCell.setCellValue("合计");
            summaryCell.setCellStyle(dataStyle);

            // 合并A,B,C列
            sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, 2));

            // 创建空的B,C列单元格以保持格式一致
            Cell cellB = summaryRow.createCell(1);
            cellB.setCellStyle(dataStyle);
            Cell cellC = summaryRow.createCell(2);
            cellC.setCellStyle(dataStyle);

            // D列：所有D列的求和
            Cell cellD = summaryRow.createCell(3);
            cellD.setCellValue(totalDistanceSum);
            cellD.setCellStyle(dataStyle);

            // 记录合计距离信息
            log.info("PCI汇总表生成完成 - 总计实际距离: {}米 (约{:.2f}公里), 数据行数: {}",
                    (int)totalDistanceSum, totalDistanceSum/1000.0, dataRowCount);

            // E列：所有E列的平均值
            Cell cellE = summaryRow.createCell(4);
            double avgUpDamageRate = upDamageRateSum / dataRowCount;
            cellE.setCellValue(avgUpDamageRate);
            cellE.setCellStyle(damageRateNumberStyle);

            // F列：根据E列的DR值计算PCI = 100-15*(E列的DR值)^0.412
            Cell cellF = summaryRow.createCell(5);
            double avgUpPCI = 100.0 - 15.0 * Math.pow(avgUpDamageRate, 0.412);
            cellF.setCellValue(avgUpPCI);
            cellF.setCellStyle(pciNumberStyle);

            // G列：根据F列的计算值评定等级
            Cell cellG = summaryRow.createCell(6);
            String upTotalGrade = getGrade(avgUpPCI);
            cellG.setCellValue(upTotalGrade);
            cellG.setCellStyle(dataStyle);

            // H列：所有H列的平均值
            Cell cellH = summaryRow.createCell(7);
            double avgDownDamageRate = downDamageRateSum / dataRowCount;
            cellH.setCellValue(avgDownDamageRate);
            cellH.setCellStyle(damageRateNumberStyle);

            // I列：根据H列的DR值计算PCI = 100-15*(H列的DR值)^0.412
            Cell cellI = summaryRow.createCell(8);
            double avgDownPCI = 100.0 - 15.0 * Math.pow(avgDownDamageRate, 0.412);
            cellI.setCellValue(avgDownPCI);
            cellI.setCellStyle(pciNumberStyle);

            // J列：根据I列的计算值评定等级
            Cell cellJ = summaryRow.createCell(9);
            String downTotalGrade = getGrade(avgDownPCI);
            cellJ.setCellValue(downTotalGrade);
            cellJ.setCellStyle(dataStyle);

            // 添加等级统计行
            addGradeStatisticsRows(sheet, rowIndex + 1, dataStyle, numberStyle,
                    totalDistanceSum, upExcellentDistance, upGoodDistance, upMediumDistance,
                    upFairDistance, upPoorDistance, downExcellentDistance, downGoodDistance,
                    downMediumDistance, downFairDistance, downPoorDistance);
        }
    }

    /**
     * 添加等级统计行
     */
    private void addGradeStatisticsRows(Sheet sheet, int startRow, CellStyle dataStyle, CellStyle numberStyle,
                                        double totalDistance, double upExcellent, double upGood,
                                        double upMedium, double upFair, double upPoor,
                                        double downExcellent, double downGood, double downMedium,
                                        double downFair, double downPoor) {
        // 创建百分比格式 - 数字字母字体
        CellStyle percentStyle = createCellStyleWithFont((SXSSFWorkbook) sheet.getWorkbook(), false, "0.00%");

        // 添加"优"等级统计行
        addGradeRow(sheet, startRow, "优 (%)", dataStyle, numberStyle, percentStyle,
                totalDistance, upExcellent, downExcellent);

        // 添加"良"等级统计行
        addGradeRow(sheet, startRow + 1, "良 (%)", dataStyle, numberStyle, percentStyle,
                totalDistance, upGood, downGood);

        // 添加"中"等级统计行
        addGradeRow(sheet, startRow + 2, "中 (%)", dataStyle, numberStyle, percentStyle,
                totalDistance, upMedium, downMedium);

        // 添加"次"等级统计行
        addGradeRow(sheet, startRow + 3, "次 (%)", dataStyle, numberStyle, percentStyle,
                totalDistance, upFair, downFair);

        // 添加"差"等级统计行
        addGradeRow(sheet, startRow + 4, "差 (%)", dataStyle, numberStyle, percentStyle,
                totalDistance, upPoor, downPoor);
    }

    /**
     * 计算PCI值：100-15*(damageRate)^0.412
     */
    private double calculatePCI(BigDecimal damageRate) {
        if (damageRate == null || damageRate.compareTo(BigDecimal.ZERO) == 0) {
            return 100.0;
        }
        double dr = damageRate.doubleValue();
        return 100.0 - 15.0 * Math.pow(dr, 0.412);
    }

    /**
     * 根据PCI值获取等级
     */
    private String getGrade(double pci) {
        if (pci >= 92) {
            return "优";
        } else if (pci >= 80) {
            return "良";
        } else if (pci >= 70) {
            return "中";
        } else if (pci >= 60) {
            return "次";
        } else {
            return "差";
        }
    }

    /**
     * 汇总数据内部类
     */
    private static class SummaryData {
        private String startCode;
        private String endCode;
        private String thousandSection;
        private List<BigDecimal> upDamageRates = new ArrayList<>();
        private List<BigDecimal> downDamageRates = new ArrayList<>();

        // 原有构造函数（按起始和结束桩号）
        public SummaryData(String startCode, String endCode) {
            this.startCode = startCode;
            this.endCode = endCode;
        }

        // 新增构造函数（按公里段）
        public SummaryData(String thousandSection) {
            this.thousandSection = thousandSection;
            // 根据公里段生成起始和结束桩号
            // 例如：K2320+000~K2321+000 -> K2320+000 ~ K2321+000
            if (thousandSection != null && thousandSection.contains("~")) {
                // 如果thousandSection已经是范围格式，直接解析
                String[] parts = thousandSection.split("~");
                if (parts.length == 2) {
                    this.startCode = parts[0].trim();
                    this.endCode = parts[1].trim();
                } else {
                    // 如果解析失败，使用默认格式
                    this.startCode = thousandSection;
                    this.endCode = thousandSection;
                }
            } else if (thousandSection != null && thousandSection.startsWith("K")) {
                // 如果是单个桩号格式，如K2320+000，生成对应的公里段
                try {
                    // 提取公里数
                    String cleanCode = thousandSection.replace("K", "").replace("+000", "");
                    int km = Integer.parseInt(cleanCode);
                    this.startCode = String.format("K%d+000", km);
                    this.endCode = String.format("K%d+000", km + 1);
                } catch (NumberFormatException e) {
                    // 如果解析失败，使用原始值
                    this.startCode = thousandSection;
                    this.endCode = thousandSection;
                }
            } else {
                this.startCode = thousandSection;
                this.endCode = thousandSection;
            }
        }

        public void addUpDamageRate(BigDecimal damageRate) {
            if (damageRate != null) {
                upDamageRates.add(damageRate);
            }
        }

        public void addDownDamageRate(BigDecimal damageRate) {
            if (damageRate != null) {
                downDamageRates.add(damageRate);
            }
        }

        public BigDecimal getUpAvgDamageRate() {
            if (upDamageRates.isEmpty()) {
                return null;
            }
            BigDecimal sum = upDamageRates.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            return sum.divide(BigDecimal.valueOf(upDamageRates.size()), 4, BigDecimal.ROUND_HALF_UP);
        }

        public BigDecimal getDownAvgDamageRate() {
            if (downDamageRates.isEmpty()) {
                return null;
            }
            BigDecimal sum = downDamageRates.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            return sum.divide(BigDecimal.valueOf(downDamageRates.size()), 4, BigDecimal.ROUND_HALF_UP);
        }

        public String getStartCode() {
            return startCode;
        }

        public String getEndCode() {
            return endCode;
        }

        public String getThousandSection() {
            return thousandSection;
        }

        /**
         * 计算实际距离（米）
         * 根据起始桩号和结束桩号计算实际距离
         * @return 实际距离（米）
         */
        public double calculateActualDistance() {
            if (startCode == null || endCode == null) {
                return 1000.0; // 默认值
            }

            try {
                long startMeters = parseStakeCodeToMeters(startCode);
                long endMeters = parseStakeCodeToMeters(endCode);

                if (startMeters >= 0 && endMeters >= 0 && endMeters > startMeters) {
                    return endMeters - startMeters;
                }
            } catch (Exception e) {
                log.warn("计算实际距离失败，起始桩号: {}, 结束桩号: {}, 错误: {}", startCode, endCode, e.getMessage());
            }

            return 1000.0; // 默认值
        }

        /**
         * 将桩号转换为总米数（从StakeCodeUtil复制的私有方法）
         */
        private long parseStakeCodeToMeters(String stakeCode) {
            if (stakeCode == null || stakeCode.trim().isEmpty()) {
                return -1;
            }

            stakeCode = stakeCode.trim().toUpperCase();

            try {
                // 处理K2320+260格式
                if (stakeCode.startsWith("K") && stakeCode.contains("+")) {
                    String cleanCode = stakeCode.substring(1); // 去掉K
                    String[] parts = cleanCode.split("\\+");
                    if (parts.length == 2) {
                        long km = Long.parseLong(parts[0]);
                        long m = Long.parseLong(parts[1]);
                        return km * 1000 + m;
                    }
                }

                // 处理纯数字格式（如：2320260）
                if (stakeCode.matches("\\d+")) {
                    return Long.parseLong(stakeCode);
                }

                // 处理K开头但没有+号的格式（如：K2320260）
                if (stakeCode.startsWith("K") && stakeCode.matches("K\\d+")) {
                    String numberPart = stakeCode.substring(1);
                    return Long.parseLong(numberPart);
                }

                return -1;
            } catch (NumberFormatException e) {
                return -1;
            }
        }
    }

    /**
     * 复制模板Sheet的结构到新Sheet
     */
    private void copySheetStructure(Sheet templateSheet, Sheet newSheet, SXSSFWorkbook workbook) {
        // 复制列宽
        for (int i = 0; i < 17; i++) {
            newSheet.setColumnWidth(i, templateSheet.getColumnWidth(i));
        }

        // 复制表头行（前4行是表头）
        for (int rowIndex = 0; rowIndex < 4; rowIndex++) {
            Row templateRow = templateSheet.getRow(rowIndex);
            if (templateRow != null) {
                Row newRow = newSheet.createRow(rowIndex);
                newRow.setHeight(templateRow.getHeight());

                // 复制每个单元格
                for (int colIndex = 0; colIndex < Math.max(17, templateRow.getLastCellNum()); colIndex++) {
                    Cell templateCell = templateRow.getCell(colIndex);
                    Cell newCell = newRow.createCell(colIndex);

                    if (templateCell != null) {
                        // 复制单元格值
                        switch (templateCell.getCellType()) {
                            case STRING:
                                newCell.setCellValue(templateCell.getStringCellValue());
                                break;
                            case NUMERIC:
                                newCell.setCellValue(templateCell.getNumericCellValue());
                                break;
                            case BOOLEAN:
                                newCell.setCellValue(templateCell.getBooleanCellValue());
                                break;
                            default:
                                break;
                        }

                        // 表头使用中文字体（宋体）
                        CellStyle newStyle = createCellStyleWithFont(workbook, true);
                        CellStyle templateStyle = templateCell.getCellStyle();

                        // 复制样式属性
                        newStyle.setAlignment(templateStyle.getAlignment());
                        newStyle.setVerticalAlignment(templateStyle.getVerticalAlignment());
                        newStyle.setBorderTop(templateStyle.getBorderTop());
                        newStyle.setBorderRight(templateStyle.getBorderRight());
                        newStyle.setBorderBottom(templateStyle.getBorderBottom());
                        newStyle.setBorderLeft(templateStyle.getBorderLeft());
                        newStyle.setFillForegroundColor(templateStyle.getFillForegroundColor());
                        newStyle.setFillPattern(templateStyle.getFillPattern());
                        newStyle.setWrapText(templateStyle.getWrapText());

                        // 复制字体属性（保持粗体等）
                        if (templateStyle.getFontIndex() > 0) {
                            Font templateFont = templateSheet.getWorkbook().getFontAt(templateStyle.getFontIndex());
                            Font newFont = workbook.createFont();
                            newFont.setBold(templateFont.getBold());
                            newFont.setFontHeightInPoints(templateFont.getFontHeightInPoints());
                            // 表头使用宋体
                            newFont.setFontName("宋体");
                            newStyle.setFont(newFont);
                        }

                        newCell.setCellStyle(newStyle);
                    } else {
                        // 如果模板单元格为空，创建一个带边框的空单元格（使用中文字体）
                        CellStyle emptyStyle = createCellStyleWithFont(workbook, true);
                        newCell.setCellStyle(emptyStyle);
                    }
                }
            } else {
                // 如果模板行不存在，创建一个空行但带边框（使用中文字体）
                Row newRow = newSheet.createRow(rowIndex);
                for (int colIndex = 0; colIndex < 17; colIndex++) {
                    Cell newCell = newRow.createCell(colIndex);
                    CellStyle emptyStyle = createCellStyleWithFont(workbook, true);
                    newCell.setCellStyle(emptyStyle);
                }
            }
        }

        // 复制合并单元格区域
        for (int i = 0; i < templateSheet.getNumMergedRegions(); i++) {
            CellRangeAddress mergedRegion = templateSheet.getMergedRegion(i);
            // 只复制表头部分的合并区域（前4行）
            if (mergedRegion.getFirstRow() < 4) {
                newSheet.addMergedRegion(new CellRangeAddress(
                        mergedRegion.getFirstRow(),
                        mergedRegion.getLastRow(),
                        mergedRegion.getFirstColumn(),
                        mergedRegion.getLastColumn()
                ));
            }
        }

        // 修正表头文本：将"m2"替换为正确的平方米符号"m²"
        fixHeaderText(newSheet);
    }

    /**
     * 修正表头文本，将"m2"替换为正确的平方米符号"m²"
     */
    private void fixHeaderText(Sheet sheet) {
        try {
            // 遍历前4行表头
            for (int rowIndex = 0; rowIndex < 4; rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row != null) {
                    // 遍历该行的所有单元格
                    for (int colIndex = 0; colIndex < row.getLastCellNum(); colIndex++) {
                        Cell cell = row.getCell(colIndex);
                        if (cell != null && cell.getCellType() == CellType.STRING) {
                            String cellValue = cell.getStringCellValue();
                            // 如果包含"m2"，替换为"m²"
                            if (cellValue != null && cellValue.contains("m2")) {
                                String correctedValue = cellValue.replace("m2", "m²");
                                cell.setCellValue(correctedValue);
                                log.debug("修正表头文本：'{}' -> '{}'", cellValue, correctedValue);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("修正表头文本时发生异常: {}", e.getMessage());
        }
    }

    /**
     * 填充PCI数据到模板Sheet
     */
    private void fillPCIDataToTemplate(Sheet sheet, List<RoadCheckPCI> dataList) {
        // 创建数据样式（中文字体用于文本列）
        CellStyle dataStyle = createCellStyleWithFont((SXSSFWorkbook) sheet.getWorkbook(), true);

        // 数值格式，保留3位小数（C列到N列使用）- 数字字母字体
        CellStyle numberStyle3Decimal = createCellStyleWithFont((SXSSFWorkbook) sheet.getWorkbook(), false, "0.000");

        // 数值格式，保留2位小数（破损率和PCI使用）- 数字字母字体
        CellStyle numberStyle2Decimal = createCellStyleWithFont((SXSSFWorkbook) sheet.getWorkbook(), false, "0.00");

        int rowIndex = 4; // 从第5行开始填充数据（模板前4行是表头）

        for (RoadCheckPCI record : dataList) {
            Row row = sheet.createRow(rowIndex++);

            // 确保创建17个单元格（与表头列数一致，包括新增的路面类型列）
            for (int i = 0; i < 17; i++) {
                row.createCell(i);
            }

            int colIndex = 0;

            // A列：开始桩号 - 格式化为标准K格式
            Cell cell0 = row.getCell(colIndex++);
            String formattedStartCode = StakeCodeUtil.formatStakeCode(record.getStartCode());
            cell0.setCellValue(formattedStartCode);
            cell0.setCellStyle(dataStyle);

            // B列：结束桩号 - 格式化为标准K格式
            Cell cell1 = row.getCell(colIndex++);
            String formattedEndCode = StakeCodeUtil.formatStakeCode(record.getEndCode());
            cell1.setCellValue(formattedEndCode);
            cell1.setCellStyle(dataStyle);

            // C列到N列：各类破损面积数据 - 保留3位小数
            setPCIDecimalCell(row, colIndex++, record.getCrackArea(), numberStyle3Decimal);          // C列
            setPCIDecimalCell(row, colIndex++, record.getBlockCrackArea(), numberStyle3Decimal);     // D列
            setPCIDecimalCell(row, colIndex++, record.getLongitudinalCrackArea(), numberStyle3Decimal); // E列
            setPCIDecimalCell(row, colIndex++, record.getTransverseCrackArea(), numberStyle3Decimal); // F列
            setPCIDecimalCell(row, colIndex++, record.getSinkArea(), numberStyle3Decimal);           // G列
            setPCIDecimalCell(row, colIndex++, record.getRutArea(), numberStyle3Decimal);            // H列
            setPCIDecimalCell(row, colIndex++, record.getWaveBumpArea(), numberStyle3Decimal);       // I列
            setPCIDecimalCell(row, colIndex++, record.getPitArea(), numberStyle3Decimal);            // J列
            setPCIDecimalCell(row, colIndex++, record.getLooseArea(), numberStyle3Decimal);          // K列
            setPCIDecimalCell(row, colIndex++, record.getBleedingArea(), numberStyle3Decimal);       // L列
            setPCIDecimalCell(row, colIndex++, record.getPatchAreaPart(), numberStyle3Decimal);      // M列
            setPCIDecimalCell(row, colIndex++, record.getPatchAreaStrip(), numberStyle3Decimal);     // N列

            // O列：破损率DR(%) - 保留3位小数
            setPCIDecimalCell(row, colIndex++, record.getDamageRate(), numberStyle3Decimal);

            // P列：PCI - 保留2位小数
            setPCIDecimalCell(row, colIndex++, record.getPci(), numberStyle2Decimal);

            // Q列：路面类型
            Cell roadTypeCell = row.getCell(colIndex);
            roadTypeCell.setCellValue(record.getRoadType() != null ? record.getRoadType() : "沥青路面");
            roadTypeCell.setCellStyle(dataStyle);
        }
    }

    /**
     * 设置PCI数据单元格
     */
    private void setPCIDecimalCell(Row row, int colIndex, BigDecimal value, CellStyle style) {
        Cell cell = row.getCell(colIndex);  // 使用getCell，因为单元格已经预先创建
        if (value != null) {
            cell.setCellValue(value.doubleValue());
        } else {
            cell.setCellValue(0.0);
        }

        // 确保单元格样式包含完整边框
        if (style == null) {
            SXSSFWorkbook workbook = (SXSSFWorkbook) row.getSheet().getWorkbook();
            // 根据列索引设置不同的小数位数和字体：C列到O列(2-14)保留3位小数，PCI列(15)保留2位小数，路面类型列(16)为文本
            if (colIndex >= 2 && colIndex <= 14) {
                cell.setCellStyle(createCellStyleWithFont(workbook, false, "0.000"));
            } else if (colIndex == 15) {
                cell.setCellStyle(createCellStyleWithFont(workbook, false, "0.00"));
            } else {
                // 路面类型列或其他文本列 - 使用中文字体
                cell.setCellStyle(createCellStyleWithFont(workbook, true));
            }
        } else {
            cell.setCellStyle(style);
        }
    }

    /**
     * 在Excel表格中添加一行等级统计
     */
    private void addGradeRow(Sheet sheet, int rowIndex, String gradeName, CellStyle dataStyle,
                             CellStyle numberStyle, CellStyle percentStyle, double totalDistance,
                             double upDistance, double downDistance) {
        Row row = sheet.createRow(rowIndex);

        // A,B,C列合并显示等级名称（中文字体）
        Cell cellA = row.createCell(0);
        cellA.setCellValue(gradeName);
        cellA.setCellStyle(dataStyle);

        // 合并A,B,C列
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, 2));

        // 创建空的B,C列单元格以保持格式一致（中文字体）
        Cell cellB = row.createCell(1);
        cellB.setCellStyle(dataStyle);
        Cell cellC = row.createCell(2);
        cellC.setCellStyle(dataStyle);

        // D列为空（中文字体）
        Cell cellD = row.createCell(3);
        cellD.setCellStyle(dataStyle);

        // E列：上行该等级的公里数（数字字母字体）
        Cell cellE = row.createCell(4);
        cellE.setCellValue(upDistance);
        cellE.setCellStyle(numberStyle);

        // F,G列合并显示上行该等级占比（数字字母字体）
        Cell cellF = row.createCell(5);
        double upPercentage = (totalDistance > 0) ? (upDistance / totalDistance) : 0;
        cellF.setCellValue(upPercentage);
        cellF.setCellStyle(percentStyle);

        // 合并F,G列
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 5, 6));

        // 创建空的G列单元格以保持格式一致（数字字母字体）
        Cell cellG = row.createCell(6);
        cellG.setCellStyle(percentStyle);

        // H列：下行该等级的公里数（数字字母字体）
        Cell cellH = row.createCell(7);
        cellH.setCellValue(downDistance);
        cellH.setCellStyle(numberStyle);

        // I,J列合并显示下行该等级占比（数字字母字体）
        Cell cellI = row.createCell(8);
        double downPercentage = (totalDistance > 0) ? (downDistance / totalDistance) : 0;
        cellI.setCellValue(downPercentage);
        cellI.setCellStyle(percentStyle);

        // 合并I,J列
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 8, 9));

        // 创建空的J列单元格以保持格式一致（数字字母字体）
        Cell cellJ = row.createCell(9);
        cellJ.setCellStyle(percentStyle);
    }



    /**
     * 填充聚合后的PCI数据到Sheet（按百米段或公里段汇总）
     *
     * @param sheet 目标Sheet
     * @param dataList 原始数据列表
     * @param sectionType 段位类型："hundred_section"(百米段) 或 "thousand_section"(公里段)
     */
    private void fillAggregatedPCIData(Sheet sheet, List<RoadCheckPCI> dataList, String sectionType) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }

        // 创建数据样式（中文字体用于文本列）
        CellStyle dataStyle = createCellStyleWithFont((SXSSFWorkbook) sheet.getWorkbook(), true);

        // 数值格式，保留3位小数（C列到N列使用）- 数字字母字体
        CellStyle numberStyle3Decimal = createCellStyleWithFont((SXSSFWorkbook) sheet.getWorkbook(), false, "0.000");

        // 数值格式，保留2位小数（破损率和PCI使用）- 数字字母字体
        CellStyle numberStyle2Decimal = createCellStyleWithFont((SXSSFWorkbook) sheet.getWorkbook(), false, "0.00");

        // 按段位分组汇总数据
        Map<String, List<RoadCheckPCI>> groupedData = new LinkedHashMap<>();

        for (RoadCheckPCI record : dataList) {
            String sectionKey;
            if ("hundred_section".equals(sectionType)) {
                sectionKey = record.getHundredSection();
            } else {
                sectionKey = record.getThousandSection();
            }

            if (sectionKey != null && !sectionKey.trim().isEmpty()) {
                groupedData.computeIfAbsent(sectionKey, k -> new ArrayList<>()).add(record);
            }
        }

        int rowIndex = 4; // 从第5行开始填充数据（前4行是表头）

        // 遍历每个段位分组
        for (Map.Entry<String, List<RoadCheckPCI>> entry : groupedData.entrySet()) {
            String sectionRange = entry.getKey();
            List<RoadCheckPCI> sectionRecords = entry.getValue();

            // 解析段位范围获取起始和结束桩号
            String startCode = "";
            String endCode = "";
            if (sectionRange.contains("~")) {
                String[] parts = sectionRange.split("~");
                if (parts.length == 2) {
                    startCode = parts[0].trim();
                    endCode = parts[1].trim();
                }
            }

            // 计算该段位内各类病害面积的总和
            BigDecimal totalCrackArea = sectionRecords.stream()
                    .map(RoadCheckPCI::getCrackArea)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal totalBlockCrackArea = sectionRecords.stream()
                    .map(RoadCheckPCI::getBlockCrackArea)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal totalLongitudinalCrackArea = sectionRecords.stream()
                    .map(RoadCheckPCI::getLongitudinalCrackArea)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal totalTransverseCrackArea = sectionRecords.stream()
                    .map(RoadCheckPCI::getTransverseCrackArea)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal totalSinkArea = sectionRecords.stream()
                    .map(RoadCheckPCI::getSinkArea)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal totalRutArea = sectionRecords.stream()
                    .map(RoadCheckPCI::getRutArea)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal totalWaveBumpArea = sectionRecords.stream()
                    .map(RoadCheckPCI::getWaveBumpArea)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal totalPitArea = sectionRecords.stream()
                    .map(RoadCheckPCI::getPitArea)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal totalLooseArea = sectionRecords.stream()
                    .map(RoadCheckPCI::getLooseArea)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal totalBleedingArea = sectionRecords.stream()
                    .map(RoadCheckPCI::getBleedingArea)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal totalPatchAreaPart = sectionRecords.stream()
                    .map(RoadCheckPCI::getPatchAreaPart)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal totalPatchAreaStrip = sectionRecords.stream()
                    .map(RoadCheckPCI::getPatchAreaStrip)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 计算总病害面积
            BigDecimal totalDamageArea = totalCrackArea
                    .add(totalBlockCrackArea)
                    .add(totalLongitudinalCrackArea)
                    .add(totalTransverseCrackArea)
                    .add(totalSinkArea)
                    .add(totalRutArea)
                    .add(totalWaveBumpArea)
                    .add(totalPitArea)
                    .add(totalLooseArea)
                    .add(totalBleedingArea)
                    .add(totalPatchAreaPart)
                    .add(totalPatchAreaStrip);

            // 计算段位长度（米）
            double sectionLength = calculateSectionLength(startCode, endCode);

            // 计算破损率DR(%) = 100 * (总病害面积) / (3.8 * 段位长度)
            BigDecimal damageRate;
            if (sectionLength > 0) {
                BigDecimal denominator = new BigDecimal("3.8").multiply(BigDecimal.valueOf(sectionLength));
                damageRate = totalDamageArea.multiply(new BigDecimal("100"))
                        .divide(denominator, 4, BigDecimal.ROUND_HALF_UP);
            } else {
                damageRate = BigDecimal.ZERO;
            }

            // 计算PCI = 100 - 15 * (O列的DR值)^0.412，使用DR的实际数值
            double pci;
            if (damageRate.compareTo(BigDecimal.ZERO) > 0) {
                // 使用DR的实际数值计算PCI，不取整
                double drValue = damageRate.doubleValue();
                pci = 100.0 - 15.0 * Math.pow(drValue, 0.412);
            } else {
                pci = 100.0;
            }

            // 创建数据行
            Row row = sheet.createRow(rowIndex++);

            // 确保创建17个单元格（与表头列数一致，包括路面类型列）
            for (int i = 0; i < 17; i++) {
                row.createCell(i);
            }

            int colIndex = 0;

            // 填充行数据
            Cell cell0 = row.getCell(colIndex++);
            cell0.setCellValue(startCode);
            cell0.setCellStyle(dataStyle);

            Cell cell1 = row.getCell(colIndex++);
            cell1.setCellValue(endCode);
            cell1.setCellStyle(dataStyle);

            // C列到N列：各类病害面积数据 - 保留3位小数
            setPCIDecimalCell(row, colIndex++, totalCrackArea, numberStyle3Decimal);          // C列
            setPCIDecimalCell(row, colIndex++, totalBlockCrackArea, numberStyle3Decimal);     // D列
            setPCIDecimalCell(row, colIndex++, totalLongitudinalCrackArea, numberStyle3Decimal); // E列
            setPCIDecimalCell(row, colIndex++, totalTransverseCrackArea, numberStyle3Decimal); // F列
            setPCIDecimalCell(row, colIndex++, totalSinkArea, numberStyle3Decimal);           // G列
            setPCIDecimalCell(row, colIndex++, totalRutArea, numberStyle3Decimal);            // H列
            setPCIDecimalCell(row, colIndex++, totalWaveBumpArea, numberStyle3Decimal);       // I列
            setPCIDecimalCell(row, colIndex++, totalPitArea, numberStyle3Decimal);            // J列
            setPCIDecimalCell(row, colIndex++, totalLooseArea, numberStyle3Decimal);          // K列
            setPCIDecimalCell(row, colIndex++, totalBleedingArea, numberStyle3Decimal);       // L列
            setPCIDecimalCell(row, colIndex++, totalPatchAreaPart, numberStyle3Decimal);      // M列
            setPCIDecimalCell(row, colIndex++, totalPatchAreaStrip, numberStyle3Decimal);     // N列

            // O列：破损率DR(%) - 保留3位小数
            Cell drCell = row.getCell(colIndex++);
            drCell.setCellValue(damageRate.doubleValue());
            drCell.setCellStyle(numberStyle3Decimal);

            // P列：PCI - 保留2位小数
            Cell pciCell = row.getCell(colIndex++);
            pciCell.setCellValue(pci);
            pciCell.setCellStyle(numberStyle2Decimal);
            
            // Q列：路面类型（聚合数据中使用第一条记录的路面类型）
            Cell roadTypeCell = row.getCell(colIndex);
            String roadType = sectionRecords.isEmpty() ? "沥青路面" : 
                             (sectionRecords.get(0).getRoadType() != null ? sectionRecords.get(0).getRoadType() : "沥青路面");
            roadTypeCell.setCellValue(roadType);
            roadTypeCell.setCellStyle(dataStyle);
        }
    }

    /**
     * 计算段位长度（米）
     *
     * @param startCode 起始桩号
     * @param endCode 结束桩号
     * @return 段位长度（米）
     */
    private double calculateSectionLength(String startCode, String endCode) {
        if (startCode == null || endCode == null || startCode.trim().isEmpty() || endCode.trim().isEmpty()) {
            return 0.0;
        }

        try {
            // 使用 SummaryData 内部的 parseStakeCodeToMeters 方法来计算
            SummaryData tempData = new SummaryData(startCode, endCode);
            return tempData.calculateActualDistance();
        } catch (Exception e) {
            log.warn("计算段位长度失败，起始桩号: {}, 结束桩号: {}, 错误: {}", startCode, endCode, e.getMessage());
        }

        return 0.0;
    }

    @Override
    public int deleteRoadCheckRecordByRoadId(Long roadId) {
        return roadCheckPCIMapper.deleteRoadCheckRecordByRoadId(roadId);
    }

    /**
     * 计算PCI Word文档的页数（返回详细信息）
     * @return int数组 [总页数, pageOne, pageTwo]
     */
    private int[] calculatePCIDocumentPagesWithDetails(Collection<SummaryData> summaryData, List<RoadCheckPCI> upData,
                                                       List<RoadCheckPCI> downData, String direction) {
        try {
            // 基础页数
            int basePages = 0;

            // 计算汇总表格页数
            int summaryRows = summaryData != null ? summaryData.size() : 0;
            int summaryPages = calculateTablePages(summaryRows + 6, 37, 40); // +6是固定的合计行和等级行

            // 根据方向确定当前数据行数
            List<RoadCheckPCI> currentDirectionData = "上行".equals(direction) ? upData : downData;
            int dataRows = currentDirectionData != null ? currentDirectionData.size() : 0;

            // 计算数据表格页数
            int dataPages = calculateTablePages(dataRows, 23, 25);

            // 计算 pageOne = 汇总表格的总页码 + 6
            int pageOne = summaryPages + basePages;

            // 计算 pageTwo = pageOne + 1
            int pageTwo = pageOne + 1;

            // 总页数计算
            int totalPages = basePages + summaryPages + dataPages;

            log.info("PCI页数计算详情：基础页数={}, 汇总行数={}, 汇总页数={}, {}数据行数={}, 数据页数={}, 总页数={}, pageOne={}, pageTwo={}",
                    basePages, summaryRows, summaryPages, direction, dataRows, dataPages, totalPages, pageOne, pageTwo);

            return new int[]{totalPages, pageOne, pageTwo};

        } catch (Exception e) {
            log.warn("计算PCI文档页数失败，使用默认值: {}", e.getMessage());
            return new int[]{10, 13, 14}; // 默认返回页数
        }
    }

    /**
     * 计算PCI Word文档的页数（保持向后兼容性）
     * 考虑第一页的特殊行数限制：
     * - 汇总表格第一页只有38行数据，后续页41行
     * - 上/下行表格第一页只有40行数据，后续页42行
     *
     * @param summaryData 汇总数据
     * @param upData 上行数据
     * @param downData 下行数据
     * @param direction 当前方向
     * @return 文档页数
     */
    private int calculatePCIDocumentPages(Collection<SummaryData> summaryData, List<RoadCheckPCI> upData,
                                          List<RoadCheckPCI> downData, String direction) {
        int[] calculations = calculatePCIDocumentPagesWithDetails(summaryData, upData, downData, direction);
        return calculations[0]; // 返回总页数
    }

    /**
     * 计算表格页数，考虑第一页和后续页的不同行数限制
     *
     * @param totalRows 总行数
     * @param firstPageRows 第一页可容纳的行数
     * @param otherPageRows 后续页可容纳的行数
     * @return 所需页数
     */
    private int calculateTablePages(int totalRows, int firstPageRows, int otherPageRows) {
        if (totalRows <= 0) {
            return 0;
        }

        if (totalRows <= firstPageRows) {
            // 第一页就能容纳所有数据
            return 1;
        } else {
            // 需要多页
            int remainingRows = totalRows - firstPageRows; // 除第一页外的剩余行数
            int additionalPages = (int) Math.ceil((double) remainingRows / otherPageRows); // 额外需要的页数
            return 1 + additionalPages; // 第一页 + 额外页数
        }
    }

    /**
     * 创建带字体的单元格样式
     * @param workbook 工作簿
     * @param isChinese 是否为中文字体（宋体），false为数字字母字体（新罗马）
     * @param dataFormat 数据格式，如"0.000"、"0.00"等
     * @return 单元格样式
     */
    private CellStyle createCellStyleWithFont(SXSSFWorkbook workbook, boolean isChinese, String dataFormat) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        
        // 创建字体
        Font font = workbook.createFont();
        if (isChinese) {
            // 中文字体：宋体
            font.setFontName("宋体");
        } else {
            // 数字字母字体：Times New Roman（新罗马）
            font.setFontName("Times New Roman");
        }
        font.setFontHeightInPoints((short) 10);
        style.setFont(font);
        
        // 设置数据格式
        if (dataFormat != null && !dataFormat.isEmpty()) {
            DataFormat format = workbook.createDataFormat();
            style.setDataFormat(format.getFormat(dataFormat));
        }
        
        return style;
    }

    /**
     * 创建带字体的单元格样式（无数据格式）
     * @param workbook 工作簿
     * @param isChinese 是否为中文字体（宋体），false为数字字母字体（新罗马）
     * @return 单元格样式
     */
    private CellStyle createCellStyleWithFont(SXSSFWorkbook workbook, boolean isChinese) {
        return createCellStyleWithFont(workbook, isChinese, null);
    }

    private boolean isLatinDigitOrSymbol(char c) {
        if (Character.isDigit(c)) return true;
        if ((c >= 'A' && c <= 'Z') || (c >= 'a' && c <= 'z')) return true;
        switch (c) {
            case '.':
            case '%':
            case '+':
            case '-':
            case '(':
            case ')':
            case '~':
            case '/':
            case ':':
                return true;
            default:
                return false;
        }
    }

    private void createRunWithFont(XWPFParagraph paragraph, String content, boolean latin,
                                   Integer inheritFontSize, Boolean inheritBold, Boolean inheritItalic, String inheritColor) {
        if (content == null || content.isEmpty()) {
            return;
        }
        XWPFRun run = paragraph.createRun();
        run.setText(content);
        run.setFontFamily(latin ? "Times New Roman" : "宋体");
        if (inheritFontSize != null && inheritFontSize > 0) {
            run.setFontSize(inheritFontSize);
        } else {
            run.setFontSize(10); // 默认10号
        }
        if (inheritBold != null) run.setBold(inheritBold);
        if (inheritItalic != null) run.setItalic(inheritItalic);
        if (inheritColor != null && !inheritColor.isEmpty()) run.setColor(inheritColor);
    }
}