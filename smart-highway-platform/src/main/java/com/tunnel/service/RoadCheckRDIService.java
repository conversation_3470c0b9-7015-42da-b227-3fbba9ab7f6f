package com.tunnel.service;

import com.tunnel.domain.BatchAddResponse;
import com.tunnel.domain.RoadCheckRDI;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 路面车辙深度检测信息Service接口
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
public interface RoadCheckRDIService {
    /**
     * 查询路面车辙深度检测信息
     *
     * @param id 路面车辙深度检测信息主键
     * @return 路面车辙深度检测信息
     */
    public RoadCheckRDI selectScRoadCheckRdById(Long id);

    /**
     * 查询路面车辙深度检测信息列表
     *
     * @param RoadCheckRDI 路面车辙深度检测信息
     * @return 路面车辙深度检测信息集合
     */
    public List<RoadCheckRDI> selectScRoadCheckRdList(RoadCheckRDI RoadCheckRDI);

    /**
     * 新增路面车辙深度检测信息
     *
     * @param RoadCheckRDI 路面车辙深度检测信息
     * @return 结果
     */
    public int insertScRoadCheckRd(RoadCheckRDI RoadCheckRDI);

    /**
     * 修改路面车辙深度检测信息
     *
     * @param RoadCheckRDI 路面车辙深度检测信息
     * @return 结果
     */
    public int updateScRoadCheckRd(RoadCheckRDI RoadCheckRDI);

    /**
     * 批量删除路面车辙深度检测信息
     *
     * @param ids 需要删除的路面车辙深度检测信息主键集合
     * @return 结果
     */
    public int deleteScRoadCheckRdByIds(Long[] ids);

    /**
     * 删除路面车辙深度检测信息信息
     *
     * @param id 路面车辙深度检测信息主键
     * @return 结果
     */
    public int deleteScRoadCheckRdById(Long id);

    /**
     * 根据道路ID获取路面车辙深度检测信息
     *
     * @param roadId 道路ID
     * @return 路面车辙深度检测信息集合
     */
    public List<RoadCheckRDI> selectScRoadCheckRdByRoadId(Long roadId);

    /**
     * 批量导入路面车辙深度检测数据
     *
     * @param file 导入文件
     * @param roadId 道路ID
     * @return 导入结果
     */
    public BatchAddResponse batchImport(MultipartFile file, Long roadId);
    
    /**
     * 优化导出路面车辙深度检测信息
     *
     * @param response HTTP响应
     * @param RoadCheckRDI 查询条件
     */
    public void exportOptimized(HttpServletResponse response, RoadCheckRDI RoadCheckRDI);
    
    /**
     * 获取路面车辙深度检测信息记录数
     *
     * @param RoadCheckRDI 查询条件
     * @return 记录数
     */
    public int countScRoadCheckRd(RoadCheckRDI RoadCheckRDI);

    void exportRDIByDirection(HttpServletResponse response, Long roadId);
    
    /**
     * 导出RDI Word报告（分上行下行）
     *
     * @param response HTTP响应
     * @param roadId 道路ID
     * @param teamId 检测分组ID
     * @param dateTime 检测日期时间
     * @param monthDate 月份日期
     * @param titleName 页眉标题名称
     * @param checkName 检测人员姓名
     * @param reviewName 审核人员姓名
     */
    void exportWordRDIByDirection(HttpServletResponse response, Long roadId, Long teamId, String dateTime, String monthDate, String titleName, String checkName, String reviewName);

    /**
     * 根据道路ID删除路面车辙深度检测信息
     *
     * @param roadId 道路ID
     * @return 结果
     */
    public int deleteScRoadCheckRdByRoadId(Long roadId);
}