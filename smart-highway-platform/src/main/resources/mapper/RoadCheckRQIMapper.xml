<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.RoadCheckRQIMapper">

    <resultMap type="com.tunnel.domain.RoadCheckRQI" id="RoadRQIResult">
        <result property="id" column="id"/>
        <result property="roadId" column="road_id"/>
        <result property="startCode" column="start_code"/>
        <result property="endCode" column="end_code"/>
        <result property="hundredSection" column="hundred_section"/>
        <result property="thousandSection" column="thousand_section"/>
        <result property="leftIri" column="left_iri"/>
        <result property="rightIri" column="right_iri"/>
        <result property="representIri" column="represent_iri"/>
        <result property="rqi" column="rqi"/>
        <result property="roadType" column="road_type"/>
        <result property="remark" column="remark"/>
        <result property="direction" column="direction"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="creator" column="creator"/>
        <result property="modifier" column="modifier"/>
        <result property="roadName" column="road_name"/>
    </resultMap>

    <sql id="selectRoadRQIVo">
        select id, road_id, start_code, end_code, hundred_section, thousand_section, left_iri, right_iri, represent_iri, rqi, road_type, 
               remark, direction, create_time, update_time, creator, modifier
        from sc_road_check_rqi
    </sql>

    <select id="selectRoadRQIList" parameterType="com.tunnel.domain.RoadCheckRQI" resultMap="RoadRQIResult">
        <include refid="selectRoadRQIVo"/>
        <where>
            <if test="roadId != null ">and road_id = #{roadId}</if>
            <if test="startCode != null and startCode != ''">and start_code = #{startCode}</if>
            <if test="endCode != null and endCode != ''">and end_code = #{endCode}</if>
            <if test="roadType != null and roadType != ''">and road_type = #{roadType}</if>
            <if test="direction != null">and direction = #{direction}</if>
        </where>
    </select>
    
    <select id="selectRoadRQIListByPage" resultMap="RoadRQIResult">
        <include refid="selectRoadRQIVo"/>
        <where>
            <if test="roadCheckRQI.roadId != null ">and road_id = #{roadCheckRQI.roadId}</if>
            <if test="roadCheckRQI.startCode != null and roadCheckRQI.startCode != ''">and start_code = #{roadCheckRQI.startCode}</if>
            <if test="roadCheckRQI.endCode != null and roadCheckRQI.endCode != ''">and end_code = #{roadCheckRQI.endCode}</if>
            <if test="roadCheckRQI.roadType != null and roadCheckRQI.roadType != ''">and road_type = #{roadCheckRQI.roadType}</if>
        </where>
        limit #{offset}, #{limit}
    </select>

    <select id="selectRoadRQIById" parameterType="Long" resultMap="RoadRQIResult">
        <include refid="selectRoadRQIVo"/>
        where id = #{id}
    </select>

    <select id="selectRoadRQIByRoadId" parameterType="Long" resultMap="RoadRQIResult">
        <include refid="selectRoadRQIVo"/>
        where road_id = #{roadId}
    </select>

    <insert id="insertRoadRQI" parameterType="com.tunnel.domain.RoadCheckRQI" useGeneratedKeys="true" keyProperty="id">
        insert into sc_road_check_rqi
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="roadId != null">road_id,</if>
            <if test="startCode != null and startCode != ''">start_code,</if>
            <if test="endCode != null and endCode != ''">end_code,</if>
            <if test="hundredSection != null and hundredSection != ''">hundred_section,</if>
            <if test="thousandSection != null and thousandSection != ''">thousand_section,</if>
            <if test="leftIri != null">left_iri,</if>
            <if test="rightIri != null">right_iri,</if>
            <if test="representIri != null">represent_iri,</if>
            <if test="rqi != null">rqi,</if>
            <if test="roadType != null and roadType != ''">road_type,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="direction != null and direction != ''">direction,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="roadId != null">#{roadId},</if>
            <if test="startCode != null and startCode != ''">#{startCode},</if>
            <if test="endCode != null and endCode != ''">#{endCode},</if>
            <if test="hundredSection != null and hundredSection != ''">#{hundredSection},</if>
            <if test="thousandSection != null and thousandSection != ''">#{thousandSection},</if>
            <if test="leftIri != null">#{leftIri},</if>
            <if test="rightIri != null">#{rightIri},</if>
            <if test="representIri != null">#{representIri},</if>
            <if test="rqi != null">#{rqi},</if>
            <if test="roadType != null and roadType != ''">#{roadType},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="direction != null and direction != ''">#{direction},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
        </trim>
    </insert>

    <update id="updateRoadRQI" parameterType="com.tunnel.domain.RoadCheckRQI">
        update sc_road_check_rqi
        <trim prefix="SET" suffixOverrides=",">
            <if test="roadId != null">road_id = #{roadId},</if>
            <if test="startCode != null and startCode != ''">start_code = #{startCode},</if>
            <if test="endCode != null and endCode != ''">end_code = #{endCode},</if>
            <if test="hundredSection != null and hundredSection != ''">hundred_section = #{hundredSection},</if>
            <if test="thousandSection != null and thousandSection != ''">thousand_section = #{thousandSection},</if>
            <if test="leftIri != null">left_iri = #{leftIri},</if>
            <if test="rightIri != null">right_iri = #{rightIri},</if>
            <if test="representIri != null">represent_iri = #{representIri},</if>
            <if test="rqi != null">rqi = #{rqi},</if>
            <if test="roadType != null and roadType != ''">road_type = #{roadType},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="direction != null and direction != ''">direction = #{direction},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRoadRQIById" parameterType="Long">
        delete from sc_road_check_rqi where id = #{id}
    </delete>

    <delete id="deleteRoadRQIByIds" parameterType="Long">
        delete from sc_road_check_rqi where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <delete id="deleteRoadRQIByRoadId" parameterType="Long">
        delete from sc_road_check_rqi where road_id = #{roadId}
    </delete>
    
    <insert id="batchInsert" parameterType="java.util.List">
        insert into sc_road_check_rqi(road_id, start_code, end_code, hundred_section, thousand_section, 
                           left_iri, right_iri, represent_iri, rqi, road_type, 
                           remark, direction, creator)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.roadId}, #{item.startCode}, #{item.endCode}, #{item.hundredSection}, #{item.thousandSection}, 
             #{item.leftIri}, #{item.rightIri}, #{item.representIri}, #{item.rqi}, #{item.roadType}, 
             #{item.remark}, #{item.direction}, #{item.creator})
        </foreach>
    </insert>
    
    <select id="countRoadRQI" parameterType="com.tunnel.domain.RoadCheckRQI" resultType="int">
        select count(1) from sc_road_check_rqi
        <where>
            <if test="roadId != null ">and road_id = #{roadId}</if>
            <if test="startCode != null and startCode != ''">and start_code = #{startCode}</if>
            <if test="endCode != null and endCode != ''">and end_code = #{endCode}</if>
            <if test="roadType != null and roadType != ''">and road_type = #{roadType}</if>
        </where>
    </select>
</mapper> 