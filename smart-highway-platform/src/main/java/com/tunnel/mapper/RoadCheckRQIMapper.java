package com.tunnel.mapper;

import com.tunnel.domain.RoadCheckRQI;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 路面平整度检测信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-01
 */
public interface RoadCheckRQIMapper {
    /**
     * 查询路面平整度检测信息
     *
     * @param id 路面平整度检测信息主键
     * @return 路面平整度检测信息
     */
    public RoadCheckRQI selectRoadRQIById(Long id);

    /**
     * 查询路面平整度检测信息列表
     *
     * @param roadCheckRQI 路面平整度检测信息
     * @return 路面平整度检测信息集合
     */
    public List<RoadCheckRQI> selectRoadRQIList(RoadCheckRQI roadCheckRQI);

    /**
     * 分页查询路面平整度检测信息列表
     *
     * @param roadCheckRQI 路面平整度检测信息
     * @param offset 起始位置
     * @param limit 查询条数
     * @return 路面平整度检测信息集合
     */
    public List<RoadCheckRQI> selectRoadRQIListByPage(@Param("roadRQI") RoadCheckRQI roadCheckRQI, @Param("offset") int offset, @Param("limit") int limit);

    /**
     * 新增路面平整度检测信息
     *
     * @param roadCheckRQI 路面平整度检测信息
     * @return 结果
     */
    public int insertRoadRQI(RoadCheckRQI roadCheckRQI);

    /**
     * 修改路面平整度检测信息
     *
     * @param roadCheckRQI 路面平整度检测信息
     * @return 结果
     */
    public int updateRoadRQI(RoadCheckRQI roadCheckRQI);

    /**
     * 删除路面平整度检测信息
     *
     * @param id 路面平整度检测信息主键
     * @return 结果
     */
    public int deleteRoadRQIById(Long id);

    /**
     * 批量删除路面平整度检测信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRoadRQIByIds(Long[] ids);

    /**
     * 批量新增路面平整度检测数据
     *
     * @param list 路面平整度检测信息列表
     * @return 结果
     */
    public int batchInsert(List<RoadCheckRQI> list);
    
    /**
     * 根据道路ID获取路面平整度检测信息
     *
     * @param roadId 道路ID
     * @return 路面平整度检测信息集合
     */
    public List<RoadCheckRQI> selectRoadRQIByRoadId(Long roadId);
    
    /**
     * 获取记录总数
     *
     * @param roadCheckRQI 查询条件
     * @return 总记录数
     */
    public int countRoadRQI(RoadCheckRQI roadCheckRQI);

    /**
     * 根据道路ID删除路面平整度检测信息
     *
     * @param roadId 道路ID
     * @return 结果
     */
    public int deleteRoadRQIByRoadId(Long roadId);
} 