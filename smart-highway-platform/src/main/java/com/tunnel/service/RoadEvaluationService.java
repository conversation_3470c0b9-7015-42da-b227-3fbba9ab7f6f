package com.tunnel.service;

import com.tunnel.domain.RoadEvaluation;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 道路技术状况评定信息Service接口
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
public interface RoadEvaluationService {
    /**
     * 查询道路技术状况评定信息
     *
     * @param id 道路技术状况评定信息主键
     * @return 道路技术状况评定信息
     */
    public RoadEvaluation selectRoadEvaluationById(Long id);

    /**
     * 查询道路技术状况评定信息列表
     *
     * @param roadEvaluation 道路技术状况评定信息
     * @return 道路技术状况评定信息集合
     */
    public List<RoadEvaluation> selectRoadEvaluationList(RoadEvaluation roadEvaluation);

    /**
     * 新增道路技术状况评定信息
     *
     * @param roadEvaluation 道路技术状况评定信息
     * @return 结果
     */
    public int insertRoadEvaluation(RoadEvaluation roadEvaluation);

    /**
     * 修改道路技术状况评定信息
     *
     * @param roadEvaluation 道路技术状况评定信息
     * @return 结果
     */
    public int updateRoadEvaluation(RoadEvaluation roadEvaluation);

    /**
     * 批量删除道路技术状况评定信息
     *
     * @param ids 需要删除的道路技术状况评定信息主键集合
     * @return 结果
     */
    public int deleteRoadEvaluationByIds(Long[] ids);

    /**
     * 删除道路技术状况评定信息信息
     *
     * @param id 道路技术状况评定信息主键
     * @return 结果
     */
    public int deleteRoadEvaluationById(Long id);

    /**
     * 校验路线编号是否唯一
     *
     * @param roadEvaluation 道路技术状况评定信息
     * @return 结果
     */
    public boolean checkRoadCodeUnique(RoadEvaluation roadEvaluation);

    /**
     * 导出道路技术状况评定信息列表
     *
     * @param response HTTP响应
     * @param roadEvaluation 查询条件
     */
    public void exportRoadEvaluation(HttpServletResponse response, RoadEvaluation roadEvaluation);

    /**
     * 导入道路技术状况评定信息数据
     *
     * @param file Excel文件
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param projectId 项目ID
     * @return 结果
     */
    public String importRoadEvaluation(MultipartFile file, boolean updateSupport, Long projectId) throws Exception;

    /**
     * 下载导入模板
     *
     * @param response HTTP响应
     */
    public void importTemplate(HttpServletResponse response);

    /**
     * 导出Word报告
     *
     * @param response HTTP响应
     * @param params 参数
     */
    public void exportWordReport(HttpServletResponse response, java.util.Map<String, Object> params) throws Exception;
} 