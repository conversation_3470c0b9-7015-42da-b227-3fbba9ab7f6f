package com.tunnel.domain;

import lombok.Data;
import java.util.ArrayList;
import java.util.List;

/**
 * 批量添加响应结果
 *
 * <AUTHOR>
 * @date 2024-11-28
 */
@Data
public class BatchAddResponse {
    
    /**
     * 状态码：0-成功，1-失败
     */
    private int status;
    
    /**
     * 返回消息
     */
    private String msg;
    
    /**
     * 处理的数据总数
     */
    private int totalCount;
    
    /**
     * 成功导入的数据数量
     */
    private int successCount;
    
    /**
     * 失败的数据数量
     */
    private int failCount;
    
    /**
     * 错误详情列表
     */
    private List<ErrorDetail> errors = new ArrayList<>();
    
    /**
     * 汇总信息
     */
    private String summary;
    
    /**
     * 错误详情内部类
     */
    @Data
    public static class ErrorDetail {
        /**
         * 工作表名称
         */
        private String sheet;
        
        /**
         * 行号
         */
        private int row;
        
        /**
         * 字段名称
         */
        private String field;
        
        /**
         * 错误信息
         */
        private String message;
        
        /**
         * 单元格值
         */
        private String cellValue;
        
        public ErrorDetail(String sheet, int row, String field, String message) {
            this.sheet = sheet;
            this.row = row;
            this.field = field;
            this.message = message;
        }
        
        public ErrorDetail(String sheet, int row, String field, String message, String cellValue) {
            this.sheet = sheet;
            this.row = row;
            this.field = field;
            this.message = message;
            this.cellValue = cellValue;
        }
    }
    
    /**
     * 添加错误详情
     */
    public void addError(String sheet, int row, String field, String message) {
        this.errors.add(new ErrorDetail(sheet, row, field, message));
        this.failCount++;
    }
    
    /**
     * 添加错误详情（包含单元格值）
     */
    public void addError(String sheet, int row, String field, String message, String cellValue) {
        this.errors.add(new ErrorDetail(sheet, row, field, message, cellValue));
        this.failCount++;
    }
    
    /**
     * 设置成功数量
     */
    public void addSuccessCount(int count) {
        this.successCount += count;
    }
    
    /**
     * 计算总数
     */
    public void calculateTotal() {
        this.totalCount = this.successCount + this.failCount;
    }
    
    /**
     * 生成汇总信息
     */
    public void generateSummary() {
        calculateTotal();
        if (status == 0) {
            this.summary = String.format("导入完成：总计 %d 条数据，成功 %d 条，失败 %d 条", 
                totalCount, successCount, failCount);
        } else {
            this.summary = String.format("导入失败：总计 %d 条数据，成功 %d 条，失败 %d 条", 
                totalCount, successCount, failCount);
        }
        
        if (failCount > 0) {
            this.summary += String.format("\n请查看下方错误详情，共 %d 个错误", errors.size());
        }
    }
}
