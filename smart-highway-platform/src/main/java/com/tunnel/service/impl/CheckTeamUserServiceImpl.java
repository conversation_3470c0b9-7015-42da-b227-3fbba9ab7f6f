package com.tunnel.service.impl;

import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.exception.ServiceException;
import com.tunnel.common.utils.DateUtils;
import com.tunnel.common.utils.SecurityUtils;
import com.tunnel.common.utils.StringUtils;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.CheckTeamUser;
import com.tunnel.domain.CheckUser;
import com.tunnel.mapper.CheckTeamUserMapper;
import com.tunnel.service.CheckTeamUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 检测分组用户关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
@Service
@Slf4j
public class CheckTeamUserServiceImpl implements CheckTeamUserService {
    @Autowired
    private CheckTeamUserMapper checkTeamUserMapper;

    /**
     * 查询检测分组用户关联
     *
     * @param id 检测分组用户关联主键
     * @return 检测分组用户关联
     */
    @Override
    public CheckTeamUser selectCheckTeamUserById(Long id) {
        return checkTeamUserMapper.selectCheckTeamUserById(id);
    }

    /**
     * 查询检测分组用户关联列表
     *
     * @param checkTeamUser 检测分组用户关联
     * @return 检测分组用户关联
     */
    @Override
    public List<CheckTeamUser> selectCheckTeamUserList(CheckTeamUser checkTeamUser) {
        return checkTeamUserMapper.selectCheckTeamUserList(checkTeamUser);
    }

    /**
     * 新增检测分组用户关联
     *
     * @param checkTeamUser 检测分组用户关联
     * @return 结果
     */
    @Override
    public int insertCheckTeamUser(CheckTeamUser checkTeamUser) {
        // 检查关联是否已存在
        if (checkTeamUserExists(checkTeamUser.getTeamId(), checkTeamUser.getUserId())) {
            throw new ServiceException("该用户已在此分组中，无法重复添加");
        }
        
        checkTeamUser.setCreateBy(SecurityUtils.getUsername());
        checkTeamUser.setCreateTime(DateUtils.getNowDate());
        
        // 如果未设置排序号，则获取下一个排序号
        if (checkTeamUser.getSortNum() == null) {
            checkTeamUser.setSortNum(getNextSortNumByTeamId(checkTeamUser.getTeamId()));
        }
        
        return checkTeamUserMapper.insertCheckTeamUser(checkTeamUser);
    }

    /**
     * 修改检测分组用户关联
     *
     * @param checkTeamUser 检测分组用户关联
     * @return 结果
     */
    @Override
    public int updateCheckTeamUser(CheckTeamUser checkTeamUser) {
        checkTeamUser.setUpdateBy(SecurityUtils.getUsername());
        checkTeamUser.setUpdateTime(DateUtils.getNowDate());
        return checkTeamUserMapper.updateCheckTeamUser(checkTeamUser);
    }

    /**
     * 批量删除检测分组用户关联
     *
     * @param ids 需要删除的检测分组用户关联主键
     * @return 结果
     */
    @Override
    public int deleteCheckTeamUserByIds(Long[] ids) {
        return checkTeamUserMapper.deleteCheckTeamUserByIds(ids);
    }

    /**
     * 删除检测分组用户关联信息
     *
     * @param id 检测分组用户关联主键
     * @return 结果
     */
    @Override
    public int deleteCheckTeamUserById(Long id) {
        return checkTeamUserMapper.deleteCheckTeamUserById(id);
    }

    /**
     * 根据分组ID查询关联的用户列表
     *
     * @param teamId 分组ID
     * @param type 路线类型
     * @return 用户关联集合
     */
    @Override
    public List<CheckTeamUser> selectUsersByTeamId(Long teamId, Integer type) {
        return checkTeamUserMapper.selectUsersByTeamId(teamId, type);
    }

    /**
     * 根据分组ID查询关联的用户列表（包含用户详细信息）
     *
     * @param teamId 分组ID
     * @param type 路线类型
     * @return 用户关联集合（包含用户详细信息）
     */
    @Override
    public List<CheckTeamUser> selectCheckTeamUserListByTeamId(Long teamId, Integer type) {
        return checkTeamUserMapper.selectUsersByTeamId(teamId, type);
    }

    /**
     * 根据用户ID查询关联的分组列表
     *
     * @param userId 用户ID
     * @param type 路线类型
     * @return 分组关联集合
     */
    @Override
    public List<CheckTeamUser> selectTeamsByUserId(Long userId, Integer type) {
        return checkTeamUserMapper.selectTeamsByUserId(userId, type);
    }

    /**
     * 检查用户分组关联是否存在
     *
     * @param teamId 分组ID
     * @param userId 用户ID
     * @return 是否存在
     */
    @Override
    public boolean checkTeamUserExists(Long teamId, Long userId) {
        CheckTeamUser relation = checkTeamUserMapper.checkTeamUserExists(teamId, userId);
        return relation != null;
    }

    /**
     * 批量新增用户分组关联
     *
     * @param teamId 分组ID
     * @param userIds 用户ID列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchAddUsersToTeam(Long teamId, Long[] userIds) {
        if (userIds == null || userIds.length == 0) {
            return 0;
        }
        
        List<CheckTeamUser> relationList = new ArrayList<>();
        String createBy = SecurityUtils.getUsername();
        
        for (Long userId : userIds) {
            // 检查关联是否已存在
            if (!checkTeamUserExists(teamId, userId)) {
                CheckTeamUser relation = new CheckTeamUser();
                relation.setTeamId(teamId);
                relation.setUserId(userId);
                relation.setSortNum(getNextSortNumByTeamId(teamId));
                relation.setCreateBy(createBy);
                relation.setCreateTime(DateUtils.getNowDate());
                relationList.add(relation);
            }
        }
        
        if (relationList.isEmpty()) {
            return 0;
        }
        
        return checkTeamUserMapper.batchInsertCheckTeamUser(relationList);
    }

    /**
     * 批量新增分组用户关联
     *
     * @param userId 用户ID
     * @param teamIds 分组ID列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchAddUserToTeams(Long userId, Long[] teamIds) {
        if (teamIds == null || teamIds.length == 0) {
            return 0;
        }
        
        List<CheckTeamUser> relationList = new ArrayList<>();
        String createBy = SecurityUtils.getUsername();
        
        for (Long teamId : teamIds) {
            // 检查关联是否已存在
            if (!checkTeamUserExists(teamId, userId)) {
                CheckTeamUser relation = new CheckTeamUser();
                relation.setTeamId(teamId);
                relation.setUserId(userId);
                relation.setSortNum(getNextSortNumByTeamId(teamId));
                relation.setCreateBy(createBy);
                relation.setCreateTime(DateUtils.getNowDate());
                relationList.add(relation);
            }
        }
        
        if (relationList.isEmpty()) {
            return 0;
        }
        
        return checkTeamUserMapper.batchInsertCheckTeamUser(relationList);
    }

    /**
     * 移除用户分组关联
     *
     * @param teamId 分组ID
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public int removeUserFromTeam(Long teamId, Long userId) {
        CheckTeamUser relation = checkTeamUserMapper.checkTeamUserExists(teamId, userId);
        if (relation != null) {
            return checkTeamUserMapper.deleteCheckTeamUserById(relation.getId());
        }
        return 0;
    }

    /**
     * 批量更新排序
     *
     * @param checkTeamUserList 关联列表
     * @return 结果
     */
    @Override
    public int batchUpdateSort(List<CheckTeamUser> checkTeamUserList) {
        if (checkTeamUserList == null || checkTeamUserList.isEmpty()) {
            return 0;
        }
        return checkTeamUserMapper.batchUpdateSort(checkTeamUserList);
    }

    /**
     * 获取分组内下一个排序号
     *
     * @param teamId 分组ID
     * @return 下一个排序号
     */
    @Override
    public Integer getNextSortNumByTeamId(Long teamId) {
        Integer nextSortNum = checkTeamUserMapper.getNextSortNumByTeamId(teamId);
        return nextSortNum != null ? nextSortNum : 1;
    }

    /**
     * 导出检测分组用户关联数据
     *
     * @param response 响应对象
     * @param checkTeamUser 查询条件
     */
    @Override
    public void exportCheckTeamUser(HttpServletResponse response, CheckTeamUser checkTeamUser) {
        List<CheckTeamUser> list = checkTeamUserMapper.selectCheckTeamUserList(checkTeamUser);
        ExcelUtil<CheckTeamUser> util = new ExcelUtil<CheckTeamUser>(CheckTeamUser.class);
        util.exportExcel(response, list, "检测分组用户关联数据");
    }

    /**
     * 根据分组ID查询关联的CheckUser对象列表
     *
     * @param teamId 分组ID
     * @param type 路线类型
     * @return CheckUser对象列表
     */
    @Override
    public List<CheckUser> selectCheckUsersByTeamId(Long teamId, Integer type) {
        return checkTeamUserMapper.selectCheckUsersByTeamId(teamId, type);
    }

    /**
     * 根据分组ID和职位查询关联的CheckUser对象列表
     *
     * @param teamId 分组ID
     * @param position 职位
     * @param type 路线类型
     * @return CheckUser对象列表
     */
    @Override
    public List<CheckUser> selectCheckUsersByTeamIdAndPosition(Long teamId, String position, Integer type) {
        return checkTeamUserMapper.selectCheckUsersByTeamIdAndPosition(teamId, position, type);
    }
} 