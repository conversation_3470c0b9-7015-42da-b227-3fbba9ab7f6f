package com.tunnel.common.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadFactory;

/**
 * <AUTHOR>
 * @date ：Created in 2022/5/24 15:31
 */
@Configuration
@Slf4j
public class uploadPicThreadPoolConfig {

    /**
     * 获得Java虚拟机可用的处理器个数.
     */
    @Value("${listSkuInfoAndPriceThread.coreNum:20}")
    private String coreNum;
    @Value("${listSkuInfoAndPriceThread.maxNum:50}")
    private String maxNum;
    @Value("${listSkuInfoAndPriceThread.queue:1000}")
    private String queue;

    private String threadPrefix = "上传文件详情线程-";
    /**
     * 下载数据线程池初始化.
     *
     * @return 下载数据线程池
     */
    @Bean("uploadPicThread")
    public ThreadPoolTaskExecutor uploadPicThreadThread() {
        log.info("coreNum:【{}】,maxNum:【{}】,queue:【{}】,threadPrefix:【{}】",
                coreNum,maxNum,queue,threadPrefix);

        final ThreadFactory threadFactory = new ThreadFactoryBuilder()
                // -%d不要少
                .setNameFormat(threadPrefix + "%d")
                .setDaemon(true)
                .build();

        final ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        taskExecutor.setCorePoolSize(Integer.parseInt(coreNum));
        taskExecutor.setMaxPoolSize(Integer.parseInt(maxNum));
        taskExecutor.setQueueCapacity(Integer.parseInt(queue));
        taskExecutor.setKeepAliveSeconds(5);
        taskExecutor.setThreadFactory(threadFactory);
        taskExecutor.setRejectedExecutionHandler((r, executor) -> {
            log.error("线程超出线程池上限被拒绝执行...");
        });
        return taskExecutor;
    }
}
