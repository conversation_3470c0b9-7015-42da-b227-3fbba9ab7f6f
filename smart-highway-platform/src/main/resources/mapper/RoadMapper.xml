<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.RoadMapper">

    <resultMap type="com.tunnel.domain.Road" id="RoadResult">
        <result property="id" column="id"/>
        <result property="year" column="year"/>
        <result property="projectName" column="project_name"/>
        <result property="companyName" column="company_name"/>
        <result property="roadCode" column="road_code"/>
        <result property="roadName" column="road_name"/>
        <result property="startCode" column="start_code"/>
        <result property="endCode" column="end_code"/>
        <result property="mileage" column="mileage"/>
        <result property="remark" column="remark"/>
        <result property="reportNo" column="report_no"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="creator" column="creator"/>
        <result property="modifier" column="modifier"/>
        <result property="isAvailable" column="is_available"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="versionNo" column="version_no"/>
        <result property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="type" column="type"/>
    </resultMap>

    <sql id="selectRoadVo">
        select id, year, project_name, company_name, road_code, road_name, start_code, end_code, mileage, remark, report_no,
               create_time, update_time, creator, modifier, is_available, is_deleted, version_no, user_id, dept_id, type
        from sc_road
    </sql>

    <select id="selectRoadList" parameterType="com.tunnel.domain.Road" resultMap="RoadResult">
        <include refid="selectRoadVo"/>
        <where>
            is_deleted = 0
            <if test="year != null">and year = #{year}</if>
            <if test="projectName != null">and project_name = #{projectName}</if>
            <if test="companyName != null and companyName != ''">and company_name like concat('%', #{companyName}, '%')</if>
            <if test="roadCode != null and roadCode != ''">and road_code like concat('%', #{roadCode}, '%')</if>
            <if test="roadName != null and roadName != ''">and road_name like concat('%', #{roadName}, '%')</if>
            <if test="startCode != null and startCode != ''">and start_code = #{startCode}</if>
            <if test="endCode != null and endCode != ''">and end_code = #{endCode}</if>
            <if test="userId != null">and user_id = #{userId}</if>
            <if test="deptId != null">and dept_id = #{deptId}</if>
            <if test="isAvailable != null">and is_available = #{isAvailable}</if>
            <if test="type != null">and type = #{type}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectRoadById" parameterType="Long" resultMap="RoadResult">
        <include refid="selectRoadVo"/>
        where id = #{id} and is_deleted = 0
    </select>

    <select id="selectRoadByCode" parameterType="String" resultMap="RoadResult">
        <include refid="selectRoadVo"/>
        where road_code = #{roadCode} and is_deleted = 0
    </select>

    <select id="selectRoadListByUserId" parameterType="Long" resultMap="RoadResult">
        <include refid="selectRoadVo"/>
        where user_id = #{userId} and is_deleted = 0 and is_available = 1
        order by create_time desc
    </select>

    <select id="selectRoadListByDeptId" parameterType="Long" resultMap="RoadResult">
        <include refid="selectRoadVo"/>
        where dept_id = #{deptId} and is_deleted = 0 and is_available = 1
        order by create_time desc
    </select>

    <select id="checkRoadCodeUnique" parameterType="com.tunnel.domain.Road" resultMap="RoadResult">
        <include refid="selectRoadVo"/>
        where road_code = #{roadCode} and is_deleted = 0
        <if test="id != null">and id != #{id}</if>
        limit 1
    </select>

    <insert id="insertRoad" parameterType="com.tunnel.domain.Road" useGeneratedKeys="true" keyProperty="id">
        insert into sc_road
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="year != null">year,</if>
            <if test="projectName != null">project_name,</if>
            <if test="companyName != null and companyName != ''">company_name,</if>
            <if test="roadCode != null and roadCode != ''">road_code,</if>
            <if test="roadName != null and roadName != ''">road_name,</if>
            <if test="startCode != null and startCode != ''">start_code,</if>
            <if test="endCode != null and endCode != ''">end_code,</if>
            <if test="mileage != null">mileage,</if>
            <if test="remark != null">remark,</if>
            <if test="reportNo != null and reportNo != ''">report_no,</if>
            <if test="createTime != null">create_time,</if>
            <if test="creator != null">creator,</if>
            <if test="isAvailable != null">is_available,</if>
            <if test="isDeleted != null">is_deleted,</if>
            <if test="versionNo != null">version_no,</if>
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="type != null">type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="year != null">#{year},</if>
            <if test="projectName != null">#{projectName},</if>
            <if test="companyName != null and companyName != ''">#{companyName},</if>
            <if test="roadCode != null and roadCode != ''">#{roadCode},</if>
            <if test="roadName != null and roadName != ''">#{roadName},</if>
            <if test="startCode != null and startCode != ''">#{startCode},</if>
            <if test="endCode != null and endCode != ''">#{endCode},</if>
            <if test="mileage != null">#{mileage},</if>
            <if test="remark != null">#{remark},</if>
            <if test="reportNo != null and reportNo != ''">#{reportNo},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="isAvailable != null">#{isAvailable},</if>
            <if test="isDeleted != null">#{isDeleted},</if>
            <if test="versionNo != null">#{versionNo},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="type != null">#{type},</if>
        </trim>
    </insert>

    <update id="updateRoad" parameterType="com.tunnel.domain.Road">
        update sc_road
        <trim prefix="SET" suffixOverrides=",">
            <if test="year != null">year = #{year},</if>
            <if test="projectName != null">project_name = #{projectName},</if>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="roadCode != null and roadCode != ''">road_code = #{roadCode},</if>
            <if test="roadName != null and roadName != ''">road_name = #{roadName},</if>
            <if test="startCode != null and startCode != ''">start_code = #{startCode},</if>
            <if test="endCode != null and endCode != ''">end_code = #{endCode},</if>
            <if test="mileage != null">mileage = #{mileage},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="reportNo != null">report_no = #{reportNo},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
            <if test="isAvailable != null">is_available = #{isAvailable},</if>
            <if test="versionNo != null">version_no = version_no + 1,</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="type != null">type = #{type},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRoadById" parameterType="Long">
        update sc_road set is_deleted = 1 where id = #{id}
    </delete>

    <delete id="deleteRoadByIds" parameterType="Long">
        update sc_road set is_deleted = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="countRoad" parameterType="com.tunnel.domain.Road" resultType="int">
        select count(*) from sc_road
        <where>
            is_deleted = 0
            <if test="year != null">and year = #{year}</if>
            <if test="projectName != null">and project_name = #{projectName}</if>
            <if test="companyName != null and companyName != ''">and company_name like concat('%', #{companyName}, '%')</if>
            <if test="roadCode != null and roadCode != ''">and road_code like concat('%', #{roadCode}, '%')</if>
            <if test="roadName != null and roadName != ''">and road_name like concat('%', #{roadName}, '%')</if>
            <if test="startCode != null and startCode != ''">and start_code = #{startCode}</if>
            <if test="endCode != null and endCode != ''">and end_code = #{endCode}</if>
            <if test="userId != null">and user_id = #{userId}</if>
            <if test="deptId != null">and dept_id = #{deptId}</if>
            <if test="isAvailable != null">and is_available = #{isAvailable}</if>
            <if test="type != null">and type = #{type}</if>
        </where>
    </select>
</mapper>