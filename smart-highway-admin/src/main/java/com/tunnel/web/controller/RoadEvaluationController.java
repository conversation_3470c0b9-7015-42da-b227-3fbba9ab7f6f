package com.tunnel.web.controller;

import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.RoadEvaluation;
import com.tunnel.service.RoadEvaluationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.FileCopyUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.List;

/**
 * 道路技术状况评定信息Controller
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
@RestController
@RequestMapping("/smart/roadEvaluation")
@Api(tags = "道路技术状况评定信息管理")
public class RoadEvaluationController extends BaseController {
    @Autowired
    private RoadEvaluationService roadEvaluationService;

    /**
     * 查询道路技术状况评定信息列表
     */
//    @PreAuthorize("@ss.hasPermi('smart:roadEvaluation:list')")
    @GetMapping("/list")
    @ApiOperation(value = "获取道路技术状况评定信息列表", notes = "获取道路技术状况评定信息数据列表")
    public TableDataInfo list(RoadEvaluation roadEvaluation) {
        startPage();
        List<RoadEvaluation> list = roadEvaluationService.selectRoadEvaluationList(roadEvaluation);
        return getDataTable(list);
    }

    /**
     * 导出道路技术状况评定信息列表
     */
//    @PreAuthorize("@ss.hasPermi('smart:roadEvaluation:export')")
    @Log(title = "道路技术状况评定信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出道路技术状况评定信息", notes = "导出道路技术状况评定信息数据")
    public void export(HttpServletResponse response, RoadEvaluation roadEvaluation) {
        roadEvaluationService.exportRoadEvaluation(response, roadEvaluation);
    }

    /**
     * 获取道路技术状况评定信息详细信息
     */
//    @PreAuthorize("@ss.hasPermi('smart:roadEvaluation:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取道路技术状况评定信息详细信息", notes = "根据ID获取道路技术状况评定信息详情")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true, dataType = "Long", paramType = "path")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(roadEvaluationService.selectRoadEvaluationById(id));
    }

    /**
     * 新增道路技术状况评定信息
     */
//    @PreAuthorize("@ss.hasPermi('smart:roadEvaluation:add')")
    @Log(title = "道路技术状况评定信息", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增道路技术状况评定信息", notes = "新增道路技术状况评定信息")
    public AjaxResult add(@Validated @RequestBody RoadEvaluation roadEvaluation) {
        return toAjax(roadEvaluationService.insertRoadEvaluation(roadEvaluation));
    }

    /**
     * 修改道路技术状况评定信息
     */
//    @PreAuthorize("@ss.hasPermi('smart:roadEvaluation:edit')")
    @Log(title = "道路技术状况评定信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改道路技术状况评定信息", notes = "修改道路技术状况评定信息")
    public AjaxResult edit(@Validated @RequestBody RoadEvaluation roadEvaluation) {
        return toAjax(roadEvaluationService.updateRoadEvaluation(roadEvaluation));
    }

    /**
     * 删除道路技术状况评定信息
     */
//    @PreAuthorize("@ss.hasPermi('smart:roadEvaluation:remove')")
    @Log(title = "道路技术状况评定信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除道路技术状况评定信息", notes = "批量删除道路技术状况评定信息")
    @ApiImplicitParam(name = "ids", value = "主键ID数组", required = true, dataType = "Long[]", paramType = "path")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(roadEvaluationService.deleteRoadEvaluationByIds(ids));
    }

    /**
     * 校验路线编号唯一性
     */
    @GetMapping("/checkRoadCodeUnique")
    @ApiOperation(value = "校验路线编号唯一性", notes = "校验路线编号+起点桩号+终点桩号+检测方向的组合是否唯一")
    public AjaxResult checkRoadCodeUnique(@RequestParam String roadCode, 
                                          @RequestParam String startCode, 
                                          @RequestParam String endCode, 
                                          @RequestParam Integer direction,
                                          @RequestParam(required = false) Long id) {
        RoadEvaluation roadEvaluation = new RoadEvaluation();
        roadEvaluation.setRoadCode(roadCode);
        roadEvaluation.setStartCode(startCode);
        roadEvaluation.setEndCode(endCode);
        roadEvaluation.setDirection(direction);
        roadEvaluation.setId(id);
        return success(roadEvaluationService.checkRoadCodeUnique(roadEvaluation));
    }

    /**
     * 下载导入模板
     */
    @PostMapping("/importTemplate")
    @ApiOperation(value = "下载导入模板", notes = "下载道路技术状况评定信息导入模板")
    public void importTemplate(HttpServletResponse response) throws IOException {
        ClassPathResource resource = new ClassPathResource("static/roadEvaluation-template.xlsx");
        InputStream inputStream = resource.getInputStream();
        String fileName = "道路技术状况评定信息导入模板.xlsx";
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
        FileCopyUtils.copy(inputStream, response.getOutputStream());
    }

    /**
     * 导入道路技术状况评定信息数据
     */
    @Log(title = "道路技术状况评定信息", businessType = BusinessType.IMPORT)
//    @PreAuthorize("@ss.hasPermi('smart:roadEvaluation:import')")
    @PostMapping("/importData")
    @ApiOperation(value = "导入道路技术状况评定信息数据", notes = "通过Excel文件导入道路技术状况评定信息数据")
    public AjaxResult importData(@RequestParam("file") MultipartFile file, 
                                @RequestParam("updateSupport") boolean updateSupport,
                                @RequestParam("projectId") Long projectId) throws Exception {
        String operName = getUsername();
        String message = roadEvaluationService.importRoadEvaluation(file, updateSupport, projectId);
        return success(message);
    }

    /**
     * 导出Word报告
     */
    @Log(title = "道路技术状况评定信息", businessType = BusinessType.EXPORT)
//    @PreAuthorize("@ss.hasPermi('smart:roadEvaluation:export')")
    @PostMapping("/exportWordReport")
    @ApiOperation(value = "导出Word报告", notes = "导出道路技术状况评定信息Word报告")
    public void exportWordReport(HttpServletResponse response, @RequestBody java.util.Map<String, Object> params) throws Exception {
        roadEvaluationService.exportWordReport(response, params);
    }
} 