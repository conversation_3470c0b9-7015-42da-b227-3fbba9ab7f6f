<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.CheckTeamMapper">

    <resultMap type="com.tunnel.domain.CheckTeam" id="CheckTeamResult">
        <result property="id" column="id"/>
        <result property="teamName" column="team_name"/>
        <result property="description" column="description"/>
        <result property="type" column="type"/>
        <result property="creator" column="creator"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectCheckTeamVo">
        select id, team_name, description, type, creator, create_time, update_time
        from sc_check_team
    </sql>

    <select id="selectCheckTeamList" parameterType="com.tunnel.domain.CheckTeam" resultMap="CheckTeamResult">
        <include refid="selectCheckTeamVo"/>
        <where>
            <if test="teamName != null and teamName != ''">and team_name like concat('%', #{teamName}, '%')</if>
            <if test="description != null and description != ''">and description like concat('%', #{description}, '%')</if>
            <if test="type != null">and type = #{type}</if>
        </where>
        order by create_time asc
    </select>

    <select id="selectCheckTeamById" parameterType="Long" resultMap="CheckTeamResult">
        <include refid="selectCheckTeamVo"/>
        where id = #{id}
    </select>

    <select id="checkTeamNameUnique" parameterType="com.tunnel.domain.CheckTeam" resultMap="CheckTeamResult">
        <include refid="selectCheckTeamVo"/>
        where team_name = #{teamName}
        <if test="id != null">and id != #{id}</if>
        <if test="type != null">and type = #{type}</if>
        limit 1
    </select>

    <insert id="insertCheckTeam" parameterType="com.tunnel.domain.CheckTeam" useGeneratedKeys="true" keyProperty="id">
        insert into sc_check_team
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teamName != null and teamName != ''">team_name,</if>
            <if test="description != null">description,</if>
            <if test="type != null">type,</if>
            <if test="creator != null">creator,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teamName != null and teamName != ''">#{teamName},</if>
            <if test="description != null">#{description},</if>
            <if test="type != null">#{type},</if>
            <if test="creator != null">#{creator},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateCheckTeam" parameterType="com.tunnel.domain.CheckTeam">
        update sc_check_team
        <trim prefix="SET" suffixOverrides=",">
            <if test="teamName != null and teamName != ''">team_name = #{teamName},</if>
            <if test="description != null">description = #{description},</if>
            <if test="type != null">type = #{type},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCheckTeamById" parameterType="Long">
        delete from sc_check_team where id = #{id}
    </delete>

    <delete id="deleteCheckTeamByIds" parameterType="Long">
        delete from sc_check_team where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper> 