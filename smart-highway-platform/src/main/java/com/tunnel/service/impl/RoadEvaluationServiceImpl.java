package com.tunnel.service.impl;

import com.tunnel.common.exception.ServiceException;
import com.tunnel.common.utils.DateUtils;
import com.tunnel.common.utils.SecurityUtils;
import com.tunnel.common.utils.StringUtils;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.common.utils.bean.BeanValidators;
import com.tunnel.common.utils.WordDocumentUtils;
import com.tunnel.domain.RoadEvaluation;
import com.tunnel.domain.CheckUser;
import com.tunnel.mapper.RoadEvaluationMapper;
import com.tunnel.mapper.CheckTeamUserMapper;
import com.tunnel.service.RoadEvaluationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xwpf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.apache.poi.openxml4j.util.ZipSecureFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Validator;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 道路技术状况评定信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
@Service
@Slf4j
public class RoadEvaluationServiceImpl implements RoadEvaluationService {
    @Autowired
    private RoadEvaluationMapper roadEvaluationMapper;

    @Autowired
    private CheckTeamUserMapper checkTeamUserMapper;

    @Autowired
    private Validator validator;

    /**
     * 查询道路技术状况评定信息
     *
     * @param id 道路技术状况评定信息主键
     * @return 道路技术状况评定信息
     */
    @Override
    public RoadEvaluation selectRoadEvaluationById(Long id) {
        return roadEvaluationMapper.selectRoadEvaluationById(id);
    }

    /**
     * 查询道路技术状况评定信息列表
     *
     * @param roadEvaluation 道路技术状况评定信息
     * @return 道路技术状况评定信息
     */
    @Override
    public List<RoadEvaluation> selectRoadEvaluationList(RoadEvaluation roadEvaluation) {
        return roadEvaluationMapper.selectRoadEvaluationList(roadEvaluation);
    }

    /**
     * 新增道路技术状况评定信息
     *
     * @param roadEvaluation 道路技术状况评定信息
     * @return 结果
     */
    @Override
    public int insertRoadEvaluation(RoadEvaluation roadEvaluation) {
        // 校验路线编号唯一性
        if (!checkRoadCodeUnique(roadEvaluation)) {
            throw new ServiceException("新增道路评定信息失败，路线编号+起点桩号+终点桩号+检测方向的组合已存在");
        }
        
        roadEvaluation.setCreateTime(DateUtils.getNowDate());
        if (SecurityUtils.getUserId() != null) {
            roadEvaluation.setCreator(SecurityUtils.getUserId());
        }
        return roadEvaluationMapper.insertRoadEvaluation(roadEvaluation);
    }

    /**
     * 修改道路技术状况评定信息
     *
     * @param roadEvaluation 道路技术状况评定信息
     * @return 结果
     */
    @Override
    public int updateRoadEvaluation(RoadEvaluation roadEvaluation) {
        // 校验路线编号唯一性
        if (!checkRoadCodeUnique(roadEvaluation)) {
            throw new ServiceException("修改道路评定信息失败，路线编号+起点桩号+终点桩号+检测方向的组合已存在");
        }
        
        roadEvaluation.setUpdateTime(DateUtils.getNowDate());
        if (SecurityUtils.getUserId() != null) {
            roadEvaluation.setModifier(SecurityUtils.getUserId());
        }
        return roadEvaluationMapper.updateRoadEvaluation(roadEvaluation);
    }

    /**
     * 批量删除道路技术状况评定信息
     *
     * @param ids 需要删除的道路技术状况评定信息主键
     * @return 结果
     */
    @Override
    public int deleteRoadEvaluationByIds(Long[] ids) {
        return roadEvaluationMapper.deleteRoadEvaluationByIds(ids);
    }

    /**
     * 删除道路技术状况评定信息信息
     *
     * @param id 道路技术状况评定信息主键
     * @return 结果
     */
    @Override
    public int deleteRoadEvaluationById(Long id) {
        return roadEvaluationMapper.deleteRoadEvaluationById(id);
    }

    /**
     * 校验路线编号是否唯一
     *
     * @param roadEvaluation 道路技术状况评定信息
     * @return 结果
     */
    @Override
    public boolean checkRoadCodeUnique(RoadEvaluation roadEvaluation) {
        RoadEvaluation info = roadEvaluationMapper.checkRoadCodeUnique(roadEvaluation);
        return info == null;
    }

    /**
     * 导出道路技术状况评定信息列表
     *
     * @param response HTTP响应
     * @param roadEvaluation 查询条件
     */
    @Override
    public void exportRoadEvaluation(HttpServletResponse response, RoadEvaluation roadEvaluation) {
        try {
            List<RoadEvaluation> list = selectRoadEvaluationList(roadEvaluation);
            
            // 按照 startCode 和 endCode 排序
            list.sort((o1, o2) -> {
                // 先按路线编号排序
                int roadCodeCompare = 0;
                if (o1.getRoadCode() != null && o2.getRoadCode() != null) {
                    roadCodeCompare = o1.getRoadCode().compareTo(o2.getRoadCode());
                }
                if (roadCodeCompare != 0) {
                    return roadCodeCompare;
                }
                
                // 再按起点桩号排序
                int startCodeCompare = 0;
                if (o1.getStartCode() != null && o2.getStartCode() != null) {
                    try {
                        // 尝试按数值排序
                        Double start1 = Double.parseDouble(o1.getStartCode().replaceAll("[^0-9.]", ""));
                        Double start2 = Double.parseDouble(o2.getStartCode().replaceAll("[^0-9.]", ""));
                        startCodeCompare = start1.compareTo(start2);
                    } catch (NumberFormatException e) {
                        // 如果不是数值，按字符串排序
                        startCodeCompare = o1.getStartCode().compareTo(o2.getStartCode());
                    }
                }
                if (startCodeCompare != 0) {
                    return startCodeCompare;
                }
                
                // 最后按终点桩号排序
                if (o1.getEndCode() != null && o2.getEndCode() != null) {
                    try {
                        // 尝试按数值排序
                        Double end1 = Double.parseDouble(o1.getEndCode().replaceAll("[^0-9.]", ""));
                        Double end2 = Double.parseDouble(o2.getEndCode().replaceAll("[^0-9.]", ""));
                        return end1.compareTo(end2);
                    } catch (NumberFormatException e) {
                        // 如果不是数值，按字符串排序
                        return o1.getEndCode().compareTo(o2.getEndCode());
                    }
                }
                return 0;
            });
            
            // 加载模板文件
            ClassPathResource templateResource = new ClassPathResource("static/roadEvaluation-template.xlsx");
            InputStream templateStream = templateResource.getInputStream();
            
            // 创建工作簿
            Workbook workbook = WorkbookFactory.create(templateStream);
            Sheet sheet = workbook.getSheetAt(0);
            
            // 创建边框样式
            CellStyle borderStyle = workbook.createCellStyle();
            borderStyle.setBorderTop(BorderStyle.THIN);
            borderStyle.setBorderBottom(BorderStyle.THIN);
            borderStyle.setBorderLeft(BorderStyle.THIN);
            borderStyle.setBorderRight(BorderStyle.THIN);
            borderStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
            borderStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
            borderStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
            borderStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
            borderStyle.setAlignment(HorizontalAlignment.CENTER);
            borderStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            
            // 创建数值型边框样式
            CellStyle numberBorderStyle = workbook.createCellStyle();
            numberBorderStyle.cloneStyleFrom(borderStyle);
            DataFormat format = workbook.createDataFormat();
            numberBorderStyle.setDataFormat(format.getFormat("0.00"));
            
            // 从第7行开始填充数据（索引为6）
            int startRowIndex = 6;
            
            for (int i = 0; i < list.size(); i++) {
                RoadEvaluation item = list.get(i);
                Row row = sheet.getRow(startRowIndex + i);
                if (row == null) {
                    row = sheet.createRow(startRowIndex + i);
                }
                
                // 按照与导入相同的列顺序填充数据，并设置边框
                // A列(0): 路线编号
                setCellValueWithBorder(row, 0, item.getRoadCode(), borderStyle);
                
                // B列(1): 行政区划代码
                setCellValueWithBorder(row, 1, item.getDivisionCode(), borderStyle);
                
                // C列(2): 路线名称
                setCellValueWithBorder(row, 2, item.getRoadName(), borderStyle);
                
                // D列(3): 起点桩号
                setCellValueWithBorder(row, 3, item.getStartCode(), borderStyle);
                
                // E列(4): 终点桩号
                setCellValueWithBorder(row, 4, item.getEndCode(), borderStyle);
                
                // F列(5): 检测方向
                String directionText = "";
                if (item.getDirection() != null) {
                    if (item.getDirection() == 1) {
                        directionText = "上行";
                    } else if (item.getDirection() == 2) {
                        directionText = "下行";
                    } else {
                        directionText = String.valueOf(item.getDirection());
                    }
                }
                setCellValueWithBorder(row, 5, directionText, borderStyle);
                
                // G列(6): 技术等级
                setCellValueWithBorder(row, 6, item.getLevel(), borderStyle);
                
                // H列(7): 路面类型
                setCellValueWithBorder(row, 7, item.getRoadType(), borderStyle);
                
                // I列(8): 检测方式 - 删除此字段
                // setCellValueWithBorder(row, 8, item.getDetectionMethod(), borderStyle);
                
                // J列(8): 路段长度
                setCellValueWithBorder(row, 8, item.getRoadLength(), numberBorderStyle);
                
                // K列(9): 路段宽度
                setCellValueWithBorder(row, 9, item.getRoadWidth(), numberBorderStyle);
                
                // L列(10): MQI
                setCellValueWithBorder(row, 10, item.getMqi(), numberBorderStyle);
                
                // M列(11): 路面PQI
                setCellValueWithBorder(row, 11, item.getPqi(), numberBorderStyle);
                
                // N列(12): PCI
                setCellValueWithBorder(row, 12, item.getPci(), numberBorderStyle);
                
                // O列(13): RQI
                setCellValueWithBorder(row, 13, item.getRqi(), numberBorderStyle);
                
                // P列(14): RDI
                setCellValueWithBorder(row, 14, item.getRdi(), numberBorderStyle);
                
                // Q列(15): PWI
                setCellValueWithBorder(row, 15, item.getPwi(), numberBorderStyle);
                
                // R列(16): 预留
                setCellValueWithBorder(row, 16, "", borderStyle);
                
                // S列(17): SRI
                setCellValueWithBorder(row, 17, item.getSri(), numberBorderStyle);
                
                // T列(18): PSSI
                setCellValueWithBorder(row, 18, item.getPssi(), numberBorderStyle);
                
                // U列(19): 路基SCI
                setCellValueWithBorder(row, 19, item.getSci(), numberBorderStyle);
                
                // V列(20): 桥梁构造物BCI
                setCellValueWithBorder(row, 20, item.getBci(), numberBorderStyle);
                
                // W列(21): 沿线设施TCI
                setCellValueWithBorder(row, 21, item.getTci(), numberBorderStyle);
                
                // X列(22): 检测方式
                setCellValueWithBorder(row, 22, item.getDetectionMethod(), borderStyle);
                
                // Y列(23): 评定标准
                setCellValueWithBorder(row, 23, item.getEvaluationStandard(), borderStyle);
                
                // Z列(24): 评定年度
                setCellValueWithBorder(row, 24, item.getEvaluationYear(), borderStyle);
                
                // AA列(25): 备注
                setCellValueWithBorder(row, 25, item.getRemark(), borderStyle);
            }
            
            // 设置响应头
            String fileName = "道路技术状况评定信息数据_" + DateUtils.dateTimeNow() + ".xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));
            
            // 输出到响应流
            workbook.write(response.getOutputStream());
            workbook.close();
            templateStream.close();
            
        } catch (Exception e) {
            log.error("导出道路技术状况评定信息失败", e);
            throw new ServiceException("导出道路技术状况评定信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 设置单元格值并添加边框的工具方法
     */
    private void setCellValueWithBorder(Row row, int columnIndex, Object value, CellStyle style) {
        Cell cell = row.getCell(columnIndex);
        if (cell == null) {
            cell = row.createCell(columnIndex);
        }
        
        // 设置样式
        cell.setCellStyle(style);
        
        if (value == null) {
            cell.setCellValue("");
            return;
        }
        
        if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof Integer) {
            cell.setCellValue((Integer) value);
        } else if (value instanceof BigDecimal) {
            cell.setCellValue(((BigDecimal) value).doubleValue());
        } else if (value instanceof Double) {
            cell.setCellValue((Double) value);
        } else if (value instanceof Date) {
            cell.setCellValue((Date) value);
        } else {
            cell.setCellValue(value.toString());
        }
    }
    
    /**
     * 设置单元格值的工具方法（保留原方法用于兼容）
     */
    private void setCellValue(Row row, int columnIndex, Object value) {
        Cell cell = row.getCell(columnIndex);
        if (cell == null) {
            cell = row.createCell(columnIndex);
        }
        
        if (value == null) {
            cell.setCellValue("");
            return;
        }
        
        if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof Integer) {
            cell.setCellValue((Integer) value);
        } else if (value instanceof BigDecimal) {
            cell.setCellValue(((BigDecimal) value).doubleValue());
        } else if (value instanceof Double) {
            cell.setCellValue((Double) value);
        } else if (value instanceof Date) {
            cell.setCellValue((Date) value);
        } else {
            cell.setCellValue(value.toString());
        }
    }

    /**
     * 导入道路技术状况评定信息数据
     *
     * @param file Excel文件
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    @Transactional
    public String importRoadEvaluation(MultipartFile file, boolean updateSupport, Long projectId) throws Exception {
        // 使用自定义的Excel导入方法，从第7行开始读取
        List<RoadEvaluation> roadEvaluationList = importRoadEvaluationFromExcel(file);
        
        // 为所有数据设置项目ID
        for (RoadEvaluation roadEvaluation : roadEvaluationList) {
            roadEvaluation.setProjectId(projectId);
        }
        
        String operName = SecurityUtils.getUsername();
        String message = importRoadEvaluation(roadEvaluationList, updateSupport, operName);
        return message;
    }

    /**
     * 自定义Excel导入方法，从第7行开始读取数据
     * 
     * @param file Excel文件
     * @return 道路技术状况评定信息列表
     * @throws Exception
     */
    private List<RoadEvaluation> importRoadEvaluationFromExcel(MultipartFile file) throws Exception {
        List<RoadEvaluation> roadEvaluationList = new ArrayList<>();
        
        try (Workbook workbook = WorkbookFactory.create(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);
            int lastRowNum = sheet.getLastRowNum();
            
            // 从第7行开始读取数据（索引为6）
            for (int rowIndex = 6; rowIndex <= lastRowNum; rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row == null || isRowEmpty(row)) {
                    continue;
                }
                
                RoadEvaluation roadEvaluation = new RoadEvaluation();
                
                try {
                    // 按照SQL字段顺序映射Excel列，根据图片中的列顺序
                    // A列(0): 路线编号
                    roadEvaluation.setRoadCode(getCellStringValue(row, 0));
                    
                    // B列(1): 行政区划代码
                    roadEvaluation.setDivisionCode(getCellStringValue(row, 1));
                    
                    // C列(2): 路线名称
                    roadEvaluation.setRoadName(getCellStringValue(row, 2));
                    
                    // D列(3): 起点桩号
                    roadEvaluation.setStartCode(getCellStringValue(row, 3));
                    
                    // E列(4): 终点桩号
                    roadEvaluation.setEndCode(getCellStringValue(row, 4));
                    
                    // F列(5): 检测方向
                    String directionStr = getCellStringValue(row, 5);
                    if (StringUtils.isNotEmpty(directionStr)) {
                        if ("上行".equals(directionStr)) {
                            roadEvaluation.setDirection(1);
                        } else if ("下行".equals(directionStr)) {
                            roadEvaluation.setDirection(2);
                        } else {
                            // 尝试解析为数字
                            try {
                                roadEvaluation.setDirection(Integer.parseInt(directionStr));
                            } catch (NumberFormatException e) {
                                log.warn("无法解析检测方向: {}, 行号: {}", directionStr, rowIndex + 1);
                            }
                        }
                    }
                    
                    // G列(6): 技术等级
                    roadEvaluation.setLevel(getCellStringValue(row, 6));
                    
                    // H列(7): 路面类型
                    roadEvaluation.setRoadType(getCellStringValue(row, 7));
                    
                    // J列(8): 路段长度
                    roadEvaluation.setRoadLength(getCellBigDecimalValue(row, 8));
                    
                    // K列(9): 路段宽度
                    roadEvaluation.setRoadWidth(getCellBigDecimalValue(row, 9));
                    
                    // L列(10): MQI
                    roadEvaluation.setMqi(getCellBigDecimalValue(row, 10));
                    
                    // M列(11): 路面PQI
                    roadEvaluation.setPqi(getCellBigDecimalValue(row, 11));
                    
                    // N列(12): PCI
                    roadEvaluation.setPci(getCellBigDecimalValue(row, 12));
                    
                    // O列(13): RQI
                    roadEvaluation.setRqi(getCellBigDecimalValue(row, 13));
                    
                    // P列(14): RDI
                    roadEvaluation.setRdi(getCellBigDecimalValue(row, 14));
                    
                    // Q列(15): PWI
                    roadEvaluation.setPwi(getCellBigDecimalValue(row, 15));
                    
                    // S列(17): SRI
                    roadEvaluation.setSri(getCellBigDecimalValue(row, 17));
                    
                    // T列(18): PSSI
                    roadEvaluation.setPssi(getCellBigDecimalValue(row, 18));
                    
                    // U列(19): 路基SCI
                    roadEvaluation.setSci(getCellBigDecimalValue(row, 19));
                    
                    // V列(20): 桥梁构造物BCI
                    roadEvaluation.setBci(getCellBigDecimalValue(row, 20));
                    
                    // W列(21): 沿线设施TCI
                    roadEvaluation.setTci(getCellBigDecimalValue(row, 21));
                    
                    // X列(22): 检测方式
                    roadEvaluation.setDetectionMethod(getCellStringValue(row, 22));
                    
                    // Y列(23): 评定标准
                    roadEvaluation.setEvaluationStandard(getCellStringValue(row, 23));
                    
                    // Z列(24): 评定年度
                    String yearStr = getCellStringValue(row, 24);
                    if (StringUtils.isNotEmpty(yearStr)) {
                        try {
                            roadEvaluation.setEvaluationYear(Integer.parseInt(yearStr));
                        } catch (NumberFormatException e) {
                            log.warn("无法解析评定年度: {}, 行号: {}", yearStr, rowIndex + 1);
                        }
                    }
                    
                    // AA列(25): 备注
                    roadEvaluation.setRemark(getCellStringValue(row, 25));
                    
                    // 验证必填字段
                    if (StringUtils.isEmpty(roadEvaluation.getRoadCode())) {
                        log.warn("路线编号为空，跳过行号: {}", rowIndex + 1);
                        continue;
                    }
                    
                    if (StringUtils.isEmpty(roadEvaluation.getDivisionCode())) {
                        log.warn("行政区划代码为空，跳过行号: {}", rowIndex + 1);
                        continue;
                    }
                    
                    if (StringUtils.isEmpty(roadEvaluation.getStartCode())) {
                        log.warn("起点桩号为空，跳过行号: {}", rowIndex + 1);
                        continue;
                    }
                    
                    if (StringUtils.isEmpty(roadEvaluation.getEndCode())) {
                        log.warn("终点桩号为空，跳过行号: {}", rowIndex + 1);
                        continue;
                    }
                    
                    if (roadEvaluation.getDirection() == null) {
                        log.warn("检测方向为空，跳过行号: {}", rowIndex + 1);
                        continue;
                    }
                    
                    if (StringUtils.isEmpty(roadEvaluation.getRoadType())) {
                        log.warn("路面类型为空，跳过行号: {}", rowIndex + 1);
                        continue;
                    }
                    
                    roadEvaluationList.add(roadEvaluation);
                    
                } catch (Exception e) {
                    log.error("解析Excel第{}行数据失败: {}", rowIndex + 1, e.getMessage());
                    throw new ServiceException("解析Excel第" + (rowIndex + 1) + "行数据失败: " + e.getMessage());
                }
            }
        }
        
        return roadEvaluationList;
    }
    
    /**
     * 获取单元格字符串值
     */
    private String getCellStringValue(Row row, int columnIndex) {
        Cell cell = row.getCell(columnIndex);
        if (cell == null) {
            return null;
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return new SimpleDateFormat("yyyy-MM-dd").format(cell.getDateCellValue());
                } else {
                    // 如果是整数，去掉小数点
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == (long) numericValue) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                try {
                    return cell.getStringCellValue();
                } catch (Exception e) {
                    return String.valueOf(cell.getNumericCellValue());
                }
            default:
                return null;
        }
    }
    
    /**
     * 获取单元格BigDecimal值
     */
    private BigDecimal getCellBigDecimalValue(Row row, int columnIndex) {
        Cell cell = row.getCell(columnIndex);
        if (cell == null) {
            return null;
        }
        
        try {
            switch (cell.getCellType()) {
                case NUMERIC:
                    return BigDecimal.valueOf(cell.getNumericCellValue());
                case STRING:
                    String value = cell.getStringCellValue().trim();
                    if (StringUtils.isEmpty(value)) {
                        return null;
                    }
                    return new BigDecimal(value);
                case FORMULA:
                    try {
                        return BigDecimal.valueOf(cell.getNumericCellValue());
                    } catch (Exception e) {
                        String formulaValue = cell.getStringCellValue().trim();
                        if (StringUtils.isEmpty(formulaValue)) {
                            return null;
                        }
                        return new BigDecimal(formulaValue);
                    }
                default:
                    return null;
            }
        } catch (Exception e) {
            log.warn("无法解析数值，单元格位置: 行{}, 列{}, 值: {}", row.getRowNum() + 1, columnIndex + 1, cell.toString());
            return null;
        }
    }
    
    /**
     * 判断行是否为空
     */
    private boolean isRowEmpty(Row row) {
        if (row == null) {
            return true;
        }
        for (int cellIndex = row.getFirstCellNum(); cellIndex < row.getLastCellNum(); cellIndex++) {
            Cell cell = row.getCell(cellIndex);
            if (cell != null && cell.getCellType() != CellType.BLANK && 
                StringUtils.isNotEmpty(getCellStringValue(row, cellIndex))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 导入道路技术状况评定信息数据
     *
     * @param roadEvaluationList 道路技术状况评定信息列表
     * @param isUpdateSupport 是否支持更新，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    public String importRoadEvaluation(List<RoadEvaluation> roadEvaluationList, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(roadEvaluationList) || roadEvaluationList.size() == 0) {
            throw new ServiceException("导入道路技术状况评定信息数据不能为空！");
        }
        
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        
        // 批量插入的集合
        List<RoadEvaluation> batchInsertList = new ArrayList<>();
        List<RoadEvaluation> batchUpdateList = new ArrayList<>();
        
        // 批量大小
        final int BATCH_SIZE = 1000;
        
        for (RoadEvaluation roadEvaluation : roadEvaluationList) {
            try {
                // 验证数据
                BeanValidators.validateWithException(validator, roadEvaluation);
                
                // 验证是否存在这个道路评定信息
                RoadEvaluation r = roadEvaluationMapper.checkRoadCodeUnique(roadEvaluation);
                if (StringUtils.isNull(r)) {
                    // 新增数据
                    roadEvaluation.setCreateTime(DateUtils.getNowDate());
                    if (SecurityUtils.getUserId() != null) {
                        roadEvaluation.setCreator(SecurityUtils.getUserId());
                    }
                    batchInsertList.add(roadEvaluation);
                    
                    // 达到批量大小时执行批量插入
                    if (batchInsertList.size() >= BATCH_SIZE) {
                        int insertCount = roadEvaluationMapper.batchInsertRoadEvaluation(new ArrayList<>(batchInsertList));
                        successNum += insertCount;
                        for (RoadEvaluation item : batchInsertList) {
                            successMsg.append("<br/>" + (successNum - insertCount + batchInsertList.indexOf(item) + 1) + "、路线编号 " + item.getRoadCode() + " 导入成功");
                        }
                        batchInsertList.clear();
                    }
                } else if (isUpdateSupport) {
                    // 更新数据
                    roadEvaluation.setId(r.getId());
                    roadEvaluation.setUpdateTime(DateUtils.getNowDate());
                    if (SecurityUtils.getUserId() != null) {
                        roadEvaluation.setModifier(SecurityUtils.getUserId());
                    }
                    batchUpdateList.add(roadEvaluation);
                    
                    // 达到批量大小时执行批量更新（注意：这里仍使用单条更新，因为批量更新比较复杂）
                    if (batchUpdateList.size() >= BATCH_SIZE) {
                        for (RoadEvaluation updateItem : batchUpdateList) {
                            roadEvaluationMapper.updateRoadEvaluation(updateItem);
                            successNum++;
                            successMsg.append("<br/>" + successNum + "、路线编号 " + updateItem.getRoadCode() + " 更新成功");
                        }
                        batchUpdateList.clear();
                    }
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、路线编号 " + roadEvaluation.getRoadCode() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、路线编号 " + roadEvaluation.getRoadCode() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        
        // 处理剩余的批量插入数据
        if (!batchInsertList.isEmpty()) {
            try {
                int insertCount = roadEvaluationMapper.batchInsertRoadEvaluation(batchInsertList);
                int startIndex = successNum + 1;
                successNum += insertCount;
                for (int i = 0; i < batchInsertList.size(); i++) {
                    RoadEvaluation item = batchInsertList.get(i);
                    successMsg.append("<br/>" + (startIndex + i) + "、路线编号 " + item.getRoadCode() + " 导入成功");
                }
            } catch (Exception e) {
                for (RoadEvaluation item : batchInsertList) {
                    failureNum++;
                    String msg = "<br/>" + failureNum + "、路线编号 " + item.getRoadCode() + " 批量导入失败：";
                    failureMsg.append(msg + e.getMessage());
                }
                log.error("批量插入失败", e);
            }
        }
        
        // 处理剩余的批量更新数据
        if (!batchUpdateList.isEmpty()) {
            for (RoadEvaluation updateItem : batchUpdateList) {
                try {
                    roadEvaluationMapper.updateRoadEvaluation(updateItem);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、路线编号 " + updateItem.getRoadCode() + " 更新成功");
                } catch (Exception e) {
                    failureNum++;
                    String msg = "<br/>" + failureNum + "、路线编号 " + updateItem.getRoadCode() + " 更新失败：";
                    failureMsg.append(msg + e.getMessage());
                    log.error(msg, e);
                }
            }
        }
        
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 下载导入模板
     *
     * @param response HTTP响应
     */
    @Override
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<RoadEvaluation> util = new ExcelUtil<RoadEvaluation>(RoadEvaluation.class);
        util.importTemplateExcel(response, "道路技术状况评定信息数据");
    }

    /**
     * 导出Word报告
     *
     * @param response HTTP响应
     * @param params 参数
     */
    @Override
    public void exportWordReport(HttpServletResponse response, Map<String, Object> params) throws Exception {
        InputStream templateStream = null;
        try {
            // 解决POI安全限制：设置ZIP文件的最小压缩比率，避免Zip bomb检测误报
            ZipSecureFile.setMinInflateRatio(0.001);
            
            // 加载Word模板
            ClassPathResource templateResource = new ClassPathResource("static/word/county_report_template.docx");
            templateStream = templateResource.getInputStream();
            XWPFDocument document = new XWPFDocument(templateStream);
            
            // 获取参数
            String area = (String) params.get("area");
            String year = String.valueOf(params.get("year"));
            String companyName = (String) params.get("companyName");
            String startDate = (String) params.get("startDate");
            String endDate = (String) params.get("endDate");
            String startDateStr = (String) params.get("startDateStr");
            String endDateStr = (String) params.get("endDateStr");
            Long teamId = ((Integer) params.get("teamId")).longValue();
            
            // 获取项目ID参数
            Long projectId = ((Integer) params.get("projectId")).longValue();

            // 使用WordDocumentUtils替换占位符
            WordDocumentUtils.replaceTextInDocument(document, "${area}", area);
            WordDocumentUtils.replaceTextInDocument(document, "${year}", year);
            WordDocumentUtils.replaceTextInDocument(document, "${companyName}", companyName);
            WordDocumentUtils.replaceTextInDocument(document, "${startDate}", startDate);
            WordDocumentUtils.replaceTextInDocument(document, "${endDate}", endDate);
            WordDocumentUtils.replaceTextInDocument(document, "${startDateStr}", startDateStr);
            WordDocumentUtils.replaceTextInDocument(document, "${endDateStr}", endDateStr);
            
            // 替换日期范围占位符
            String dateRange = startDateStr + "至" + endDateStr;
            WordDocumentUtils.replaceTextInDocument(document, "${startDateStr}至${endDateStr}", dateRange);
            
            // 如果提供了teamId，查询分组用户并填充表格
            if (teamId != null) {
                List<CheckUser> teamUsers = checkTeamUserMapper.selectCheckUsersByTeamId(teamId, 2); // type=2
                fillUserTable(document, teamUsers);
            }

            // 如果提供了projectId，查询道路评定数据并计算统计参数
            if (projectId != null) {
                RoadEvaluation query = new RoadEvaluation();
                query.setProjectId(projectId);
                List<RoadEvaluation> roadEvaluationList = roadEvaluationMapper.selectRoadEvaluationList(query);
                
                // 计算统计参数
                calculateAndReplaceStatistics(document, roadEvaluationList);
                
                // 生成检测结论并替换占位符
                generateAndReplaceCheckConclusion(document, roadEvaluationList);
                
                // 填充第六个表格（统计数据表格）
                fillSixthTable(document, roadEvaluationList);
                //详细检测数据
                fillDetailCheckList(roadEvaluationList,document);

            }
            
            // 设置响应头
            String fileName = URLEncoder.encode("道路技术状况评定报告_" + area + "_" + year + ".docx", "UTF-8");
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
            
            // 输出文档
            document.write(response.getOutputStream());
            document.close();
            
        } catch (Exception e) {
            log.error("导出Word报告失败", e);
            throw new Exception("导出Word报告失败: " + e.getMessage());
        } finally {
            if (templateStream != null) {
                try {
                    templateStream.close();
                } catch (Exception e) {
                    log.warn("关闭模板流失败: {}", e.getMessage());
                }
            }
        }
    }



    private void fillDetailCheckList(List<RoadEvaluation> roadEvaluationList,XWPFDocument document) {
        //详细检测数据
        if (!roadEvaluationList.isEmpty()) {
            // 对数据进行排序：按路线编码、起始桩号、结束桩号正排序
            roadEvaluationList.sort((o1, o2) -> {
                // 第一级：按路线编号排序
                int roadCodeCompare = 0;
                if (o1.getRoadCode() != null && o2.getRoadCode() != null) {
                    roadCodeCompare = o1.getRoadCode().compareTo(o2.getRoadCode());
                }
                if (roadCodeCompare != 0) {
                    return roadCodeCompare;
                }

                // 第二级：按起点桩号排序
                if (o1.getStartCode() != null && o2.getStartCode() != null) {
                    try {
                        // 尝试按数值排序
                        Double start1 = Double.parseDouble(o1.getStartCode().replaceAll("[^0-9.]", ""));
                        Double start2 = Double.parseDouble(o2.getStartCode().replaceAll("[^0-9.]", ""));
                        int startCompare = start1.compareTo(start2);
                        if (startCompare != 0) {
                            return startCompare;
                        }
                    } catch (NumberFormatException e) {
                        // 如果不是数值，按字符串排序
                        int startCompare = o1.getStartCode().compareTo(o2.getStartCode());
                        if (startCompare != 0) {
                            return startCompare;
                        }
                    }
                }

                // 第三级：按结束桩号排序
                if (o1.getEndCode() != null && o2.getEndCode() != null) {
                    try {
                        // 尝试按数值排序
                        Double end1 = Double.parseDouble(o1.getEndCode().replaceAll("[^0-9.]", ""));
                        Double end2 = Double.parseDouble(o2.getEndCode().replaceAll("[^0-9.]", ""));
                        return end1.compareTo(end2);
                    } catch (NumberFormatException e) {
                        // 如果不是数值，按字符串排序
                        return o1.getEndCode().compareTo(o2.getEndCode());
                    }
                }

                return 0;
            });

            fillRoadEvaluationTable(document, roadEvaluationList);
        }
    }

    /**
     * 填充道路评定数据表格
     *
     * @param document Word文档
     * @param roadEvaluationList 道路评定数据列表
     */
    private void fillRoadEvaluationTable(XWPFDocument document, List<RoadEvaluation> roadEvaluationList) {
        try {
            // 获取最后一个表格（道路评定数据表格）
            List<XWPFTable> tables = document.getTables();
            if (tables.isEmpty()) {
                log.warn("Word模板中没有找到表格，无法填充道路评定数据");
                return;
            }
            
            XWPFTable table = tables.get(tables.size() - 1); // 获取最后一个表格
            
            // 从第二行开始填充数据（第一行是表头）
            for (int i = 0; i < roadEvaluationList.size(); i++) {
                RoadEvaluation roadEvaluation = roadEvaluationList.get(i);
                
                // 创建新行或使用现有行
                XWPFTableRow row;
                if (table.getRows().size() > i + 1) {
                    row = table.getRow(i + 1);
                } else {
                    row = table.createRow();
                }
                
                // 确保行有足够的单元格（9列）
                while (row.getTableCells().size() < 9) {
                    row.createCell();
                }
                
                // 填充数据：9列：路线编码、起始桩号、结束桩号、路段长度（Km）、路面材质、公路等级、PCI、RQI、PQI
                // 第1列：路线编码
                safeSetCellText(row, 0, roadEvaluation.getRoadCode() != null ? roadEvaluation.getRoadCode() : "","FF0000");
                
                // 第2列：起始桩号（标准格式）
                String startCode = formatStakeCode(roadEvaluation.getStartCode());
                safeSetCellText(row, 1, startCode,"FF0000");
                
                // 第3列：结束桩号（标准格式）
                String endCode = formatStakeCode(roadEvaluation.getEndCode());
                safeSetCellText(row, 2, endCode,"FF0000");
                
                // 第4列：路段长度（Km）- 将米转换为公里
                String roadLength = "";
                if (roadEvaluation.getRoadLength() != null) {
                    // 将米转换为公里，保留3位小数
                    double lengthKm = roadEvaluation.getRoadLength().doubleValue() / 1000.0;
                    roadLength = String.format("%.3f", lengthKm);
                }
                safeSetCellText(row, 3, roadLength,"FF0000");
                
                // 第5列：路面材质（路面类型）
                safeSetCellText(row, 4, roadEvaluation.getRoadType() != null ? roadEvaluation.getRoadType() : "","FF0000");
                
                // 第6列：公路等级（技术等级）
                safeSetCellText(row, 5, roadEvaluation.getLevel() != null ? roadEvaluation.getLevel() : "","FF0000");
                
                // 第7列：PCI
                String pci = "";
                if (roadEvaluation.getPci() != null) {
                    pci = String.format("%.2f", roadEvaluation.getPci().doubleValue());
                }
                safeSetCellText(row, 6, pci,"FF0000");
                
                // 第8列：RQI
                String rqi = "";
                if (roadEvaluation.getRqi() != null) {
                    rqi = String.format("%.2f", roadEvaluation.getRqi().doubleValue());
                }
                safeSetCellText(row, 7, rqi,"FF0000");
                
                // 第9列：PQI
                String pqi = "";
                if (roadEvaluation.getPqi() != null) {
                    pqi = String.format("%.2f", roadEvaluation.getPqi().doubleValue());
                }
                safeSetCellText(row, 8, pqi,"FF0000");
            }
            
            log.info("成功填充道路评定数据表格，共{}条记录", roadEvaluationList.size());
        } catch (Exception e) {
            log.error("填充道路评定数据表格失败", e);
        }
    }

    /**
     * 填充用户表格数据
     *
     * @param document Word文档
     * @param teamUsers 分组用户列表
     */
    private void fillUserTable(XWPFDocument document, List<CheckUser> teamUsers) {
        try {
            // 获取第二个表格（索引为1）
            if (document.getTables().size() >= 3) {
                XWPFTable table = document.getTables().get(2);
                
                // 从第二行开始填充数据（第一行是表头）
                for (int i = 0; i < teamUsers.size(); i++) {
                    CheckUser user = teamUsers.get(i);
                    
                    // 创建新行或使用现有行
                    XWPFTableRow row;
                    if (table.getRows().size() > i + 1) {
                        row = table.getRow(i + 1);
                    } else {
                        row = table.createRow();
                    }
                    
                    // 确保行有足够的单元格
                    while (row.getTableCells().size() < 4) {
                        row.createCell();
                    }
                    
                    // 填充数据：第一列:position,第二列:user_name,第三列:title,第四列:certificate_no
                    safeSetCellText(row, 0, user.getPosition() != null ? user.getPosition() : "",null);
                    safeSetCellText(row, 1, user.getUserName() != null ? user.getUserName() : "",null);
                    safeSetCellText(row, 2, user.getTitle() != null ? user.getTitle() : "",null);
                    safeSetCellText(row, 3, user.getCertificateNo() != null ? user.getCertificateNo() : "",null);
                }
                
                log.info("成功填充用户表格数据，共{}条记录", teamUsers.size());
            } else {
                log.warn("Word模板中没有找到第二个表格，无法填充用户数据");
            }
        } catch (Exception e) {
            log.error("填充用户表格数据失败", e);
        }
    }

    /**
     * 计算统计参数并替换到Word文档中
     *
     * @param document Word文档
     * @param roadEvaluationList 道路评定数据列表
     */
    private void calculateAndReplaceStatistics(XWPFDocument document, List<RoadEvaluation> roadEvaluationList) {
        try {
            // 初始化统计变量  
            double totalMiles = 0.0;
            double countryMiles = 0.0;  // 国道+村道（文档中国道和村道都用${countyMiles}表示）
            double countyMiles = 0.0;  // 国道+村道（文档中国道和村道都用${countyMiles}表示）
            double provinceMiles = 0.0;  // 省道
            double cityMiles = 0.0;  // 县道
            double areaMiles = 0.0;  // 乡道
            
            double rankOneMiles = 0.0;  // 一级公路
            double rankTwoMiles = 0.0;  // 二级公路
            double rankThreeMiles = 0.0;  // 三级公路
            double rankFourMiles = 0.0;  // 四级公路
            double outMiles = 0.0;  // 等外公路
            
            double liqingMiles = 0.0;  // 沥青路面
            double shuiniMiles = 0.0;  // 水泥路面
            
            // 遍历数据进行统计
            for (RoadEvaluation roadEvaluation : roadEvaluationList) {
                if (roadEvaluation.getRoadLength() == null) {
                    continue;
                }
                
                // 将米转换为公里
                double lengthKm = roadEvaluation.getRoadLength().doubleValue() / 1000.0;
                totalMiles += lengthKm;
                
                // 根据路线编号分类（国道、省道、县道、乡道、村道）
                String roadCode = roadEvaluation.getRoadCode();
                if (roadCode != null) {
                    roadCode = roadCode.toUpperCase().trim();
                    if (roadCode.startsWith("G") || roadCode.startsWith("国道")) {
                        countryMiles += lengthKm;  // 国道（countryMiles）
                    } else if (roadCode.startsWith("S") || roadCode.startsWith("省道")) {
                        provinceMiles += lengthKm;  // 省道
                    } else if (roadCode.startsWith("X") || roadCode.startsWith("县道")) {
                        cityMiles += lengthKm;  // 县道
                    } else if (roadCode.startsWith("Y") || roadCode.startsWith("乡道")) {
                        areaMiles += lengthKm;  // 乡道
                    } else if (roadCode.startsWith("C") || roadCode.startsWith("村道")) {
                        countyMiles += lengthKm;  // 村道
                    } else {
                        // 如果无法明确分类，暂时归为县道
                        cityMiles += lengthKm;
                    }
                }
                
                // 根据技术等级分类
                String level = roadEvaluation.getLevel();
                if (level != null) {
                    level = level.trim();
                    if (level.contains("一级") || level.equals("1")) {
                        rankOneMiles += lengthKm;
                    } else if (level.contains("二级") || level.equals("2")) {
                        rankTwoMiles += lengthKm;
                    } else if (level.contains("三级") || level.equals("3")) {
                        rankThreeMiles += lengthKm;
                    } else if (level.contains("四级") || level.equals("4")) {
                        rankFourMiles += lengthKm;
                    } else if (level.contains("等外") || level.contains("其他")) {
                        outMiles += lengthKm;
                    } else {
                        // 默认归为三级公路
                        rankThreeMiles += lengthKm;
                    }
                }
                
                // 根据路面类型分类
                String roadType = roadEvaluation.getRoadType();
                if (roadType != null) {
                    roadType = roadType.trim();
                    if (roadType.contains("沥青") || roadType.contains("柏油")) {
                        liqingMiles += lengthKm;
                    } else if (roadType.contains("水泥") || roadType.contains("混凝土")) {
                        shuiniMiles += lengthKm;
                    } else {
                        // 默认归为沥青路面
                        liqingMiles += lengthKm;
                    }
                }
            }
            
            // 替换统计参数到Word文档
            WordDocumentUtils.replaceTextInDocument(document, "${totalMiles}", String.format("%.3f", totalMiles));
            WordDocumentUtils.replaceTextInDocument(document, "${countryMiles}", String.format("%.3f", countryMiles));
            WordDocumentUtils.replaceTextInDocument(document, "${countyMiles}", String.format("%.3f", countyMiles));
            WordDocumentUtils.replaceTextInDocument(document, "${provinceMiles}", String.format("%.3f", provinceMiles));
            WordDocumentUtils.replaceTextInDocument(document, "${cityMiles}", String.format("%.3f", cityMiles));
            WordDocumentUtils.replaceTextInDocument(document, "${areaMiles}", String.format("%.3f", areaMiles));
            
            WordDocumentUtils.replaceTextInDocument(document, "${rankOneMiles}", String.format("%.3f", rankOneMiles));
            WordDocumentUtils.replaceTextInDocument(document, "${rankTwoMiles}", String.format("%.3f", rankTwoMiles));
            WordDocumentUtils.replaceTextInDocument(document, "${rankThreeMiles}", String.format("%.3f", rankThreeMiles));
            WordDocumentUtils.replaceTextInDocument(document, "${rankFourMiles}", String.format("%.3f", rankFourMiles));
            WordDocumentUtils.replaceTextInDocument(document, "${outMiles}", String.format("%.3f", outMiles));
            
            WordDocumentUtils.replaceTextInDocument(document, "${liqingMiles}", String.format("%.3f", liqingMiles));
            WordDocumentUtils.replaceTextInDocument(document, "${shuiniMiles}", String.format("%.3f", shuiniMiles));
            
            log.info("成功计算并替换统计参数: 总里程={}, 国道+村道={}, 省道={}, 县道={}, 乡道={}", 
                totalMiles, countyMiles, provinceMiles, cityMiles, areaMiles);
            log.info("技术等级统计: 一级={}, 二级={}, 三级={}, 四级={}, 等外={}", 
                rankOneMiles, rankTwoMiles, rankThreeMiles, rankFourMiles, outMiles);
            log.info("路面类型统计: 沥青={}, 水泥={}", 
                liqingMiles, shuiniMiles);
                
        } catch (Exception e) {
            log.error("计算统计参数失败", e);
            // 如果计算失败，设置默认值
            WordDocumentUtils.replaceTextInDocument(document, "${totalMiles}", "0.00");
            WordDocumentUtils.replaceTextInDocument(document, "${countryMiles}", "0.00");
            WordDocumentUtils.replaceTextInDocument(document, "${countyMiles}", "0.00");
            WordDocumentUtils.replaceTextInDocument(document, "${provinceMiles}", "0.00");
            WordDocumentUtils.replaceTextInDocument(document, "${cityMiles}", "0.00");
            WordDocumentUtils.replaceTextInDocument(document, "${areaMiles}", "0.00");
            WordDocumentUtils.replaceTextInDocument(document, "${rankOneMiles}", "0.00");
            WordDocumentUtils.replaceTextInDocument(document, "${rankTwoMiles}", "0.00");
            WordDocumentUtils.replaceTextInDocument(document, "${rankThreeMiles}", "0.00");
            WordDocumentUtils.replaceTextInDocument(document, "${rankFourMiles}", "0.00");
            WordDocumentUtils.replaceTextInDocument(document, "${outMiles}", "0.00");
            WordDocumentUtils.replaceTextInDocument(document, "${liqingMiles}", "0.00");
            WordDocumentUtils.replaceTextInDocument(document, "${shuiniMiles}", "0.00");
        }
    }

    /**
     * 生成检测结论并替换到Word文档中
     *
     * @param document Word文档
     * @param roadEvaluationList 道路评定数据列表
     */
    private void generateAndReplaceCheckConclusion(XWPFDocument document, List<RoadEvaluation> roadEvaluationList) {
        try {
            if (roadEvaluationList == null || roadEvaluationList.isEmpty()) {
                WordDocumentUtils.replaceTextInDocument(document, "${checkConclusion}", "暂无检测数据");
                return;
            }
            
            // 计算PQI、PCI、RQI的平均值
            double pqiSum = 0.0, pciSum = 0.0, rqiSum = 0.0;
            int pqiCount = 0, pciCount = 0, rqiCount = 0;

            // 统计各等级路段数量（用于PQI）
            int pqiExcellent = 0, pqiGood = 0, pqiFair = 0, pqiPoor = 0, pqiBad = 0;
            // 统计各等级路段数量（用于PCI）
            int pciExcellent = 0, pciGood = 0, pciFair = 0, pciPoor = 0, pciBad = 0;
            // 统计各等级路段数量（用于RQI）
            int rqiExcellent = 0, rqiGood = 0, rqiFair = 0, rqiPoor = 0, rqiBad = 0;

            for (RoadEvaluation roadEval : roadEvaluationList) {
                // 统计PQI
                if (roadEval.getPqi() != null) {
                    double pqi = roadEval.getPqi().doubleValue();
                    pqiSum += pqi;
                    pqiCount++;
                    
                    if (pqi >= 90) {
                        pqiExcellent++;
                    } else if (pqi >= 80) {
                        pqiGood++;
                    } else if (pqi >= 70) {
                        pqiFair++;
                    } else if (pqi >= 60) {
                        pqiPoor++;
                    } else {
                        pqiBad++;
                    }
                }
                
                // 统计PCI
                if (roadEval.getPci() != null) {
                    double pci = roadEval.getPci().doubleValue();
                    pciSum += pci;
                    pciCount++;
                    
                    if (pci >= 90) {
                        pciExcellent++;
                    } else if (pci >= 80) {
                        pciGood++;
                    } else if (pci >= 70) {
                        pciFair++;
                    } else if (pci >= 60) {
                        pciPoor++;
                    } else {
                        pciBad++;
                    }
                }
                
                // 统计RQI
                if (roadEval.getRqi() != null) {
                    double rqi = roadEval.getRqi().doubleValue();
                    rqiSum += rqi;
                    rqiCount++;
                    
                    if (rqi >= 90) {
                        rqiExcellent++;
                    } else if (rqi >= 80) {
                        rqiGood++;
                    } else if (rqi >= 70) {
                        rqiFair++;
                    } else if (rqi >= 60) {
                        rqiPoor++;
                    } else {
                        rqiBad++;
                    }
                }
            }

            // 计算平均值
            double pqiAvg = pqiCount > 0 ? pqiSum / pqiCount : 0.0;
            double pciAvg = pciCount > 0 ? pciSum / pciCount : 0.0;
            double rqiAvg = rqiCount > 0 ? rqiSum / rqiCount : 0.0;

            // 根据评分等级规则获取评定结果
            String pqiLevel = getEvaluationLevel(pqiAvg);
            String pciLevel = getEvaluationLevel(pciAvg);
            String rqiLevel = getEvaluationLevel(rqiAvg);

            // 计算优良中路率和次差路率（PQI）
            double pqiGoodRate = pqiCount > 0 ? (double)(pqiExcellent + pqiGood + pqiFair) / pqiCount * 100 : 0.0;
            double pqiBadRate = pqiCount > 0 ? (double)(pqiPoor + pqiBad) / pqiCount * 100 : 0.0;
            
            // 计算优良中路率和次差路率（PCI）
            double pciGoodRate = pciCount > 0 ? (double)(pciExcellent + pciGood + pciFair) / pciCount * 100 : 0.0;
            double pciBadRate = pciCount > 0 ? (double)(pciPoor + pciBad) / pciCount * 100 : 0.0;
            
            // 计算优良中路率和次差路率（RQI）
            double rqiGoodRate = rqiCount > 0 ? (double)(rqiExcellent + rqiGood + rqiFair) / rqiCount * 100 : 0.0;
            double rqiBadRate = rqiCount > 0 ? (double)(rqiPoor + rqiBad) / rqiCount * 100 : 0.0;

            // 生成检测结论描述
            String checkConclusion = String.format(
                "经过本次检测得出：本项目检测路段总体路面性能（PQI）均值为%.2f，评定为%s，优良中路率为%.1f%%，次差路率为%.1f%%。" +
                "从分项指标路面损坏PCI来看，均值为%.2f，评定为%s，优良中路率为%.2f%%，次差路率为%.2f%%；" +
                "从分项指标平整度RQI来看，均值为%.2f，评定为%s，优良中路率为%.2f%%，次差路率为%.2f%%。",
                pqiAvg, pqiLevel, pqiGoodRate, pqiBadRate,
                pciAvg, pciLevel, pciGoodRate, pciBadRate,
                rqiAvg, rqiLevel, rqiGoodRate, rqiBadRate
            );

            // 替换检测结论占位符
            WordDocumentUtils.replaceTextInDocument(document, "${checkConclusion}", checkConclusion);

            log.info("成功生成检测结论: PQI均值={} 评定={}, PCI均值={} 评定={}, RQI均值={} 评定={}",
                pqiAvg, pqiLevel, pciAvg, pciLevel, rqiAvg, rqiLevel);

        } catch (Exception e) {
            log.error("生成检测结论失败", e);
            // 如果生成失败，设置默认值
            WordDocumentUtils.replaceTextInDocument(document, "${checkConclusion}", "检测结论生成失败，请检查数据");
        }
    }

    /**
     * 根据数值获取评定等级
     * 评分等级规则：优>=90, 良>=80且<90, 中>=70且<80, 次>=60且<70, 差<60
     */
    private String getEvaluationLevel(double value) {
        if (value >= 90) {
            return "优";
        } else if (value >= 80) {
            return "良";
        } else if (value >= 70) {
            return "中";
        } else if (value >= 60) {
            return "次";
        } else {
            return "差";
        }
    }

    /**
     * 填充第六个表格（统计数据表格）
     *
     * @param document Word文档
     * @param roadEvaluationList 道路评定数据列表
     */
    private void fillSixthTable(XWPFDocument document, List<RoadEvaluation> roadEvaluationList) {
        try {
            // 获取第六个表格（索引为5）
            List<XWPFTable> tables = document.getTables();
            if (tables.size() < 6) {
                log.warn("Word模板中表格数量不足，无法找到第六个表格，当前表格数量: {}", tables.size());
                return;
            }
            
            XWPFTable table = tables.get(5); // 第六个表格（索引为5）
            
            // 计算统计数据
            Map<String, Object> statistics = calculateStatisticsForSixthTable(roadEvaluationList);
            
            // 定义表格行对应的统计参数映射关系（跳过标题行，从第二行开始）
            String[] statisticsKeys = {
                "countryMiles",    // 国道里程
                "provinceMiles",  // 省道里程
                "cityMiles",      // 县道里程
                "areaMiles",      // 乡道里程
                "countyMiles",    // 村道里程
                "totalMiles",     // 合计
                "rankOneMiles",   // 一级公路里程
                "rankTwoMiles",   // 二级公路里程
                "rankThreeMiles", // 三级公路里程
                "rankFourMiles",  // 四级公路里程
                "outMiles",       // 等外公路里程
                "totalRankMiles", // 合计
                "liqingMiles",    // 沥青路面里程
                "shuiniMiles",    // 水泥路面里程
                "totalTypeMiles"  // 合计
            };
            for (int i = 0; i < statisticsKeys.length; i++) {
                WordDocumentUtils.replaceTextInDocument(document, "${"+statisticsKeys[i]+"}", String.format("%.3f", statistics.get(statisticsKeys[i])));
            }
            log.info("成功填充第六个表格，共处理{}行数据，第三列数据设置为红色", statisticsKeys.length);
            
        } catch (Exception e) {
            log.error("填充第六个表格失败", e);
        }
    }

    /**
     * 计算第六个表格所需的统计数据
     *
     * @param roadEvaluationList 道路评定数据列表
     * @return 统计数据Map
     */
    private Map<String, Object> calculateStatisticsForSixthTable(List<RoadEvaluation> roadEvaluationList) {
        Map<String, Object> statistics = new HashMap<>();
        
        // 初始化统计变量  
        double totalMiles = 0.0;
        double countyMiles = 0.0;  // 国道+村道
        double provinceMiles = 0.0;  // 省道
        double cityMiles = 0.0;  // 县道
        double areaMiles = 0.0;  // 乡道
        
        double rankOneMiles = 0.0;  // 一级公路
        double rankTwoMiles = 0.0;  // 二级公路
        double rankThreeMiles = 0.0;  // 三级公路
        double rankFourMiles = 0.0;  // 四级公路
        double outMiles = 0.0;  // 等外公路
        
        double liqingMiles = 0.0;  // 沥青路面
        double shuiniMiles = 0.0;  // 水泥路面
        
        // 遍历数据进行统计
        for (RoadEvaluation roadEvaluation : roadEvaluationList) {
            if (roadEvaluation.getRoadLength() == null) {
                continue;
            }
            
            // 将米转换为公里
            double lengthKm = roadEvaluation.getRoadLength().doubleValue() / 1000.0;
            totalMiles += lengthKm;
            
            // 根据路线编号分类
            String roadCode = roadEvaluation.getRoadCode();
            if (roadCode != null) {
                roadCode = roadCode.toUpperCase().trim();
                if (roadCode.startsWith("G") || roadCode.startsWith("国道")) {
                    countyMiles += lengthKm;  // 国道
                } else if (roadCode.startsWith("S") || roadCode.startsWith("省道")) {
                    provinceMiles += lengthKm;  // 省道
                } else if (roadCode.startsWith("X") || roadCode.startsWith("县道")) {
                    cityMiles += lengthKm;  // 县道
                } else if (roadCode.startsWith("Y") || roadCode.startsWith("乡道")) {
                    areaMiles += lengthKm;  // 乡道
                } else if (roadCode.startsWith("C") || roadCode.startsWith("村道")) {
                    countyMiles += lengthKm;  // 村道（归入countyMiles）
                } else {
                    cityMiles += lengthKm;  // 默认归为县道
                }
            }
            
            // 根据技术等级分类
            String level = roadEvaluation.getLevel();
            if (level != null) {
                level = level.trim();
                if (level.contains("一级") || level.equals("1")) {
                    rankOneMiles += lengthKm;
                } else if (level.contains("二级") || level.equals("2")) {
                    rankTwoMiles += lengthKm;
                } else if (level.contains("三级") || level.equals("3")) {
                    rankThreeMiles += lengthKm;
                } else if (level.contains("四级") || level.equals("4")) {
                    rankFourMiles += lengthKm;
                } else if (level.contains("等外") || level.contains("其他")) {
                    outMiles += lengthKm;
                } else {
                    rankThreeMiles += lengthKm;  // 默认归为三级公路
                }
            }
            
            // 根据路面类型分类
            String roadType = roadEvaluation.getRoadType();
            if (roadType != null) {
                roadType = roadType.trim();
                if (roadType.contains("沥青") || roadType.contains("柏油")) {
                    liqingMiles += lengthKm;
                } else if (roadType.contains("水泥") || roadType.contains("混凝土")) {
                    shuiniMiles += lengthKm;
                } else {
                    liqingMiles += lengthKm;  // 默认归为沥青路面
                }
            }
        }
        
        // 计算合计
        double totalRankMiles = rankOneMiles + rankTwoMiles + rankThreeMiles + rankFourMiles + outMiles;
        double totalTypeMiles = liqingMiles + shuiniMiles;
        
        // 保存统计结果
        statistics.put("totalMiles", totalMiles);
        statistics.put("countyMiles", countyMiles);
        statistics.put("provinceMiles", provinceMiles);
        statistics.put("cityMiles", cityMiles);
        statistics.put("areaMiles", areaMiles);
        
        statistics.put("rankOneMiles", rankOneMiles);
        statistics.put("rankTwoMiles", rankTwoMiles);
        statistics.put("rankThreeMiles", rankThreeMiles);
        statistics.put("rankFourMiles", rankFourMiles);
        statistics.put("outMiles", outMiles);
        statistics.put("totalRankMiles", totalRankMiles);
        
        statistics.put("liqingMiles", liqingMiles);
        statistics.put("shuiniMiles", shuiniMiles);
        statistics.put("totalTypeMiles", totalTypeMiles);
        
        log.info("第六个表格统计计算完成: 总里程={}, 国道+村道={}, 省道={}, 县道={}, 乡道={}", 
                totalMiles, countyMiles, provinceMiles, cityMiles, areaMiles);
        log.info("技术等级统计: 一级={}, 二级={}, 三级={}, 四级={}, 等外={}, 合计={}", 
                rankOneMiles, rankTwoMiles, rankThreeMiles, rankFourMiles, outMiles, totalRankMiles);
        log.info("路面类型统计: 沥青={}, 水泥={}, 合计={}", 
                liqingMiles, shuiniMiles, totalTypeMiles);
        
        return statistics;
    }

    /**
     * 格式化桩号为标准格式 (如: K0+504, K1+504等)
     * 规则：千位以上的数值作为K后面的数字，百位及以下（后三位）作为+后面的数字
     */
    private String formatStakeCode(String originalCode) {
        if (originalCode == null || originalCode.trim().isEmpty()) {
            return "";
        }
        
        String code = originalCode.trim();
        
        // 如果已经是标准格式，直接返回
        if (code.matches("^K\\d+\\+\\d{3}$")) {
            return code;
        }
        
        try {
            // 提取所有数字
            String numbers = code.replaceAll("[^0-9]", "");
            
            if (numbers.isEmpty()) {
                return code; // 如果没有数字，返回原值
            }
            
            // 转换为整数
            int totalValue = Integer.parseInt(numbers);
            
            // 按照规则分离：千位以上作为K后面的数字，后三位作为+后面的数字
            int kmPart = totalValue / 1000;     // 千位以上部分
            int meterPart = totalValue % 1000;   // 后三位部分
            
            // 格式化为K+格式，米的部分固定为3位数字，不足补0
            return String.format("K%d+%03d", kmPart, meterPart);
            
        } catch (NumberFormatException e) {
            // 如果解析失败，尝试提取小数
            try {
                // 提取数字（包括小数点）
                String numbersWithDecimal = code.replaceAll("[^0-9.]", "");
                
                if (numbersWithDecimal.isEmpty()) {
                    return code;
                }
                
                // 转换为浮点数，然后转为整数（假设单位是米）
                double doubleValue = Double.parseDouble(numbersWithDecimal);
                int totalValue = (int) Math.round(doubleValue);
                
                // 按照规则分离
                int kmPart = totalValue / 1000;
                int meterPart = totalValue % 1000;
                
                return String.format("K%d+%03d", kmPart, meterPart);
                
            } catch (NumberFormatException ex) {
                // 最终失败，返回原值
                log.warn("桩号格式化失败，无法解析数字，原始值: {}", originalCode);
                return code;
            }
        }
    }

    /**
     * 安全地设置Word表格单元格文本
     */
    private void safeSetCellText(XWPFTableRow row, int cellIndex, String text,String color) {
        try {
            if (row != null && cellIndex >= 0 && cellIndex < row.getTableCells().size()) {
                XWPFTableCell cell = row.getCell(cellIndex);
                if (cell != null) {
                    // 清除现有段落，创建新的居中段落
                    cell.removeParagraph(0);
                    XWPFParagraph paragraph = cell.addParagraph();
                    paragraph.setAlignment(ParagraphAlignment.CENTER);
                    XWPFRun run = paragraph.createRun();
                    run.setText(text != null ? text : "");
                    // 使用方法三：通过CTRPr精确设置10.5磅字体
                    setFontSizeHalfPoint(run, 10.5);
                    run.setFontFamily("仿宋"); // 修改为宋体
                    
                    // 设置字体颜色
                    if(StringUtils.isNotEmpty(color)){
                        run.setColor(color);
                    }
                    // 设置单元格垂直居中对齐
                    cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
                }
            }
        } catch (Exception e) {
            log.warn("设置单元格文本失败 [行:{}, 列:{}]: {}",
                    row != null ? row.getTableCells().size() : "null", cellIndex, e.getMessage());
        }
    }

    /**
     * 精确设置字体大小（支持小数点）
     * @param run XWPFRun对象
     * @param fontSize 字体大小（磅）
     */
    private void setFontSizeHalfPoint(XWPFRun run, double fontSize) {
        // 在Word中，字体大小以半磅为单位存储，所以10.5磅 = 21个半磅单位
        int halfPoints = (int) (fontSize * 2);

        if (run.getCTR() != null) {
            if (run.getCTR().getRPr() == null) {
                run.getCTR().addNewRPr();
            }

            // 设置字体大小（半磅单位）
            if (run.getCTR().getRPr().getSz() == null) {
                run.getCTR().getRPr().addNewSz();
            }
            run.getCTR().getRPr().getSz().setVal(BigInteger.valueOf(halfPoints));

            // 设置复杂脚本字体大小（用于中文等）
            if (run.getCTR().getRPr().getSzCs() == null) {
                run.getCTR().getRPr().addNewSzCs();
            }
            run.getCTR().getRPr().getSzCs().setVal(BigInteger.valueOf(halfPoints));
        }
    }
}



