package com.tunnel.service.impl;

import com.tunnel.common.core.domain.entity.SysUser;
import com.tunnel.common.utils.CollectUtil;
import com.tunnel.common.utils.FileUtil;
import com.tunnel.common.utils.SecurityUtils;
import com.tunnel.common.utils.StakeCodeUtil;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.common.utils.WordDocumentUtils;
import com.tunnel.domain.BatchAddResponse;
import com.tunnel.domain.Road;
import com.tunnel.domain.RoadCheckSRI;
import com.tunnel.mapper.RoadCheckSRIMapper;
import com.tunnel.mapper.RoadMapper;
import com.tunnel.service.CheckTeamService;
import com.tunnel.service.RoadCheckSRIService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge;
import org.springframework.core.io.ClassPathResource;

/**
 * 路面横向力系数检测信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Service
@Slf4j
public class RoadCheckSRIServiceImpl implements RoadCheckSRIService {
    @Autowired
    private RoadCheckSRIMapper roadCheckSRIMapper;

    @Autowired
    private RoadMapper roadMapper;

    @Autowired
    private CheckTeamService checkTeamService;

    // 每页导出数量
    private static final int PAGE_SIZE = 5000;

    // 导出线程池大小
    private static final int EXPORT_THREADS = 5;

    /**
     * 查询路面横向力系数检测信息
     *
     * @param id 路面横向力系数检测信息主键
     * @return 路面横向力系数检测信息
     */
    @Override
    public RoadCheckSRI selectRoadCheckSFCById(Long id) {
        return roadCheckSRIMapper.selectRoadCheckSFCById(id);
    }

    /**
     * 查询路面横向力系数检测信息列表
     *
     * @param roadCheckSRI 路面横向力系数检测信息
     * @return 路面横向力系数检测信息
     */
    @Override
    public List<RoadCheckSRI> selectRoadCheckSFCList(RoadCheckSRI roadCheckSRI) {
        return roadCheckSRIMapper.selectRoadCheckSFCList(roadCheckSRI);
    }

    /**
     * 新增路面横向力系数检测信息
     *
     * @param roadCheckSRI 路面横向力系数检测信息
     * @return 结果
     */
    @Override
    public int insertRoadCheckSFC(RoadCheckSRI roadCheckSRI) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        roadCheckSRI.setCreator(user.getUserId());
        roadCheckSRI.setModifier(user.getUserId());

        // 计算百米段和公里段
        if (roadCheckSRI.getStartCode() != null && !roadCheckSRI.getStartCode().isEmpty()) {
            roadCheckSRI.setHundredSection(StakeCodeUtil.calculateHundredSection(roadCheckSRI.getStartCode()));
            roadCheckSRI.setThousandSection(StakeCodeUtil.calculateThousandSection(roadCheckSRI.getStartCode()));
        }

        return roadCheckSRIMapper.insertRoadCheckSFC(roadCheckSRI);
    }

    /**
     * 修改路面横向力系数检测信息
     *
     * @param roadCheckSRI 路面横向力系数检测信息
     * @return 结果
     */
    @Override
    public int updateRoadCheckSFC(RoadCheckSRI roadCheckSRI) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        roadCheckSRI.setModifier(user.getUserId());

        // 计算百米段和公里段
        if (roadCheckSRI.getStartCode() != null && !roadCheckSRI.getStartCode().isEmpty()) {
            roadCheckSRI.setHundredSection(StakeCodeUtil.calculateHundredSection(roadCheckSRI.getStartCode()));
            roadCheckSRI.setThousandSection(StakeCodeUtil.calculateThousandSection(roadCheckSRI.getStartCode()));
        }

        return roadCheckSRIMapper.updateRoadCheckSFC(roadCheckSRI);
    }

    /**
     * 批量删除路面横向力系数检测信息
     *
     * @param ids 需要删除的路面横向力系数检测信息主键
     * @return 结果
     */
    @Override
    public int deleteRoadCheckSFCByIds(Long[] ids) {
        return roadCheckSRIMapper.deleteRoadCheckSFCByIds(ids);
    }

    /**
     * 删除路面横向力系数检测信息信息
     *
     * @param id 路面横向力系数检测信息主键
     * @return 结果
     */
    @Override
    public int deleteRoadCheckSFCById(Long id) {
        return roadCheckSRIMapper.deleteRoadCheckSFCById(id);
    }

    /**
     * 根据道路ID获取路面横向力系数检测信息
     *
     * @param roadId 道路ID
     * @return 路面横向力系数检测信息集合
     */
    @Override
    public List<RoadCheckSRI> selectRoadCheckSFCByRoadId(Long roadId) {
        return roadCheckSRIMapper.selectRoadCheckSFCByRoadId(roadId);
    }

    /**
     * 批量导入路面横向力系数检测数据
     * 支持多工作表格式：Sheet1为上行数据，Sheet2为下行数据
     *
     * @param file 导入文件
     * @param roadId 道路ID
     * @return 导入结果
     */
    @Override
    @Transactional
    public BatchAddResponse batchImport(MultipartFile file, Long roadId) {
        BatchAddResponse addResponse = new BatchAddResponse();
        InputStream fileStream = null;
        try {
            // 导入前先删除该道路的所有SRI数据
            int deletedCount = deleteRoadCheckSFCByRoadId(roadId);
            log.info("导入前删除道路ID {} 的SRI数据：{} 条", roadId, deletedCount);
            
            // 生成本地缓存路径
            String localFile = FileUtil.saveExcelFile(file);
            fileStream = new FileInputStream(localFile);
            BufferedInputStream bufferedInputStream = new BufferedInputStream(fileStream);

            // 读取Excel文件
            Workbook workbook;
            if (file.getOriginalFilename().endsWith(".xlsx")) {
                workbook = WorkbookFactory.create(bufferedInputStream);
            } else {
                addResponse.setStatus(1);
                addResponse.setMsg("请使用Excel 2007及以上版本的文件（.xlsx格式）！");
                addResponse.generateSummary();
                return addResponse;
            }

            List<RoadCheckSRI> resultList = new ArrayList<>();

            // 处理Sheet1（上行数据）
            Sheet sheet1 = workbook.getSheetAt(0);
            if (sheet1 != null) {
                List<RoadCheckSRI> upDirectionData = processSheetWithValidation(sheet1, roadId, "上行", "Sheet1", addResponse);
                resultList.addAll(upDirectionData);
                log.info("Sheet1(上行)处理数据：{} 条", upDirectionData.size());
            }

            // 处理Sheet2（下行数据）
            if (workbook.getNumberOfSheets() > 1) {
                Sheet sheet2 = workbook.getSheetAt(1);
                if (sheet2 != null) {
                    List<RoadCheckSRI> downDirectionData = processSheetWithValidation(sheet2, roadId, "下行", "Sheet2", addResponse);
                    resultList.addAll(downDirectionData);
                    log.info("Sheet2(下行)处理数据：{} 条", downDirectionData.size());
                }
            }

            // 根据roadType分组并处理段位信息
            if (!resultList.isEmpty()) {
                log.info("开始处理SRI数据的段位信息，总计 {} 条记录", resultList.size());
                StakeCodeUtil.processSectionsByRoadType(resultList);
            }

            // 只插入校验通过的数据
            if (!resultList.isEmpty()) {
                List<List<RoadCheckSRI>> splitList = CollectUtil.splitList(resultList, 1000);
                for (List<RoadCheckSRI> tempList : splitList) {
                    roadCheckSRIMapper.batchInsert(tempList);
                }
                addResponse.addSuccessCount(resultList.size());
                log.info("成功导入数据：{} 条", resultList.size());

                // 输出最后一行数据的段位信息用于验证
                if (!resultList.isEmpty()) {
                    RoadCheckSRI lastRecord = resultList.get(resultList.size() - 1);
                    log.info("导入数据最后一条记录段位信息：起始桩号={}, 结束桩号={}, 百米段={}, 公里段={}",
                            lastRecord.getStartCode(), lastRecord.getEndCode(),
                            lastRecord.getHundredSection(), lastRecord.getThousandSection());
                }
            }

            // 设置最终状态
            if (addResponse.getSuccessCount() > 0 && addResponse.getFailCount() == 0) {
                addResponse.setStatus(0);
                addResponse.setMsg("导入成功");
            } else if (addResponse.getSuccessCount() > 0 && addResponse.getFailCount() > 0) {
                addResponse.setStatus(0);
                addResponse.setMsg("部分导入成功");
            } else {
                addResponse.setStatus(1);
                addResponse.setMsg("导入失败");
            }

            addResponse.generateSummary();
            workbook.close();

        } catch (IOException e) {
            log.error("文件解析失败", e);
            addResponse.setStatus(1);
            addResponse.setMsg("文件解析失败: " + e.getMessage());
            addResponse.generateSummary();
        } catch (Exception e) {
            log.error("导入数据失败", e);
            addResponse.setStatus(1);
            addResponse.setMsg("导入数据失败: " + e.getMessage());
            addResponse.generateSummary();
        } finally {
            try {
                if (fileStream != null) {
                    fileStream.close();
                }
            } catch (IOException e) {
                log.error("excel文件读取失败, 失败原因：{}", e);
            }
        }
        return addResponse;
    }

    /**
     * 处理工作表的SRI数据（包含详细校验）
     *
     * @param sheet 工作表
     * @param roadId 道路ID
     * @param direction 行驶方向（上行/下行）
     * @param sheetName 工作表名称
     * @param addResponse 响应对象
     * @return 解析后的数据列表
     */
    private List<RoadCheckSRI> processSheetWithValidation(Sheet sheet, Long roadId, String direction,
                                                          String sheetName, BatchAddResponse addResponse) {
        List<RoadCheckSRI> resultList = new ArrayList<>();

        // 从第6行开始读取数据（索引从0开始，第6行对应索引5），跳过表头和标题行
        int startRow = 5;
        int rowCount = sheet.getPhysicalNumberOfRows();

        if (rowCount <= startRow) {
            log.warn("工作表 {} 中没有找到有效数据行", sheetName);
            return resultList;
        }

        // 将方向字符串转换为Integer值
        Integer directionValue = "上行".equals(direction) ? 1 : 2;

        // 遍历数据行
        for (int i = startRow; i < rowCount; i++) {
            Row row = sheet.getRow(i);
            if (row == null) {
                continue;
            }

            int excelRowNum = i + 1; // Excel行号从1开始
            boolean rowHasError = false;

            // 检查第一列是否为空（起始桩号）
            Cell stakeCell = row.getCell(0);
            if (stakeCell == null || getCellStringValue(stakeCell).trim().isEmpty()) {
                // 检查是否为空行
                boolean isEmptyRow = true;
                for (int j = 0; j < 10; j++) { // SRI有10列数据（包括路面类型）
                    Cell cell = row.getCell(j);
                    if (cell != null && !getCellStringValue(cell).trim().isEmpty()) {
                        isEmptyRow = false;
                        break;
                    }
                }
                if (!isEmptyRow) {
                    // 跳过前6行的错误报告（表头行）
                    if (excelRowNum > 6) {
                        addResponse.addError(sheetName, excelRowNum, "起始桩号", "起始桩号不能为空");
                    }
                    rowHasError = true;
                }
                continue;
            }

            try {
                RoadCheckSRI record = new RoadCheckSRI();

                // 校验和设置起始桩号
                String startCode = getCellStringValue(row.getCell(0));
                String endCode = getCellStringValue(row.getCell(2));

                if (startCode != null && !startCode.trim().isEmpty()) {
                    // 格式化桩号为标准格式
                    String formattedStartCode = StakeCodeUtil.formatStakeCode(startCode.trim());
                    record.setStartCode(formattedStartCode);

                    // 格式化结束桩号
                    String formattedEndCode = null;
                    if (endCode != null && !endCode.trim().isEmpty()) {
                        formattedEndCode = StakeCodeUtil.formatStakeCode(endCode.trim());
                        record.setEndCode(formattedEndCode);
                    }


                }

                // 校验和设置结束桩号
                if (endCode != null && !endCode.trim().isEmpty()) {
                    // 格式化桩号为标准格式
                    if (record.getEndCode() == null) {
                        String formattedEndCode = StakeCodeUtil.formatStakeCode(endCode.trim());
                        record.setEndCode(formattedEndCode);
                    }
                } else {
                    // 跳过前6行的错误报告（表头行）
                    if (excelRowNum > 6) {
                        addResponse.addError(sheetName, excelRowNum, "结束桩号", "结束桩号不能为空");
                    }
                    rowHasError = true;
                }

                // 校验各项检测数据
                BigDecimal testSpeed = validateAndGetDecimalValue(row.getCell(3), sheetName, excelRowNum, "测试速度", addResponse);
                if (testSpeed != null) record.setTestSpeed(testSpeed); else rowHasError = true;

                BigDecimal testTemp = validateAndGetDecimalValue(row.getCell(4), sheetName, excelRowNum, "测试温度", addResponse);
                if (testTemp != null) record.setTestTemp(testTemp); else rowHasError = true;

                BigDecimal sfcValue = validateAndGetDecimalValue(row.getCell(5), sheetName, excelRowNum, "SFC实测值", addResponse);
                if (sfcValue != null) record.setSfcValue(sfcValue); else rowHasError = true;

                BigDecimal sfc50 = validateAndGetDecimalValue(row.getCell(6), sheetName, excelRowNum, "SFC(50km/h)", addResponse);
                if (sfc50 != null) record.setSfc50(sfc50); else rowHasError = true;

                BigDecimal sfc50At20 = validateAndGetDecimalValue(row.getCell(7), sheetName, excelRowNum, "SFC(50km/h，20℃)", addResponse);
                if (sfc50At20 != null) record.setSfc50At20(sfc50At20); else rowHasError = true;

                // 设置备注信息（可选字段）（第9列，索引8）
                String remark = getCellStringValue(row.getCell(8));
                record.setRemark(remark);

                // 校验和设置路面类型（第10列，索引9）
                String roadType = getCellStringValue(row.getCell(9));
                if (roadType == null || roadType.trim().isEmpty()) {
                    roadType = "沥青路面"; // 默认值
                } else {
                    roadType = roadType.trim();
                }
                record.setRoadType(roadType);

                // 设置道路ID和方向
                record.setRoadId(roadId);
                record.setDirection(directionValue);

                // 设置创建者
                SysUser user = SecurityUtils.getLoginUser().getUser();
                record.setCreator(user.getUserId());

                // 只有没有错误的行才加入结果列表
                if (!rowHasError) {
                    resultList.add(record);
                }

            } catch (Exception e) {
                log.error("处理第{}行数据时发生错误", excelRowNum, e);
                addResponse.addError(sheetName, excelRowNum, "数据处理", "处理数据时发生错误：" + e.getMessage());
            }
        }
        return resultList;
    }

    /**
     * 校验并获取BigDecimal值
     */
    private BigDecimal validateAndGetDecimalValue(Cell cell, String sheetName, int rowNum,
                                                  String fieldName, BatchAddResponse addResponse) {
        if (cell == null) {
            // 跳过前6行的错误报告（表头行）
            if (rowNum > 6) {
                addResponse.addError(sheetName, rowNum, fieldName, fieldName + "不能为空");
            }
            return null;
        }

        String cellValue = getCellStringValue(cell);
        if (cellValue.trim().isEmpty()) {
            // 跳过前6行的错误报告（表头行）
            if (rowNum > 6) {
                addResponse.addError(sheetName, rowNum, fieldName, fieldName + "不能为空", cellValue);
            }
            return null;
        }
        return new BigDecimal(cellValue);
    }

    /**
     * 获取单元格的字符串值
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        try {
            switch (cell.getCellType()) {
                case STRING:
                    return cell.getStringCellValue().trim();
                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        return new SimpleDateFormat("yyyy-MM-dd").format(cell.getDateCellValue());
                    }
                    // 对于数字，转为字符串并去除小数点后的零
                    String value = String.valueOf(cell.getNumericCellValue());
                    if (value.endsWith(".0")) {
                        value = value.substring(0, value.length() - 2);
                    }
                    return value;
                case BOOLEAN:
                    return String.valueOf(cell.getBooleanCellValue());
                case FORMULA:
                    try {
                        return String.valueOf(cell.getNumericCellValue());
                    } catch (Exception e) {
                        return cell.getStringCellValue();
                    }
                default:
                    return "";
            }
        } catch (Exception e) {
            log.warn("获取单元格字符串值失败: {}", e.getMessage());
            return "";
        }
    }

    /**
     * 获取单元格的BigDecimal值
     */
    private BigDecimal getCellDecimalValue(Cell cell) {
        if (cell == null) {
            return null;
        }

        try {
            switch (cell.getCellType()) {
                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        return null;
                    }
                    // 对于数字，直接转为BigDecimal
                    return new BigDecimal(String.valueOf(cell.getNumericCellValue()));
                case STRING:
                    String strValue = cell.getStringCellValue().trim();
                    if (strValue.isEmpty()) {
                        return null;
                    }
                    try {
                        return new BigDecimal(strValue);
                    } catch (NumberFormatException e) {
                        return null;
                    }
                default:
                    return null;
            }
        } catch (Exception e) {
            log.warn("获取单元格数值值失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 优化导出路面横向力系数检测信息
     *
     * @param response HTTP响应
     * @param roadCheckSRI 查询条件
     */
    @Override
    public void exportOptimized(HttpServletResponse response, RoadCheckSRI roadCheckSRI) {
        List<RoadCheckSRI> list = roadCheckSRIMapper.selectRoadCheckSFCList(roadCheckSRI);
        ExcelUtil<RoadCheckSRI> util = new ExcelUtil<>(RoadCheckSRI.class);
        util.exportExcel(response, list, "路面横向力系数检测信息数据");
    }

    /**
     * 获取路面横向力系数检测信息记录数
     *
     * @param roadCheckSRI 查询条件
     * @return 记录数
     */
    @Override
    public int countRoadCheckSFC(RoadCheckSRI roadCheckSRI) {
        return roadCheckSRIMapper.countRoadCheckSFC(roadCheckSRI);
    }

    @Override
    public void exportSRIByDirection(HttpServletResponse response, Long roadId) {
        try {
            // 查询路线信息
            Road road = roadMapper.selectRoadById(roadId);
            if (road == null) {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500, \"msg\":\"路线信息不存在\"}");
                return;
            }

            String roadName = road.getRoadName();
            String roadCode = road.getRoadCode();
            String companyName = road.getCompanyName() != null ? road.getCompanyName() : "湖北交投智能检测股份有限公司";
            String projectName = road.getProjectName() != null ? road.getProjectName() : roadName;
            String startCode = road.getStartCode();
            String endCode = road.getEndCode();

            // 查询指定路线的SRI数据
            RoadCheckSRI query = new RoadCheckSRI();
            query.setRoadId(roadId);
            query.setDirection(1); // 上行
            List<RoadCheckSRI> upData = roadCheckSRIMapper.selectRoadCheckSFCList(query);

            query.setDirection(2); // 下行
            List<RoadCheckSRI> downData = roadCheckSRIMapper.selectRoadCheckSFCList(query);

            // 检查是否有数据可以导出
            if (CollectionUtils.isEmpty(upData) && CollectionUtils.isEmpty(downData)) {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500, \"msg\":\"没有SRI数据可以导出\"}");
                return;
            }

            // 创建新的工作簿
            SXSSFWorkbook workbook = new SXSSFWorkbook(100);

            // 创建上行数据Sheet
            if (!CollectionUtils.isEmpty(upData)) {
                Sheet upSheet = workbook.createSheet("上行十米");
                createSRIHeader(workbook, upSheet, roadName, roadCode, companyName, projectName, startCode, endCode, "上行");
                fillSRIData(upSheet, upData);
            }

            // 创建下行数据Sheet  
            if (!CollectionUtils.isEmpty(downData)) {
                Sheet downSheet = workbook.createSheet("下行十米");
                createSRIHeader(workbook, downSheet, roadName, roadCode, companyName, projectName, startCode, endCode, "下行");
                fillSRIData(downSheet, downData);
            }

            // 创建PBI百米汇总数据Sheet
            if (!CollectionUtils.isEmpty(upData)) {
                List<Map<String, Object>> upHundredData = groupByHundredSection(upData);
                Sheet upHundredSheet = workbook.createSheet("上行百米");
                createPBIHeader(workbook, upHundredSheet, "上行");
                fillPBIData(upHundredSheet, upHundredData);
            }

            if (!CollectionUtils.isEmpty(downData)) {
                List<Map<String, Object>> downHundredData = groupByHundredSection(downData);
                Sheet downHundredSheet = workbook.createSheet("下行百米");
                createPBIHeader(workbook, downHundredSheet, "下行");
                fillPBIData(downHundredSheet, downHundredData);
            }

            // 创建PBI公里汇总数据Sheet
            if (!CollectionUtils.isEmpty(upData)) {
                List<Map<String, Object>> upThousandData = groupByThousandSection(upData);
                Sheet upThousandSheet = workbook.createSheet("上行汇总表");
                createSRISummaryHeader(workbook, upThousandSheet, "上行");
                fillSRISummaryData(upThousandSheet, upThousandData);
            }

            if (!CollectionUtils.isEmpty(downData)) {
                List<Map<String, Object>> downThousandData = groupByThousandSection(downData);
                Sheet downThousandSheet = workbook.createSheet("下行汇总表");
                createSRISummaryHeader(workbook, downThousandSheet, "下行");
                fillSRISummaryData(downThousandSheet, downThousandData);
            }

            // 创建汇总表Sheet7 - 包含上下行数据对比
            if (!CollectionUtils.isEmpty(upData) || !CollectionUtils.isEmpty(downData)) {
                Sheet summarySheet = workbook.createSheet("汇总");
                createSummaryHeader(workbook, summarySheet, roadName, roadCode, startCode, endCode);
                fillSummaryData(summarySheet, upData, downData);
            }

            // 设置响应头
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String fileName = "路面横向力系数SFC检测记录_" + sdf.format(new Date()) + ".xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));

            // 写入响应流
            workbook.write(response.getOutputStream());
            workbook.dispose();

        } catch (Exception e) {
            log.error("导出SRI数据失败", e);
            try {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500, \"msg\":\"导出失败：" + e.getMessage() + "\"}");
            } catch (IOException ioException) {
                log.error("导出错误响应失败", ioException);
            }
        }
    }

    /**
     * 创建SRI Excel表头
     */
    private void createSRIHeader(SXSSFWorkbook workbook, Sheet sheet, String roadName, String roadCode,
                                 String companyName, String projectName, String startCode, String endCode, String direction) {
        // 创建单元格样式
        CellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        titleStyle.setBorderTop(BorderStyle.THIN);
        titleStyle.setBorderBottom(BorderStyle.THIN);
        titleStyle.setBorderLeft(BorderStyle.THIN);
        titleStyle.setBorderRight(BorderStyle.THIN);
        Font titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 14);
        titleFont.setFontName("宋体");
        titleStyle.setFont(titleFont);

        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontName("宋体");
        headerStyle.setFont(headerFont);

        CellStyle infoStyle = workbook.createCellStyle();
        infoStyle.setAlignment(HorizontalAlignment.LEFT);
        infoStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        infoStyle.setBorderTop(BorderStyle.THIN);
        infoStyle.setBorderBottom(BorderStyle.THIN);
        infoStyle.setBorderLeft(BorderStyle.THIN);
        infoStyle.setBorderRight(BorderStyle.THIN);
        Font infoFont = workbook.createFont();
        infoFont.setFontName("宋体");
        infoFont.setFontHeightInPoints((short) 10);
        infoStyle.setFont(infoFont);

        // 第1行：路面横向力系数（SFC)检测记录
        Row titleRow = sheet.createRow(0);
        titleRow.setHeightInPoints(25);
        // 为第1行的所有列创建单元格并应用样式
        for (int i = 0; i < 9; i++) {
            Cell cell = titleRow.createCell(i);
            if (i == 0) {
                cell.setCellValue("路面横向力系数（SFC)检测记录");
            }
            cell.setCellStyle(titleStyle);
        }
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 8)); // 合并A-I列

        // 第2行：项目名称和检测单位
        Row projectRow = sheet.createRow(1);
        projectRow.setHeightInPoints(20);
        // 为第2行的所有列创建单元格并应用样式
        for (int i = 0; i < 9; i++) {
            Cell cell = projectRow.createCell(i);
            if (i == 0) {
                cell.setCellValue("项目名称:" + projectName + "(" + roadCode + ")");
            } else if (i == 4) {
                cell.setCellValue("检测单位:" + companyName);
            }
            cell.setCellStyle(infoStyle);
        }
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, 3)); // 合并A-D列
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 4, 8)); // 合并E-I列

        // 第3行：检测项目和检测段落
        Row itemRow = sheet.createRow(2);
        itemRow.setHeightInPoints(20);
        // 为第3行的所有列创建单元格并应用样式
        for (int i = 0; i < 9; i++) {
            Cell cell = itemRow.createCell(i);
            if (i == 0) {
                cell.setCellValue("检测项目：路面横向力系数");
            } else if (i == 4) {
                cell.setCellValue("检测段落：" + startCode + "-" + endCode + direction + "行车道");
            }
            cell.setCellStyle(infoStyle);
        }
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 0, 3)); // 合并A-D列
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 4, 8)); // 合并E-I列

        // 第4行：表头第一行
        Row header1Row = sheet.createRow(3);
        header1Row.setHeightInPoints(30);

        // 为第4行的所有列创建单元格并应用样式
        Cell stakeHeader1 = header1Row.createCell(0);
        stakeHeader1.setCellValue("桩号");
        stakeHeader1.setCellStyle(headerStyle);

        // 创建并设置其他单元格的样式
        for (int i = 1; i < 9; i++) {
            Cell cell = header1Row.createCell(i);
            cell.setCellStyle(headerStyle);
            switch (i) {
                case 3:
                    cell.setCellValue("测试速度");
                    break;
                case 4:
                    cell.setCellValue("测试温度");
                    break;
                case 5:
                    cell.setCellValue("SFC");
                    break;
                case 8:
                    cell.setCellValue("备注");
                    break;
                default:
                    break;
            }
        }

        // 添加合并区域
        sheet.addMergedRegion(new CellRangeAddress(3, 4, 0, 2)); // 桩号列合并A-C列
        sheet.addMergedRegion(new CellRangeAddress(3, 4, 3, 3)); // 测试速度列
        sheet.addMergedRegion(new CellRangeAddress(3, 4, 4, 4)); // 测试温度列
        sheet.addMergedRegion(new CellRangeAddress(3, 3, 5, 7)); // SFC主标题
        sheet.addMergedRegion(new CellRangeAddress(3, 4, 8, 8)); // 备注列

        // 第5行：表头第二行
        Row header2Row = sheet.createRow(4);
        header2Row.setHeightInPoints(30);

        // 为第5行的所有列创建单元格并应用样式
        for (int i = 0; i < 9; i++) {
            Cell cell = header2Row.createCell(i);
            cell.setCellStyle(headerStyle);
            switch (i) {
                case 5:
                    cell.setCellValue("SFC实测");
                    break;
                case 6:
                    cell.setCellValue("SFC(50km/h)");
                    break;
                case 7:
                    cell.setCellValue("SFC(50km/h，20℃)");
                    break;
                default:
                    // 其他单元格保持空白但有边框
                    break;
            }
        }

        // 设置列宽
        sheet.setColumnWidth(0, 4000);  // 桩号列
        sheet.setColumnWidth(1, 1000);  // ~ 列
        sheet.setColumnWidth(2, 4000);  // 桩号列
        sheet.setColumnWidth(3, 3000);  // 测试速度
        sheet.setColumnWidth(4, 3000);  // 测试温度
        sheet.setColumnWidth(5, 3000);  // SFC实测
        sheet.setColumnWidth(6, 4000);  // SFC(50km/h)
        sheet.setColumnWidth(7, 5000);  // SFC(50km/h，20℃)
        sheet.setColumnWidth(8, 4000);  // 备注
    }

    /**
     * 填充SRI数据
     */
    private void fillSRIData(Sheet sheet, List<RoadCheckSRI> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }

        // 获取工作簿引用以创建数据样式
        SXSSFWorkbook workbook = (SXSSFWorkbook) sheet.getWorkbook();

        // 创建数据行样式
        CellStyle dataStyle = workbook.createCellStyle();
        dataStyle.setAlignment(HorizontalAlignment.CENTER);
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);
        Font dataFont = workbook.createFont();
        dataFont.setFontName("宋体");
        dataFont.setFontHeightInPoints((short) 10);
        dataStyle.setFont(dataFont);

        // 创建两种数值样式（整数和小数），避免重复创建
        CellStyle integerStyle = workbook.createCellStyle();
        integerStyle.setAlignment(HorizontalAlignment.CENTER);
        integerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        integerStyle.setBorderTop(BorderStyle.THIN);
        integerStyle.setBorderBottom(BorderStyle.THIN);
        integerStyle.setBorderLeft(BorderStyle.THIN);
        integerStyle.setBorderRight(BorderStyle.THIN);
        integerStyle.setDataFormat(workbook.createDataFormat().getFormat("0"));
        Font intFont = workbook.createFont();
        intFont.setFontName("Times New Roman");
        intFont.setFontHeightInPoints((short) 10);
        integerStyle.setFont(intFont);

        CellStyle decimalStyle = workbook.createCellStyle();
        decimalStyle.setAlignment(HorizontalAlignment.CENTER);
        decimalStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        decimalStyle.setBorderTop(BorderStyle.THIN);
        decimalStyle.setBorderBottom(BorderStyle.THIN);
        decimalStyle.setBorderLeft(BorderStyle.THIN);
        decimalStyle.setBorderRight(BorderStyle.THIN);
        decimalStyle.setDataFormat(workbook.createDataFormat().getFormat("0.###"));
        Font decFont = workbook.createFont();
        decFont.setFontName("Times New Roman");
        decFont.setFontHeightInPoints((short) 10);
        decimalStyle.setFont(decFont);

        int rowIndex = 5; // 从第6行开始填充数据
        for (RoadCheckSRI record : dataList) {
            Row dataRow = sheet.createRow(rowIndex++);
            int colIndex = 0;

            // A列：起始桩号
            Cell startCodeCell = dataRow.createCell(colIndex++);
            startCodeCell.setCellValue(record.getStartCode() != null ? record.getStartCode() : "");
            startCodeCell.setCellStyle(dataStyle);

            // B列：~符号
            Cell tildeCell = dataRow.createCell(colIndex++);
            tildeCell.setCellValue("~");
            tildeCell.setCellStyle(dataStyle);

            // C列：结束桩号
            Cell endCodeCell = dataRow.createCell(colIndex++);
            endCodeCell.setCellValue(record.getEndCode() != null ? record.getEndCode() : "");
            endCodeCell.setCellStyle(dataStyle);

            // D列：测试速度
            Cell testSpeedCell = dataRow.createCell(colIndex++);
            if (record.getTestSpeed() != null) {
                testSpeedCell.setCellValue(record.getTestSpeed().doubleValue());
                testSpeedCell.setCellStyle(isInteger(record.getTestSpeed()) ? integerStyle : decimalStyle);
            } else {
                testSpeedCell.setCellValue(0.0);
                testSpeedCell.setCellStyle(integerStyle);
            }

            // E列：测试温度
            Cell testTempCell = dataRow.createCell(colIndex++);
            if (record.getTestTemp() != null) {
                testTempCell.setCellValue(record.getTestTemp().doubleValue());
                testTempCell.setCellStyle(isInteger(record.getTestTemp()) ? integerStyle : decimalStyle);
            } else {
                testTempCell.setCellValue(0.0);
                testTempCell.setCellStyle(integerStyle);
            }

            // F列：SFC实测值
            Cell sfcValueCell = dataRow.createCell(colIndex++);
            if (record.getSfcValue() != null) {
                sfcValueCell.setCellValue(record.getSfcValue().doubleValue());
                sfcValueCell.setCellStyle(isInteger(record.getSfcValue()) ? integerStyle : decimalStyle);
            } else {
                sfcValueCell.setCellValue(0.0);
                sfcValueCell.setCellStyle(integerStyle);
            }

            // G列：SFC(50km/h)
            Cell sfc50Cell = dataRow.createCell(colIndex++);
            if (record.getSfc50() != null) {
                sfc50Cell.setCellValue(record.getSfc50().doubleValue());
                sfc50Cell.setCellStyle(isInteger(record.getSfc50()) ? integerStyle : decimalStyle);
            } else {
                sfc50Cell.setCellValue(0.0);
                sfc50Cell.setCellStyle(integerStyle);
            }

            // H列：SFC(50km/h，20℃)
            Cell sfc50At20Cell = dataRow.createCell(colIndex++);
            if (record.getSfc50At20() != null) {
                sfc50At20Cell.setCellValue(record.getSfc50At20().doubleValue());
                sfc50At20Cell.setCellStyle(isInteger(record.getSfc50At20()) ? integerStyle : decimalStyle);
            } else {
                sfc50At20Cell.setCellValue(0.0);
                sfc50At20Cell.setCellStyle(integerStyle);
            }

            // I列：备注
            Cell remarkCell = dataRow.createCell(colIndex);
            remarkCell.setCellValue(record.getRemark() != null ? record.getRemark() : "");
            remarkCell.setCellStyle(dataStyle);
        }
    }

    /**
     * 判断BigDecimal是否为整数
     */
    private boolean isInteger(java.math.BigDecimal value) {
        return value != null && value.stripTrailingZeros().scale() <= 0;
    }

    @Override
    public void exportWordSRIByDirection(HttpServletResponse response, Long roadId) {
        exportWordSRIByDirection(response, roadId, null, null, null, null, null, null);
    }

    @Override
    public void exportWordSRIByDirection(HttpServletResponse response, Long roadId, Long teamId, String dateTime, String monthDate, String titleName, String checkName, String reviewName) {
        try {
            // 查询指定路线的SRI数据
            RoadCheckSRI query = new RoadCheckSRI();
            query.setRoadId(roadId);
            query.setDirection(1); // 上行
            List<RoadCheckSRI> upData = roadCheckSRIMapper.selectRoadCheckSFCList(query);

            query.setDirection(2); // 下行
            List<RoadCheckSRI> downData = roadCheckSRIMapper.selectRoadCheckSFCList(query);

            // 检查是否有数据可以导出
            if (CollectionUtils.isEmpty(upData) && CollectionUtils.isEmpty(downData)) {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500, \"msg\":\"没有SRI数据可以导出\"}");
                return;
            }

            Road road = roadMapper.selectRoadById(roadId);
            if (Objects.isNull(road)) {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500, \"msg\":\"路线不存在\"}");
                return;
            }
            // 合并上行和下行数据来获取完整的桩号范围
            List<RoadCheckSRI> allData = new ArrayList<>();
            if (upData != null && !upData.isEmpty()) allData.addAll(upData);
            if (downData != null && !downData.isEmpty()) allData.addAll(downData);
            // 生成上行和下行两个独立的Word文档，并打包成ZIP
            generateSeparateSRIWordDocuments(response, upData, downData, road, teamId, dateTime, monthDate, titleName, checkName, reviewName);

        } catch (Exception e) {
            log.error("导出SRI Word报告失败", e);
            try {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500, \"msg\":\"导出失败：" + e.getMessage() + "\"}");
            } catch (IOException ioException) {
                log.error("导出错误响应失败", ioException);
            }
        }
    }

    /**
     * 按百米段分组SRI数据
     */
    private List<Map<String, Object>> groupByHundredSection(List<RoadCheckSRI> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return new ArrayList<>();
        }

        // 按hundred_section分组
        Map<String, List<RoadCheckSRI>> groupedData = dataList.stream()
                .filter(record -> record.getHundredSection() != null && !record.getHundredSection().trim().isEmpty())
                .collect(java.util.stream.Collectors.groupingBy(RoadCheckSRI::getHundredSection));

        List<Map<String, Object>> result = new ArrayList<>();

        for (Map.Entry<String, List<RoadCheckSRI>> entry : groupedData.entrySet()) {
            String hundredSection = entry.getKey();
            List<RoadCheckSRI> records = entry.getValue();

            if (records.isEmpty()) {
                continue;
            }

            // 按桩号排序
            records.sort((a, b) -> {
                if (a.getStartCode() == null) return 1;
                if (b.getStartCode() == null) return -1;
                return a.getStartCode().compareTo(b.getStartCode());
            });

            Map<String, Object> summary = new HashMap<>();
            
            // 获取起始和结束桩号
            String startCode = records.get(0).getStartCode();
            String endCode = records.get(records.size() - 1).getEndCode();
            
            // 获取路面类型（使用第一条记录的路面类型）
            String roadType = records.get(0).getRoadType() != null ? records.get(0).getRoadType() : "沥青路面";
            
            // 计算段长度（米）
            double length = calculateSectionDistance(startCode, endCode);
            
            // 计算sfc50At20的平均值
            double avgSfc50At20 = records.stream()
                    .filter(r -> r.getSfc50At20() != null)
                    .mapToDouble(r -> r.getSfc50At20().doubleValue())
                    .average()
                    .orElse(0.0);
            
            // 计算SRI值：(100-35)/(1+28.6*EXP(-0.105*E列))+35
            double sri = calculateSRI(avgSfc50At20);
            
            // 计算等级
            String grade = calculateSRIGrade(sri);
            
            // 获取备注（使用第一条记录的备注）
            String remark = records.get(0).getRemark();
            
            summary.put("startCode", startCode);
            summary.put("endCode", endCode);
            summary.put("roadType", roadType);
            summary.put("length", length);
            summary.put("avgSfc50At20", avgSfc50At20);
            summary.put("sri", sri);
            summary.put("grade", grade);
            summary.put("remark", remark);
            
            result.add(summary);
        }

        // 按起始桩号排序
        result.sort((a, b) -> {
            String startA = (String) a.get("startCode");
            String startB = (String) b.get("startCode");
            if (startA == null) return 1;
            if (startB == null) return -1;
            return startA.compareTo(startB);
        });

        return result;
    }

    /**
     * 计算段位距离（米）
     */
    private double calculateSectionDistance(String startCode, String endCode) {
        if (startCode == null || endCode == null) {
            return 100.0; // 默认百米
        }

        try {
            long startMeters = StakeCodeUtil.parseStakeCodeToMeters(startCode);
            long endMeters = StakeCodeUtil.parseStakeCodeToMeters(endCode);
            
            if (startMeters >= 0 && endMeters >= 0 && endMeters > startMeters) {
                return endMeters - startMeters;
            }
        } catch (Exception e) {
            log.warn("计算段位距离失败，起始桩号: {}, 结束桩号: {}", startCode, endCode);
        }

        return 100.0; // 默认百米
    }

    /**
     * 计算SRI值：(100-35)/(1+28.6*EXP(-0.105*E列))+35
     */
    private double calculateSRI(double sfc50At20) {
        return (100.0 - 35.0) / (1.0 + 28.6 * Math.exp(-0.105 * sfc50At20)) + 35.0;
    }

    /**
     * 根据SRI值计算等级
     */
    private String calculateSRIGrade(double sri) {
        if (sri >= 90) {
            return "优";
        } else if (sri >= 80) {
            return "良";
        } else if (sri >= 70) {
            return "中";
        } else if (sri >= 60) {
            return "次";
        } else {
            return "差";
        }
    }

    /**
     * 创建PBI Excel表头
     */
    private void createPBIHeader(SXSSFWorkbook workbook, Sheet sheet, String direction) {
        // 创建单元格样式
        CellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        titleStyle.setBorderTop(BorderStyle.THIN);
        titleStyle.setBorderBottom(BorderStyle.THIN);
        titleStyle.setBorderLeft(BorderStyle.THIN);
        titleStyle.setBorderRight(BorderStyle.THIN);
        Font titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 14);
        titleFont.setFontName("宋体");
        titleStyle.setFont(titleFont);

        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontName("宋体");
        headerStyle.setFont(headerFont);

        // 第1行：标题
        Row titleRow = sheet.createRow(0);
        titleRow.setHeightInPoints(25);
        for (int i = 0; i < 9; i++) {
            Cell cell = titleRow.createCell(i);
            if (i == 0) {
                cell.setCellValue("路面抗滑性能指数（SRI）汇总表（" + direction + "）");
            }
            cell.setCellStyle(titleStyle);
        }
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 8)); // 合并A-I列

        // 第2-3行：合并后的表头
        Row header1Row = sheet.createRow(1);
        header1Row.setHeightInPoints(30);
        Row header2Row = sheet.createRow(2);
        header2Row.setHeightInPoints(30);
        
        // 创建表头内容
        String[] headers = {"桩号", "路面类型", "长度", "横向力系数（SFC）", "抗滑性能指数（SRI）", "抗滑性能等级", "备注"};
        
        // A-C列：桩号
        for (int i = 0; i < 3; i++) {
            Cell cell1 = header1Row.createCell(i);
            Cell cell2 = header2Row.createCell(i);
            if (i == 0) {
                cell1.setCellValue("桩号");
            }
            cell1.setCellStyle(headerStyle);
            cell2.setCellStyle(headerStyle);
        }
        
        // D-I列：其他表头
        for (int i = 3; i < 9; i++) {
            Cell cell1 = header1Row.createCell(i);
            Cell cell2 = header2Row.createCell(i);
            cell1.setCellValue(headers[i - 2]);
            cell1.setCellStyle(headerStyle);
            cell2.setCellStyle(headerStyle);
        }

        // 合并单元格
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 0, 2)); // A-C列：桩号
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 3, 3)); // D列：路面类型
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 4, 4)); // E列：长度
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 5, 5)); // F列：横向力系数（SFC）
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 6, 6)); // G列：抗滑性能指数（SRI）
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 7, 7)); // H列：抗滑性能等级
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 8, 8)); // I列：备注

        // 设置列宽
        sheet.setColumnWidth(0, 4000);  // 起点桩号
        sheet.setColumnWidth(1, 1000);  // ~ 符号
        sheet.setColumnWidth(2, 4000);  // 终点桩号
        sheet.setColumnWidth(3, 3000);  // 路面类型
        sheet.setColumnWidth(4, 2500);  // 长度
        sheet.setColumnWidth(5, 4000);  // 横向力系数（SFC）
        sheet.setColumnWidth(6, 2500);  // 抗滑性能指数（SRI）
        sheet.setColumnWidth(7, 2500);  // 抗滑性能等级
        sheet.setColumnWidth(8, 4000);  // 备注
    }

    /**
     * 填充PBI数据
     */
    private void fillPBIData(Sheet sheet, List<Map<String, Object>> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }

        // 获取工作簿引用以创建数据样式
        SXSSFWorkbook workbook = (SXSSFWorkbook) sheet.getWorkbook();

        // 创建数据行样式
        CellStyle dataStyle = workbook.createCellStyle();
        dataStyle.setAlignment(HorizontalAlignment.CENTER);
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);
        Font dataFont = workbook.createFont();
        dataFont.setFontName("宋体");
        dataFont.setFontHeightInPoints((short) 10);
        dataStyle.setFont(dataFont);

        // 创建数值样式（保留2位小数）
        CellStyle numberStyle = workbook.createCellStyle();
        numberStyle.setAlignment(HorizontalAlignment.CENTER);
        numberStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        numberStyle.setBorderTop(BorderStyle.THIN);
        numberStyle.setBorderBottom(BorderStyle.THIN);
        numberStyle.setBorderLeft(BorderStyle.THIN);
        numberStyle.setBorderRight(BorderStyle.THIN);
        numberStyle.setDataFormat(workbook.createDataFormat().getFormat("0.00"));
        Font numFont = workbook.createFont();
        numFont.setFontName("Times New Roman");
        numFont.setFontHeightInPoints((short) 10);
        numberStyle.setFont(numFont);

        // 创建整数样式（E、F列专用）
        CellStyle integerStyle = workbook.createCellStyle();
        integerStyle.setAlignment(HorizontalAlignment.CENTER);
        integerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        integerStyle.setBorderTop(BorderStyle.THIN);
        integerStyle.setBorderBottom(BorderStyle.THIN);
        integerStyle.setBorderLeft(BorderStyle.THIN);
        integerStyle.setBorderRight(BorderStyle.THIN);
        integerStyle.setDataFormat(workbook.createDataFormat().getFormat("0"));
        Font intFont2 = workbook.createFont();
        intFont2.setFontName("Times New Roman");
        intFont2.setFontHeightInPoints((short) 10);
        integerStyle.setFont(intFont2);

        int rowIndex = 3; // 从第4行开始填充数据（第1行标题，第2-3行表头）
        for (Map<String, Object> record : dataList) {
            Row dataRow = sheet.createRow(rowIndex++);
            int colIndex = 0;

            // A列：起始桩号
            Cell startCodeCell = dataRow.createCell(colIndex++);
            startCodeCell.setCellValue((String) record.get("startCode"));
            startCodeCell.setCellStyle(dataStyle);

            // B列：~符号
            Cell tildeCell = dataRow.createCell(colIndex++);
            tildeCell.setCellValue("~");
            tildeCell.setCellStyle(dataStyle);

            // C列：结束桩号
            Cell endCodeCell = dataRow.createCell(colIndex++);
            endCodeCell.setCellValue((String) record.get("endCode"));
            endCodeCell.setCellStyle(dataStyle);

            // D列：路面类型
            Cell roadTypeCell = dataRow.createCell(colIndex++);
            roadTypeCell.setCellValue((String) record.get("roadType"));
            roadTypeCell.setCellStyle(dataStyle);

            // E列：段长度
            Cell lengthCell = dataRow.createCell(colIndex++);
            lengthCell.setCellValue((Double) record.get("length"));
            lengthCell.setCellStyle(integerStyle);

            // F列：SFC(50km/h，20℃)平均值 - 四舍五入为整数
            Cell avgSfc50At20Cell = dataRow.createCell(colIndex++);
            double avgSfc50At20 = (Double) record.get("avgSfc50At20");
            avgSfc50At20Cell.setCellValue(Math.round(avgSfc50At20));
            avgSfc50At20Cell.setCellStyle(integerStyle);

            // G列：SRI值
            Cell sriCell = dataRow.createCell(colIndex++);
            sriCell.setCellValue((Double) record.get("sri"));
            sriCell.setCellStyle(numberStyle);

            // H列：等级
            Cell gradeCell = dataRow.createCell(colIndex++);
            gradeCell.setCellValue((String) record.get("grade"));
            gradeCell.setCellStyle(dataStyle);

            // I列：备注
            Cell remarkCell = dataRow.createCell(colIndex);
            remarkCell.setCellValue((String) record.get("remark"));
            remarkCell.setCellStyle(dataStyle);
        }
    }

    /**
     * 填充SRI数据
     */
    private void fillSRISummaryData(Sheet sheet, List<Map<String, Object>> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }

        // 获取工作簿引用以创建数据样式
        SXSSFWorkbook workbook = (SXSSFWorkbook) sheet.getWorkbook();

        // 创建数据行样式
        CellStyle dataStyle = workbook.createCellStyle();
        dataStyle.setAlignment(HorizontalAlignment.CENTER);
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);
        Font dataFont = workbook.createFont();
        dataFont.setFontName("宋体");
        dataFont.setFontHeightInPoints((short) 10);
        dataStyle.setFont(dataFont);

        // 创建数值样式（保留2位小数）
        CellStyle numberStyle = workbook.createCellStyle();
        numberStyle.setAlignment(HorizontalAlignment.CENTER);
        numberStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        numberStyle.setBorderTop(BorderStyle.THIN);
        numberStyle.setBorderBottom(BorderStyle.THIN);
        numberStyle.setBorderLeft(BorderStyle.THIN);
        numberStyle.setBorderRight(BorderStyle.THIN);
        numberStyle.setDataFormat(workbook.createDataFormat().getFormat("0.00"));
        Font numFont = workbook.createFont();
        numFont.setFontName("Times New Roman");
        numFont.setFontHeightInPoints((short) 10);
        numberStyle.setFont(numFont);

        // 创建整数样式（E、F列专用）
        CellStyle integerStyle = workbook.createCellStyle();
        integerStyle.setAlignment(HorizontalAlignment.CENTER);
        integerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        integerStyle.setBorderTop(BorderStyle.THIN);
        integerStyle.setBorderBottom(BorderStyle.THIN);
        integerStyle.setBorderLeft(BorderStyle.THIN);
        integerStyle.setBorderRight(BorderStyle.THIN);
        integerStyle.setDataFormat(workbook.createDataFormat().getFormat("0"));
        Font intFont3 = workbook.createFont();
        intFont3.setFontName("Times New Roman");
        intFont3.setFontHeightInPoints((short) 10);
        integerStyle.setFont(intFont3);

        int rowIndex = 3; // 从第4行开始填充数据（第1行标题，第2-3行表头）
        for (Map<String, Object> record : dataList) {
            Row dataRow = sheet.createRow(rowIndex++);
            int colIndex = 0;

            // A列：起始桩号
            Cell startCodeCell = dataRow.createCell(colIndex++);
            startCodeCell.setCellValue((String) record.get("startCode"));
            startCodeCell.setCellStyle(dataStyle);

            // B列：~符号
            Cell tildeCell = dataRow.createCell(colIndex++);
            tildeCell.setCellValue("~");
            tildeCell.setCellStyle(dataStyle);

            // C列：结束桩号
            Cell endCodeCell = dataRow.createCell(colIndex++);
            endCodeCell.setCellValue((String) record.get("endCode"));
            endCodeCell.setCellStyle(dataStyle);

            // D列：路面类型
            Cell roadTypeCell = dataRow.createCell(colIndex++);
            roadTypeCell.setCellValue((String) record.get("roadType"));
            roadTypeCell.setCellStyle(dataStyle);

            // E列：段长度
            Cell lengthCell = dataRow.createCell(colIndex++);
            lengthCell.setCellValue((Double) record.get("length"));
            lengthCell.setCellStyle(integerStyle);

            // F列：SFC(50km/h，20℃)平均值 - 四舍五入为整数
            Cell avgSfc50At20Cell = dataRow.createCell(colIndex++);
            double avgSfc50At20 = (Double) record.get("avgSfc50At20");
            avgSfc50At20Cell.setCellValue(Math.round(avgSfc50At20));
            avgSfc50At20Cell.setCellStyle(integerStyle);

            // G列：SRI值
            Cell sriCell = dataRow.createCell(colIndex++);
            sriCell.setCellValue((Double) record.get("sri"));
            sriCell.setCellStyle(numberStyle);

            // H列：等级
            Cell gradeCell = dataRow.createCell(colIndex++);
            gradeCell.setCellValue((String) record.get("grade"));
            gradeCell.setCellStyle(dataStyle);

            // I列：备注
            Cell remarkCell = dataRow.createCell(colIndex);
            remarkCell.setCellValue((String) record.get("remark"));
            remarkCell.setCellStyle(dataStyle);
        }

        // 添加统计信息的6行
        addStatisticsRows(sheet, dataList, rowIndex, dataStyle, numberStyle);
    }

    /**
     * 添加统计信息的6行
     */
    private void addStatisticsRows(Sheet sheet, List<Map<String, Object>> dataList, int startRowIndex, 
                                  CellStyle dataStyle, CellStyle numberStyle) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }

        // 计算统计数据
        double totalLength = dataList.stream()
                .mapToDouble(record -> (Double) record.get("length"))
                .sum();

        double avgSfc = dataList.stream()
                .mapToDouble(record -> (Double) record.get("avgSfc50At20"))
                .average()
                .orElse(0.0);

        double avgSri = dataList.stream()
                .mapToDouble(record -> (Double) record.get("sri"))
                .average()
                .orElse(0.0);

        // 计算各等级的里程
        double excellentGoodLength = dataList.stream()
                .filter(record -> {
                    String grade = (String) record.get("grade");
                    return "优".equals(grade) || "良".equals(grade);
                })
                .mapToDouble(record -> (Double) record.get("length"))
                .sum();

        double mediumLength = dataList.stream()
                .filter(record -> "中".equals((String) record.get("grade")))
                .mapToDouble(record -> (Double) record.get("length"))
                .sum();

        double poorLength = dataList.stream()
                .filter(record -> {
                    String grade = (String) record.get("grade");
                    return "次".equals(grade) || "差".equals(grade);
                })
                .mapToDouble(record -> (Double) record.get("length"))
                .sum();

        // 统计信息数据
        String[][] statistics = {
                {"总长度（m）", String.format("%.2f", totalLength)},
                {"SFC平均值", String.valueOf(Math.round(avgSfc))},
                {"SRI平均值", String.format("%.2f", avgSri)},
                {"SRI（优、良）", String.format("%.2f", excellentGoodLength)},
                {"SRI（中）", String.format("%.2f", mediumLength)},
                {"SRI（次、差）", String.format("%.2f", poorLength)}
        };

        // 添加统计行
        for (int i = 0; i < statistics.length; i++) {
            Row statRow = sheet.createRow(startRowIndex + i);

            // A-C列合并：显示"统计信息"
            for (int j = 0; j < 3; j++) {
                Cell cell = statRow.createCell(j);
                if (j == 0) {
                    cell.setCellValue("统计信息");
                }
                cell.setCellStyle(dataStyle);
            }
            if (i == 0) {
                sheet.addMergedRegion(new CellRangeAddress(startRowIndex, startRowIndex + 5, 0, 2));
            }

            // D-G列合并：显示统计项目名称
            for (int j = 3; j < 7; j++) {
                Cell cell = statRow.createCell(j);
                if (j == 3) {
                    cell.setCellValue(statistics[i][0]);
                }
                cell.setCellStyle(dataStyle);
            }
            sheet.addMergedRegion(new CellRangeAddress(startRowIndex + i, startRowIndex + i, 3, 6));

            // H-I列合并：显示统计值
            for (int j = 7; j < 9; j++) {
                Cell cell = statRow.createCell(j);
                if (j == 7) {
                    cell.setCellValue(statistics[i][1]);
                }
                cell.setCellStyle(numberStyle);
            }
            sheet.addMergedRegion(new CellRangeAddress(startRowIndex + i, startRowIndex + i, 7, 8));
        }
    }

    /**
     * 按公里段分组SRI数据
     */
    private List<Map<String, Object>> groupByThousandSection(List<RoadCheckSRI> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return new ArrayList<>();
        }

        // 按thousand_section分组
        Map<String, List<RoadCheckSRI>> groupedData = dataList.stream()
                .filter(record -> record.getThousandSection() != null && !record.getThousandSection().trim().isEmpty())
                .collect(java.util.stream.Collectors.groupingBy(RoadCheckSRI::getThousandSection));

        List<Map<String, Object>> result = new ArrayList<>();

        for (Map.Entry<String, List<RoadCheckSRI>> entry : groupedData.entrySet()) {
            String thousandSection = entry.getKey();
            List<RoadCheckSRI> records = entry.getValue();

            if (records.isEmpty()) {
                continue;
            }

            // 按桩号排序
            records.sort((a, b) -> {
                if (a.getStartCode() == null) return 1;
                if (b.getStartCode() == null) return -1;
                return a.getStartCode().compareTo(b.getStartCode());
            });

            Map<String, Object> summary = new HashMap<>();
            
            // 获取起始和结束桩号
            String startCode = records.get(0).getStartCode();
            String endCode = records.get(records.size() - 1).getEndCode();
            
            // 获取路面类型（使用第一条记录的路面类型）
            String roadType = records.get(0).getRoadType() != null ? records.get(0).getRoadType() : "沥青路面";
            
            // 计算段长度（米）
            double length = calculateSectionDistance(startCode, endCode);
            
            // 计算sfc50At20的平均值
            double avgSfc50At20 = records.stream()
                    .filter(r -> r.getSfc50At20() != null)
                    .mapToDouble(r -> r.getSfc50At20().doubleValue())
                    .average()
                    .orElse(0.0);
            
            // 计算SRI值：(100-35)/(1+28.6*EXP(-0.105*E列))+35
            double sri = calculateSRI(avgSfc50At20);
            
            // 计算等级
            String grade = calculateSRIGrade(sri);
            
            // 获取备注（使用第一条记录的备注）
            String remark = records.get(0).getRemark();
            
            summary.put("startCode", startCode);
            summary.put("endCode", endCode);
            summary.put("roadType", roadType);
            summary.put("length", length);
            summary.put("avgSfc50At20", avgSfc50At20);
            summary.put("sri", sri);
            summary.put("grade", grade);
            summary.put("remark", remark);
            
            result.add(summary);
        }

        // 按起始桩号排序
        result.sort((a, b) -> {
            String startA = (String) a.get("startCode");
            String startB = (String) b.get("startCode");
            if (startA == null) return 1;
            if (startB == null) return -1;
            return startA.compareTo(startB);
        });

        return result;
    }

    /**
     * 创建SRI Excel表头
     */
    private void createSRISummaryHeader(SXSSFWorkbook workbook, Sheet sheet, String direction) {
        // 创建单元格样式
        CellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        titleStyle.setBorderTop(BorderStyle.THIN);
        titleStyle.setBorderBottom(BorderStyle.THIN);
        titleStyle.setBorderLeft(BorderStyle.THIN);
        titleStyle.setBorderRight(BorderStyle.THIN);
        Font titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 14);
        titleFont.setFontName("宋体");
        titleStyle.setFont(titleFont);

        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontName("宋体");
        headerStyle.setFont(headerFont);

        // 第1行：标题
        Row titleRow = sheet.createRow(0);
        titleRow.setHeightInPoints(25);
        for (int i = 0; i < 9; i++) {
            Cell cell = titleRow.createCell(i);
            if (i == 0) {
                cell.setCellValue("路面抗滑性能指数（SRI）汇总表（" + direction + "）");
            }
            cell.setCellStyle(titleStyle);
        }
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 8)); // 合并A-I列

        // 第2-3行：合并后的表头
        Row header1Row = sheet.createRow(1);
        header1Row.setHeightInPoints(30);
        Row header2Row = sheet.createRow(2);
        header2Row.setHeightInPoints(30);
        
        // 创建表头内容
        String[] headers = {"桩号", "路面类型", "长度", "横向力系数（SFC）", "抗滑性能指数（SRI）", "抗滑性能等级", "备注"};
        
        // A-C列：桩号
        for (int i = 0; i < 3; i++) {
            Cell cell1 = header1Row.createCell(i);
            Cell cell2 = header2Row.createCell(i);
            if (i == 0) {
                cell1.setCellValue("桩号");
            }
            cell1.setCellStyle(headerStyle);
            cell2.setCellStyle(headerStyle);
        }
        
        // D-I列：其他表头
        for (int i = 3; i < 9; i++) {
            Cell cell1 = header1Row.createCell(i);
            Cell cell2 = header2Row.createCell(i);
            cell1.setCellValue(headers[i - 2]);
            cell1.setCellStyle(headerStyle);
            cell2.setCellStyle(headerStyle);
        }

        // 合并单元格
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 0, 2)); // A-C列：桩号
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 3, 3)); // D列：路面类型
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 4, 4)); // E列：长度
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 5, 5)); // F列：横向力系数（SFC）
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 6, 6)); // G列：抗滑性能指数（SRI）
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 7, 7)); // H列：抗滑性能等级
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 8, 8)); // I列：备注

        // 设置列宽
        sheet.setColumnWidth(0, 4000);  // 起点桩号
        sheet.setColumnWidth(1, 1000);  // ~ 符号
        sheet.setColumnWidth(2, 4000);  // 终点桩号
        sheet.setColumnWidth(3, 3000);  // 路面类型
        sheet.setColumnWidth(4, 2500);  // 长度
        sheet.setColumnWidth(5, 4000);  // 横向力系数（SFC）
        sheet.setColumnWidth(6, 2500);  // 抗滑性能指数（SRI）
        sheet.setColumnWidth(7, 2500);  // 抗滑性能等级
        sheet.setColumnWidth(8, 4000);  // 备注
    }

    /**
     * 填充汇总表数据
     */
    private void fillSummaryData(Sheet sheet, List<RoadCheckSRI> upData, List<RoadCheckSRI> downData) {
        if (CollectionUtils.isEmpty(upData) && CollectionUtils.isEmpty(downData)) {
            return;
        }

        // 获取工作簿引用以创建数据样式
        SXSSFWorkbook workbook = (SXSSFWorkbook) sheet.getWorkbook();

        // 创建数据行样式
        CellStyle dataStyle = workbook.createCellStyle();
        dataStyle.setAlignment(HorizontalAlignment.CENTER);
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);
        Font dataFont = workbook.createFont();
        dataFont.setFontName("宋体");
        dataFont.setFontHeightInPoints((short) 10);
        dataStyle.setFont(dataFont);

        // 创建数值样式（保留2位小数）
        CellStyle numberStyle = workbook.createCellStyle();
        numberStyle.setAlignment(HorizontalAlignment.CENTER);
        numberStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        numberStyle.setBorderTop(BorderStyle.THIN);
        numberStyle.setBorderBottom(BorderStyle.THIN);
        numberStyle.setBorderLeft(BorderStyle.THIN);
        numberStyle.setBorderRight(BorderStyle.THIN);
        numberStyle.setDataFormat(workbook.createDataFormat().getFormat("0.00"));
        Font numFont = workbook.createFont();
        numFont.setFontName("Times New Roman");
        numFont.setFontHeightInPoints((short) 10);
        numberStyle.setFont(numFont);

        // 创建整数样式
        CellStyle integerStyle = workbook.createCellStyle();
        integerStyle.setAlignment(HorizontalAlignment.CENTER);
        integerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        integerStyle.setBorderTop(BorderStyle.THIN);
        integerStyle.setBorderBottom(BorderStyle.THIN);
        integerStyle.setBorderLeft(BorderStyle.THIN);
        integerStyle.setBorderRight(BorderStyle.THIN);
        integerStyle.setDataFormat(workbook.createDataFormat().getFormat("0"));
        Font intFont4 = workbook.createFont();
        intFont4.setFontName("Times New Roman");
        intFont4.setFontHeightInPoints((short) 10);
        integerStyle.setFont(intFont4);

        // 按公里段分组上行和下行数据
        List<Map<String, Object>> upThousandData = groupByThousandSection(upData);
        List<Map<String, Object>> downThousandData = groupByThousandSection(downData);

        // 获取所有公里段
        Map<String, Map<String, Object>> upDataMap = new HashMap<>();
        Map<String, Map<String, Object>> downDataMap = new HashMap<>();
        
        for (Map<String, Object> data : upThousandData) {
            String startCode = (String) data.get("startCode");
            upDataMap.put(startCode, data);
        }
        
        for (Map<String, Object> data : downThousandData) {
            String startCode = (String) data.get("startCode");
            downDataMap.put(startCode, data);
        }

        // 获取所有公里段的起始桩号并排序
        java.util.Set<String> allSections = new java.util.TreeSet<>();
        allSections.addAll(upDataMap.keySet());
        allSections.addAll(downDataMap.keySet());

        int rowIndex = 4; // 从第5行开始填充数据
        List<Map<String, Object>> summaryDataList = new ArrayList<>();

        for (String startCode : allSections) {
            Map<String, Object> upRecord = upDataMap.get(startCode);
            Map<String, Object> downRecord = downDataMap.get(startCode);

            Row dataRow = sheet.createRow(rowIndex++);
            int colIndex = 0;

            // 获取基本信息（优先使用上行数据，没有则使用下行数据）
            Map<String, Object> primaryRecord = upRecord != null ? upRecord : downRecord;
            String endCode = (String) primaryRecord.get("endCode");
            double length = (Double) primaryRecord.get("length");

            // A列：起始桩号
            Cell startCodeCell = dataRow.createCell(colIndex++);
            startCodeCell.setCellValue(startCode);
            startCodeCell.setCellStyle(dataStyle);

            // B列：~符号
            Cell tildeCell = dataRow.createCell(colIndex++);
            tildeCell.setCellValue("~");
            tildeCell.setCellStyle(dataStyle);

            // C列：结束桩号
            Cell endCodeCell = dataRow.createCell(colIndex++);
            endCodeCell.setCellValue(endCode);
            endCodeCell.setCellStyle(dataStyle);

            // D列：段长度
            Cell lengthCell = dataRow.createCell(colIndex++);
            lengthCell.setCellValue(length);
            lengthCell.setCellStyle(integerStyle);

            // E列：上行SFC平均值（直接截断小数位）
            Cell upSfcCell = dataRow.createCell(colIndex++);
            double upSfc = 0.0;
            if (upRecord != null) {
                upSfc = (Double) upRecord.get("avgSfc50At20");
            }
            upSfcCell.setCellValue((int) upSfc);
            upSfcCell.setCellStyle(integerStyle);

            // F列：上行SRI值
            Cell upSriCell = dataRow.createCell(colIndex++);
            double upSri = calculateSRI(upSfc);
            upSriCell.setCellValue(upSri);
            upSriCell.setCellStyle(numberStyle);

            // G列：上行等级
            Cell upGradeCell = dataRow.createCell(colIndex++);
            String upGrade = calculateSRIGrade(upSri);
            upGradeCell.setCellValue(upGrade);
            upGradeCell.setCellStyle(dataStyle);

            // H列：下行SFC平均值（直接截断小数位）
            Cell downSfcCell = dataRow.createCell(colIndex++);
            double downSfc = 0.0;
            if (downRecord != null) {
                downSfc = (Double) downRecord.get("avgSfc50At20");
            }
            downSfcCell.setCellValue((int) downSfc);
            downSfcCell.setCellStyle(integerStyle);

            // I列：下行SRI值
            Cell downSriCell = dataRow.createCell(colIndex++);
            double downSri = calculateSRI(downSfc);
            downSriCell.setCellValue(downSri);
            downSriCell.setCellStyle(numberStyle);

            // J列：下行等级
            Cell downGradeCell = dataRow.createCell(colIndex);
            String downGrade = calculateSRIGrade(downSri);
            downGradeCell.setCellValue(downGrade);
            downGradeCell.setCellStyle(dataStyle);

            // 保存数据用于统计
            Map<String, Object> summaryRecord = new HashMap<>();
            summaryRecord.put("length", length);
            summaryRecord.put("upSfc", upSfc);
            summaryRecord.put("upSri", upSri);
            summaryRecord.put("upGrade", upGrade);
            summaryRecord.put("downSfc", downSfc);
            summaryRecord.put("downSri", downSri);
            summaryRecord.put("downGrade", downGrade);
            summaryDataList.add(summaryRecord);
        }

        // 添加统计信息的6行
        addSummaryStatisticsRows(sheet, summaryDataList, upData, downData, rowIndex, dataStyle, numberStyle, integerStyle);
    }

    /**
     * 创建汇总表Excel表头
     */
    private void createSummaryHeader(SXSSFWorkbook workbook, Sheet sheet, String roadName, String roadCode, String startCode, String endCode) {
        // 创建单元格样式
        CellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        titleStyle.setBorderTop(BorderStyle.THIN);
        titleStyle.setBorderBottom(BorderStyle.THIN);
        titleStyle.setBorderLeft(BorderStyle.THIN);
        titleStyle.setBorderRight(BorderStyle.THIN);
        Font titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 14);
        titleFont.setFontName("宋体");
        titleStyle.setFont(titleFont);

        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontName("宋体");
        headerStyle.setFont(headerFont);

        CellStyle rightAlignStyle = workbook.createCellStyle();
        rightAlignStyle.setAlignment(HorizontalAlignment.RIGHT);
        rightAlignStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        rightAlignStyle.setBorderTop(BorderStyle.THIN);
        rightAlignStyle.setBorderBottom(BorderStyle.THIN);
        rightAlignStyle.setBorderLeft(BorderStyle.THIN);
        rightAlignStyle.setBorderRight(BorderStyle.THIN);
        Font rightFont = workbook.createFont();
        rightFont.setFontName("宋体");
        rightFont.setFontHeightInPoints((short) 10);
        rightAlignStyle.setFont(rightFont);

        // 第1行：标题
        Row titleRow = sheet.createRow(0);
        titleRow.setHeightInPoints(25);
        for (int i = 0; i < 10; i++) {
            Cell cell = titleRow.createCell(i);
            if (i == 0) {
                cell.setCellValue(roadName + "(" + roadCode + ")" + "路面横向力系数及抗滑性能指数汇总表");
            }
            cell.setCellStyle(titleStyle);
        }
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 9)); // 合并A-J列

        // 第2行：桩号信息
        Row stakeRow = sheet.createRow(1);
        stakeRow.setHeightInPoints(20);
        for (int i = 0; i < 10; i++) {
            Cell cell = stakeRow.createCell(i);
            if (i == 0) {
                cell.setCellValue("桩号:" + startCode + "~" + endCode);
            }
            cell.setCellStyle(rightAlignStyle);
        }
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, 9)); // 合并A-J列

        // 第3行：表头第一行
        Row header1Row = sheet.createRow(2);
        header1Row.setHeightInPoints(30);
        String[] header1 = {"桩号", "", "", "段落长度", "上行行车道", "", "", "下行行车道", "", ""};
        for (int i = 0; i < header1.length; i++) {
            Cell cell = header1Row.createCell(i);
            cell.setCellValue(header1[i]);
            cell.setCellStyle(headerStyle);
        }

        // 第4行：表头第二行
        Row header2Row = sheet.createRow(3);
        header2Row.setHeightInPoints(30);
        String[] header2 = {"起点", "~", "终点", "(m)", "横向力系数(SFC)", "路面抗滑性能(SRI)", "等级", "横向力系数(SFC)", "路面抗滑性能(SRI)", "等级"};
        for (int i = 0; i < header2.length; i++) {
            Cell cell = header2Row.createCell(i);
            cell.setCellValue(header2[i]);
            cell.setCellStyle(headerStyle);
        }

        // 合并单元格
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 0, 2)); // 桩号列合并A-C列
        sheet.addMergedRegion(new CellRangeAddress(2, 3, 3, 3)); // 段落长度列
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 4, 6)); // 上行行车道
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 7, 9)); // 下行行车道

        // 设置列宽
        sheet.setColumnWidth(0, 4000);  // 起点桩号
        sheet.setColumnWidth(1, 1000);  // ~ 符号
        sheet.setColumnWidth(2, 4000);  // 终点桩号
        sheet.setColumnWidth(3, 3000);  // 段落长度
        sheet.setColumnWidth(4, 4000);  // 上行SFC
        sheet.setColumnWidth(5, 4000);  // 上行SRI
        sheet.setColumnWidth(6, 2500);  // 上行等级
        sheet.setColumnWidth(7, 4000);  // 下行SFC
        sheet.setColumnWidth(8, 4000);  // 下行SRI
        sheet.setColumnWidth(9, 2500);  // 下行等级
    }

    /**
     * 添加汇总统计信息的6行
     */
    private void addSummaryStatisticsRows(Sheet sheet, List<Map<String, Object>> summaryDataList,
                                         List<RoadCheckSRI> upData, List<RoadCheckSRI> downData,
                                         int startRowIndex, CellStyle dataStyle, CellStyle numberStyle, CellStyle integerStyle) {
        if (summaryDataList == null || summaryDataList.isEmpty()) {
            return;
        }

        // 计算总长度
        double totalLength = summaryDataList.stream()
                .mapToDouble(record -> (Double) record.get("length"))
                .sum();

        // 计算上行和下行的总体平均SFC和SRI
        double totalUpSfc = 0.0, totalDownSfc = 0.0;
        if (!CollectionUtils.isEmpty(upData)) {
            totalUpSfc = upData.stream()
                    .filter(r -> r.getSfc50At20() != null)
                    .mapToDouble(r -> r.getSfc50At20().doubleValue())
                    .average()
                    .orElse(0.0);
        }
        
        if (!CollectionUtils.isEmpty(downData)) {
            totalDownSfc = downData.stream()
                    .filter(r -> r.getSfc50At20() != null)
                    .mapToDouble(r -> r.getSfc50At20().doubleValue())
                    .average()
                    .orElse(0.0);
        }

        double totalUpSri = calculateSRI(totalUpSfc);
        double totalDownSri = calculateSRI(totalDownSfc);
        String totalUpGrade = calculateSRIGrade(totalUpSri);
        String totalDownGrade = calculateSRIGrade(totalDownSri);

        // 第1行：合计
        Row totalRow = sheet.createRow(startRowIndex);
        // A-C列合并：显示"合计"
        for (int j = 0; j < 3; j++) {
            Cell cell = totalRow.createCell(j);
            if (j == 0) {
                cell.setCellValue("合计");
            }
            cell.setCellStyle(dataStyle);
        }
        sheet.addMergedRegion(new CellRangeAddress(startRowIndex, startRowIndex, 0, 2));

        // D列：总长度
        Cell totalLengthCell = totalRow.createCell(3);
        totalLengthCell.setCellValue(totalLength);
        totalLengthCell.setCellStyle(numberStyle);

        // E列：上行总体SFC平均值
        Cell totalUpSfcCell = totalRow.createCell(4);
        totalUpSfcCell.setCellValue((int) totalUpSfc);
        totalUpSfcCell.setCellStyle(integerStyle);

        // F列：上行总体SRI值
        Cell totalUpSriCell = totalRow.createCell(5);
        totalUpSriCell.setCellValue(totalUpSri);
        totalUpSriCell.setCellStyle(numberStyle);

        // G列：上行总体等级
        Cell totalUpGradeCell = totalRow.createCell(6);
        totalUpGradeCell.setCellValue(totalUpGrade);
        totalUpGradeCell.setCellStyle(dataStyle);

        // H列：下行总体SFC平均值
        Cell totalDownSfcCell = totalRow.createCell(7);
        totalDownSfcCell.setCellValue((int) totalDownSfc);
        totalDownSfcCell.setCellStyle(integerStyle);

        // I列：下行总体SRI值
        Cell totalDownSriCell = totalRow.createCell(8);
        totalDownSriCell.setCellValue(totalDownSri);
        totalDownSriCell.setCellStyle(numberStyle);

        // J列：下行总体等级
        Cell totalDownGradeCell = totalRow.createCell(9);
        totalDownGradeCell.setCellValue(totalDownGrade);
        totalDownGradeCell.setCellStyle(dataStyle);

        // 计算各等级的里程和占比
        String[] grades = {"优", "良", "中", "次", "差"};
        for (int i = 0; i < grades.length; i++) {
            String grade = grades[i];
            Row gradeRow = sheet.createRow(startRowIndex + 1 + i);

            // A-C列合并：显示等级(%)
            for (int j = 0; j < 3; j++) {
                Cell cell = gradeRow.createCell(j);
                if (j == 0) {
                    cell.setCellValue(grade + "(%)");
                }
                cell.setCellStyle(dataStyle);
            }
            sheet.addMergedRegion(new CellRangeAddress(startRowIndex + 1 + i, startRowIndex + 1 + i, 0, 2));

            // D列：空着
            Cell emptyCell = gradeRow.createCell(3);
            emptyCell.setCellValue("");
            emptyCell.setCellStyle(dataStyle);

            // 计算上行该等级的里程
            double upGradeLength = summaryDataList.stream()
                    .filter(record -> grade.equals((String) record.get("upGrade")))
                    .mapToDouble(record -> (Double) record.get("length"))
                    .sum();

            // 计算下行该等级的里程
            double downGradeLength = summaryDataList.stream()
                    .filter(record -> grade.equals((String) record.get("downGrade")))
                    .mapToDouble(record -> (Double) record.get("length"))
                    .sum();

            // E列：上行该等级总里程
            Cell upGradeLengthCell = gradeRow.createCell(4);
            upGradeLengthCell.setCellValue(upGradeLength);
            upGradeLengthCell.setCellStyle(numberStyle);

            // F-G列合并：上行该等级里程占比
            for (int j = 5; j < 7; j++) {
                Cell cell = gradeRow.createCell(j);
                if (j == 5) {
                    double upGradeRatio = totalLength > 0 ? (upGradeLength / totalLength) * 100 : 0;
                    cell.setCellValue(String.format("%.2f%%", upGradeRatio));
                }
                cell.setCellStyle(numberStyle);
            }
            sheet.addMergedRegion(new CellRangeAddress(startRowIndex + 1 + i, startRowIndex + 1 + i, 5, 6));

            // H列：下行该等级总里程
            Cell downGradeLengthCell = gradeRow.createCell(7);
            downGradeLengthCell.setCellValue(downGradeLength);
            downGradeLengthCell.setCellStyle(numberStyle);

            // I-J列合并：下行该等级里程占比
            for (int j = 8; j < 10; j++) {
                Cell cell = gradeRow.createCell(j);
                if (j == 8) {
                    double downGradeRatio = totalLength > 0 ? (downGradeLength / totalLength) * 100 : 0;
                    cell.setCellValue(String.format("%.2f%%", downGradeRatio));
                }
                cell.setCellStyle(numberStyle);
            }
            sheet.addMergedRegion(new CellRangeAddress(startRowIndex + 1 + i, startRowIndex + 1 + i, 8, 9));
        }
    }

    /**
     * 生成上行和下行两个独立的SRI Word文档
     */
    private void generateSeparateSRIWordDocuments(HttpServletResponse response,
                                                  List<RoadCheckSRI> upData, List<RoadCheckSRI> downData,
                                                  Road road,
                                                  Long teamId, String dateTime, String monthDate,
                                                  String titleName, String checkName, String reviewName) {
        try {
            // 创建ZIP输出流
            response.setContentType("application/zip");
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String zipFileName = "路面SRI检测报告_" + sdf.format(new Date()) + ".zip";
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(zipFileName, "UTF-8"));

            ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream());

            // 使用并发工具类
            ExecutorService executor = Executors.newFixedThreadPool(2);
            CountDownLatch latch = new CountDownLatch(2);

            // 用于存储生成的文档和文件名
            final XWPFDocument[] documents = new XWPFDocument[2];
            final String[] fileNames = new String[2];
            final Exception[] exceptions = new Exception[2];

            // 并行生成上行文档
            if (!CollectionUtils.isEmpty(upData)) {
                executor.submit(() -> {
                    try {
                        log.info("开始生成上行SRI Word文档...");
                        documents[0] = createSingleDirectionSRIDocument(upData, downData, road, "上行", teamId, dateTime, monthDate, titleName, checkName, reviewName);
                        fileNames[0] = "路面SRI检测报告_上行_" + sdf.format(new Date()) + ".docx";
                        log.info("上行SRI Word文档生成完成");
                    } catch (Exception e) {
                        log.error("生成上行SRI Word文档失败", e);
                        exceptions[0] = e;
                    } finally {
                        latch.countDown();
                    }
                });
            } else {
                latch.countDown();
            }

            // 并行生成下行文档
            if (!CollectionUtils.isEmpty(downData)) {
                executor.submit(() -> {
                    try {
                        log.info("开始生成下行SRI Word文档...");
                        documents[1] = createSingleDirectionSRIDocument(upData, downData, road, "下行", teamId, dateTime, monthDate, titleName, checkName, reviewName);
                        fileNames[1] = "路面SRI检测报告_下行_" + sdf.format(new Date()) + ".docx";
                        log.info("下行SRI Word文档生成完成");
                    } catch (Exception e) {
                        log.error("生成下行SRI Word文档失败", e);
                        exceptions[1] = e;
                    } finally {
                        latch.countDown();
                    }
                });
            } else {
                latch.countDown();
            }

            // 等待所有文档生成完成
            latch.await();
            executor.shutdown();

            // 检查是否有异常发生
            if (exceptions[0] != null) {
                throw new RuntimeException("生成上行SRI Word文档失败", exceptions[0]);
            }
            if (exceptions[1] != null) {
                throw new RuntimeException("生成下行SRI Word文档失败", exceptions[1]);
            }

            // 将生成的文档写入ZIP
            if (documents[0] != null && fileNames[0] != null) {
                ZipEntry upEntry = new ZipEntry(fileNames[0]);
                zipOut.putNextEntry(upEntry);
                documents[0].write(zipOut);
                zipOut.closeEntry();
                documents[0].close();
                log.info("上行文档已添加到ZIP文件");
            }

            if (documents[1] != null && fileNames[1] != null) {
                ZipEntry downEntry = new ZipEntry(fileNames[1]);
                zipOut.putNextEntry(downEntry);
                documents[1].write(zipOut);
                zipOut.closeEntry();
                documents[1].close();
                log.info("下行文档已添加到ZIP文件");
            }

            zipOut.close();
            log.info("SRI ZIP文件生成完成");

        } catch (Exception e) {
            log.error("生成分离的SRI Word文档失败", e);
            throw new RuntimeException("生成分离的SRI Word文档失败", e);
        }
    }

    /**
     * 创建单个方向的SRI Word文档
     */
    private XWPFDocument createSingleDirectionSRIDocument(List<RoadCheckSRI> upData, List<RoadCheckSRI> downData,
                                                          Road road, String direction,
                                                          Long teamId, String dateTime, String monthDate,
                                                          String titleName, String checkName, String reviewName) {
        InputStream templateStream = null;
        try {
            // 解决POI安全限制：设置ZIP文件的最小压缩比率，避免Zip bomb检测误报
            org.apache.poi.openxml4j.util.ZipSecureFile.setMinInflateRatio(0.001);
            
            // 加载模板
            ClassPathResource templateResource = new ClassPathResource("static/word/sri-template.docx");
            templateStream = templateResource.getInputStream();
            XWPFDocument document = new XWPFDocument(templateStream);
            log.info("成功加载SRI Word模板文件");

            // 准备汇总数据 - 使用千米段进行汇总
            List<Map<String, Object>> summaryData = processThousandSectionSummaryData(upData, downData);

            // 填充第三个表格（汇总表格）
            if (document.getTables().size() >= 3) {
                XWPFTable summaryTable = document.getTables().get(2); // 第三个表格
                fillSRISummaryTable(summaryTable, summaryData, upData, downData);
                log.info("成功填充SRI汇总表格，共 {} 行数据", summaryData.size());
            } else {
                log.warn("未找到SRI Word模板中的汇总表格");
            }

            // 填充最后一个表格（十米详细数据）
            if (document.getTables().size() >= 4) {
                XWPFTable detailTable = document.getTables().get(document.getTables().size() - 1); // 最后一个表格

                // 根据方向选择对应的详细数据
                List<RoadCheckSRI> detailData = new ArrayList<>();
                if ("上行".equals(direction) && upData != null && !upData.isEmpty()) {
                    detailData.addAll(upData);
                } else if ("下行".equals(direction) && downData != null && !downData.isEmpty()) {
                    detailData.addAll(downData);
                }

                // 按起始桩号排序
                detailData.sort((a, b) -> a.getStartCode().compareTo(b.getStartCode()));

                // 填充详细数据到表格
                fillSRIDetailTable(detailTable, detailData);

                log.info("成功填充{}详细数据表格，共 {} 行数据", direction, detailData.size());
            } else {
                log.warn("未找到详细数据表格");
            }

            // 填充检测人员信息到第二个表格（如果存在）
            if (document.getTables().size() >= 2) {
                Long actualTeamId = teamId;
                if (actualTeamId != null) {
                    checkTeamService.fillCheckTeamUserTable(document.getTables().get(1), actualTeamId);
                    log.info("成功填充SRI检测人员信息表格，使用分组ID: {}", actualTeamId);
                }
            } else {
                log.warn("未找到SRI检测人员信息表格（第二个表格）");
            }

            // 替换文档中的占位符
            WordDocumentUtils.replaceTextInDocument(document, "${roadName}", road.getRoadName());
            WordDocumentUtils.replaceTextInDocument(document, "${startCode}", road.getStartCode());
            WordDocumentUtils.replaceTextInDocument(document, "${endCode}", road.getEndCode());
            WordDocumentUtils.replaceTextInDocumentWithFontStyle(document, "${roadNames}", road.getRoadName(),"黑体",24);
            WordDocumentUtils.replaceTextInDocumentWithFontStyle(document, "${startCodes}", road.getStartCode(),"黑体",24);
            WordDocumentUtils.replaceTextInDocumentWithFontStyle(document, "${endCodes}", road.getEndCode(),"黑体",24);
            WordDocumentUtils.replaceTextInDocumentWithFontStyle(document, "${year}", String.valueOf(road.getYear()),"黑体",24);
            WordDocumentUtils.replaceTextInDocument(document, "${roadNameTitle}", road.getRoadName());
            WordDocumentUtils.replaceTextInDocument(document, "${direction}", direction);
            WordDocumentUtils.replaceTextInDocument(document, "${companyName}", road.getCompanyName());
            WordDocumentUtils.replaceTextInDocument(document, "${projectName}", road.getProjectName());
            WordDocumentUtils.replaceTextInDocument(document, "${reportNo}", road.getReportNo());
            WordDocumentUtils.replaceTextInDocument(document, "${number}", "05");
            
            // 替换日期时间相关占位符
            String finalDateTime = dateTime != null && !dateTime.trim().isEmpty() ? dateTime : com.tunnel.common.utils.DateUtils.getDate();
            String finalMonthDate = monthDate != null && !monthDate.trim().isEmpty() ? monthDate : com.tunnel.common.utils.DateUtils.parseDateToStr("yyyy年MM月", new java.util.Date());

            replaceSRITextInDocument(document, "${dateTime}", finalDateTime);
            replaceSRITextInDocument(document, "${monthDate}", finalMonthDate);
            replaceSRITextInDocument(document, "${date}", finalDateTime); // 兼容旧的占位符

            // 根据dateTime参数生成dateTimeStr并替换
            if (dateTime != null && !dateTime.isEmpty()) {
                String dateTimeStr = dateTime.replace("年", ".").replace("月", ".").replace("日", "");
                replaceSRITextInDocument(document, "${dateTimeStr}", dateTimeStr);
            }

            // 计算文档页数并替换页数占位符
            int[] pageCalculations = calculateSRIDocumentPagesWithDetails(summaryData, upData, downData, direction);
            int totalPages = pageCalculations[0];
            int pageOne = pageCalculations[1];
            int pageTwo = pageCalculations[2];
            
            WordDocumentUtils.replaceTextInDocument(document, "${pages}", String.valueOf(totalPages));
            WordDocumentUtils.replaceTextInDocument(document, "${pageOne}", String.valueOf(pageOne));
            WordDocumentUtils.replaceTextInDocument(document, "${pageTwo}", String.valueOf(pageTwo));
            log.info("{}方向SRI文档共 {} 页，pageOne={}, pageTwo={}", direction, totalPages, pageOne, pageTwo);

            // 生成对齐的目录内容并替换占位符
            String[] catalogLines = WordDocumentUtils.generateAlignedCatalogLines(road.getRoadName(),"公路路面公里横向力系数及抗滑性能SRI评定汇总表","公路路面横向力系数及抗滑性能SRI检测表", direction, pageOne, pageTwo, totalPages);
            WordDocumentUtils.replaceTextInDocumentWithMixedFont(document, "${mulu1}", catalogLines[0]);
            WordDocumentUtils.replaceTextInDocumentWithMixedFont(document, "${mulu2}", catalogLines[1]);

            // 获取路线信息用于报告编号
            Road roadInfo = roadMapper.selectRoadById(upData != null && !upData.isEmpty() ? upData.get(0).getRoadId() : 
                                                     (downData != null && !downData.isEmpty() ? downData.get(0).getRoadId() : null));
            
            // 使用公共工具类替换标准占位符（包括报告编号）
            WordDocumentUtils.replaceStandardPlaceholders(document, "SRI", titleName, checkName, reviewName, roadInfo);

            return document;

        } catch (Exception e) {
            log.error("创建{}方向SRI Word文档失败", direction, e);
            throw new RuntimeException("创建" + direction + "方向SRI Word文档失败", e);
        } finally {
            // 确保流被关闭
            if (templateStream != null) {
                try {
                    templateStream.close();
                } catch (IOException e) {
                    log.warn("关闭模板流失败: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 处理千米段汇总数据，参考exportSRIByDirection中汇总sheet的逻辑
     */
    private List<Map<String, Object>> processThousandSectionSummaryData(List<RoadCheckSRI> upData, List<RoadCheckSRI> downData) {
        // 按公里段分组上行和下行数据
        List<Map<String, Object>> upThousandData = groupByThousandSection(upData);
        List<Map<String, Object>> downThousandData = groupByThousandSection(downData);

        // 获取所有公里段
        Map<String, Map<String, Object>> upDataMap = new HashMap<>();
        Map<String, Map<String, Object>> downDataMap = new HashMap<>();
        
        for (Map<String, Object> data : upThousandData) {
            String startCode = (String) data.get("startCode");
            upDataMap.put(startCode, data);
        }
        
        for (Map<String, Object> data : downThousandData) {
            String startCode = (String) data.get("startCode");
            downDataMap.put(startCode, data);
        }

        // 获取所有公里段的起始桩号并排序
        java.util.Set<String> allSections = new java.util.TreeSet<>();
        allSections.addAll(upDataMap.keySet());
        allSections.addAll(downDataMap.keySet());

        List<Map<String, Object>> summaryDataList = new ArrayList<>();

        for (String startCode : allSections) {
            Map<String, Object> upRecord = upDataMap.get(startCode);
            Map<String, Object> downRecord = downDataMap.get(startCode);

            // 获取基本信息（优先使用上行数据，没有则使用下行数据）
            Map<String, Object> primaryRecord = upRecord != null ? upRecord : downRecord;
            String endCode = (String) primaryRecord.get("endCode");
            double length = (Double) primaryRecord.get("length");

            Map<String, Object> summaryRecord = new HashMap<>();
            summaryRecord.put("startCode", startCode);
            summaryRecord.put("endCode", endCode);
            summaryRecord.put("length", length);

            // 上行数据
            double upSfc = 0.0;
            if (upRecord != null) {
                upSfc = (Double) upRecord.get("avgSfc50At20");
            }
            double upSri = calculateSRI(upSfc);
            String upGrade = calculateSRIGrade(upSri);

            summaryRecord.put("upSfc", upSfc);
            summaryRecord.put("upSri", upSri);
            summaryRecord.put("upGrade", upGrade);

            // 下行数据
            double downSfc = 0.0;
            if (downRecord != null) {
                downSfc = (Double) downRecord.get("avgSfc50At20");
            }
            double downSri = calculateSRI(downSfc);
            String downGrade = calculateSRIGrade(downSri);

            summaryRecord.put("downSfc", downSfc);
            summaryRecord.put("downSri", downSri);
            summaryRecord.put("downGrade", downGrade);

            summaryDataList.add(summaryRecord);
        }

        return summaryDataList;
    }

    /**
     * 填充SRI汇总表格
     */
    private void fillSRISummaryTable(XWPFTable table, List<Map<String, Object>> summaryData,
                                     List<RoadCheckSRI> upData, List<RoadCheckSRI> downData) {
        try {
            // 设置表格整体边框
            setSRITableBorders(table);
            
            // 清空表格中除前2行表头外的所有行，保留第3行作为数据填充的起始行
            while (table.getRows().size() > 3) {
                table.removeRow(3);
            }

            // 如果第3行不存在，创建一个
            if (table.getRows().size() < 3) {
                table.createRow();
            }

            // 统计各等级里程数据
            double[] upGradeDistances = new double[5]; // 优良中次差的实际里程
            double[] downGradeDistances = new double[5];
            double totalDistance = 0;

            // 从第3行开始填充数据行
            int currentRowIndex = 2; // 第3行的索引是2（从0开始）

            // 填充数据行
            for (Map<String, Object> record : summaryData) {
                XWPFTableRow dataRow;
                if (currentRowIndex < table.getRows().size()) {
                    // 使用现有行
                    dataRow = table.getRow(currentRowIndex);
                } else {
                    // 创建新行
                    dataRow = table.createRow();
                }
                currentRowIndex++;
                int colIndex = 0;

                // A列：起始桩号
                safeSetSRICellText(dataRow, colIndex++, (String) record.get("startCode"));

                // B列：~符号
                WordDocumentUtils.setCellTextWithVerticalCenter(dataRow, colIndex++, "~");

                // C列：结束桩号
                safeSetSRICellText(dataRow, colIndex++, (String) record.get("endCode"));

                // D列：段落长度
                double distance = (Double) record.get("length");
                totalDistance += distance;
                safeSetSRICellText(dataRow, colIndex++, String.format("%.2f", distance));

                // E列：上行SFC平均值（四舍五入为整数）
                double upSfc = (Double) record.get("upSfc");
                safeSetSRICellText(dataRow, colIndex++, String.valueOf(Math.round(upSfc)));

                // F列：上行SRI值
                double upSri = (Double) record.get("upSri");
                safeSetSRICellText(dataRow, colIndex++, String.format("%.2f", upSri));

                // G列：上行等级
                String upGrade = (String) record.get("upGrade");
                safeSetSRICellText(dataRow, colIndex++, upGrade);

                // H列：下行SFC平均值（四舍五入为整数）
                double downSfc = (Double) record.get("downSfc");
                safeSetSRICellText(dataRow, colIndex++, String.valueOf(Math.round(downSfc)));

                // I列：下行SRI值
                double downSri = (Double) record.get("downSri");
                safeSetSRICellText(dataRow, colIndex++, String.format("%.2f", downSri));

                // J列：下行等级
                String downGrade = (String) record.get("downGrade");
                safeSetSRICellText(dataRow, colIndex, downGrade);

                // 统计上行和下行等级里程
                upGradeDistances[getSRIGradeIndex(upGrade)] += distance;
                downGradeDistances[getSRIGradeIndex(downGrade)] += distance;
            }

            // 添加合计行
            XWPFTableRow totalRow;
            if (currentRowIndex < table.getRows().size()) {
                totalRow = table.getRow(currentRowIndex);
            } else {
                totalRow = table.createRow();
            }
            int totalRowIndex = currentRowIndex; // 记录合计行的索引
            currentRowIndex++;
            int colIndex = 0;

            // 合并A-C列显示"合计"
            safeSetSRICellText(totalRow, colIndex++, "合计");
            safeSetSRICellText(totalRow, colIndex++, "");
            safeSetSRICellText(totalRow, colIndex++, "");

            // D列：总里程
            safeSetSRICellText(totalRow, colIndex++, String.valueOf((int) totalDistance));

            // E列：上行总体SFC平均值
            double totalUpSfc = 0.0;
            if (!CollectionUtils.isEmpty(upData)) {
                totalUpSfc = upData.stream()
                        .filter(r -> r.getSfc50At20() != null)
                        .mapToDouble(r -> r.getSfc50At20().doubleValue())
                        .average()
                        .orElse(0.0);
            }
            safeSetSRICellText(totalRow, colIndex++, String.valueOf(Math.round(totalUpSfc)));

            // F列：上行总体SRI值
            double totalUpSri = calculateSRI(totalUpSfc);
            safeSetSRICellText(totalRow, colIndex++, String.format("%.2f", totalUpSri));

            // G列：上行总体等级
            safeSetSRICellText(totalRow, colIndex++, calculateSRIGrade(totalUpSri));

            // H列：下行总体SFC平均值
            double totalDownSfc = 0.0;
            if (!CollectionUtils.isEmpty(downData)) {
                totalDownSfc = downData.stream()
                        .filter(r -> r.getSfc50At20() != null)
                        .mapToDouble(r -> r.getSfc50At20().doubleValue())
                        .average()
                        .orElse(0.0);
            }
            safeSetSRICellText(totalRow, colIndex++, String.valueOf(Math.round(totalDownSfc)));

            // I列：下行总体SRI值
            double totalDownSri = calculateSRI(totalDownSfc);
            safeSetSRICellText(totalRow, colIndex++, String.format("%.2f", totalDownSri));

            // J列：下行总体等级
            safeSetSRICellText(totalRow, colIndex, calculateSRIGrade(totalDownSri));

            // 合并合计行的前三列
            mergeSRISummaryRowCells(table, totalRowIndex);

            // 添加各等级统计行
            String[] grades = {"优", "良", "中", "次", "差"};
            double totalUpDistance = java.util.Arrays.stream(upGradeDistances).sum();
            double totalDownDistance = java.util.Arrays.stream(downGradeDistances).sum();

            for (int i = 0; i < grades.length; i++) {
                XWPFTableRow gradeRow;
                if (currentRowIndex < table.getRows().size()) {
                    gradeRow = table.getRow(currentRowIndex);
                } else {
                    gradeRow = table.createRow();
                }
                int gradeRowIndex = currentRowIndex; // 记录等级行的索引
                currentRowIndex++;
                colIndex = 0;

                // 合并A-C列显示等级
                safeSetSRICellText(gradeRow, colIndex++, grades[i] + " (%)");
                safeSetSRICellText(gradeRow, colIndex++, "");
                safeSetSRICellText(gradeRow, colIndex++, "");

                // D列为空
                safeSetSRICellText(gradeRow, colIndex++, "");

                // E列：上行该等级里程（实际里程）
                safeSetSRICellText(gradeRow, colIndex++, String.valueOf((int) upGradeDistances[i]));

                // F-G列合并：上行该等级里程占比
                double upGradeRatio = totalUpDistance > 0 ? (upGradeDistances[i] / totalUpDistance) * 100 : 0;
                safeSetSRICellText(gradeRow, colIndex++, String.format("%.2f%%", upGradeRatio));
                safeSetSRICellText(gradeRow, colIndex++, "");

                // H列：下行该等级里程（实际里程）
                safeSetSRICellText(gradeRow, colIndex++, String.valueOf((int) downGradeDistances[i]));

                // I-J列合并：下行该等级里程占比
                double downGradeRatio = totalDownDistance > 0 ? (downGradeDistances[i] / totalDownDistance) * 100 : 0;
                safeSetSRICellText(gradeRow, colIndex++, String.format("%.2f%%", downGradeRatio));
                safeSetSRICellText(gradeRow, colIndex, "");

                // 合并等级行的前三列
                mergeSRISummaryRowCells(table, gradeRowIndex);
                // 合并等级行的F-G列和I-J列
                mergeSRIGradeRatioCells(table, gradeRowIndex);
            }

            log.info("完成填充SRI汇总表格，共 {} 行数据行 + 1行合计 + 5行等级统计", summaryData.size());
        } catch (Exception e) {
            log.error("填充SRI汇总表格失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 填充SRI详细数据表格
     */
    private void fillSRIDetailTable(XWPFTable table, List<RoadCheckSRI> dataList) {
        try {
            // 设置表格整体边框
            setSRITableBorders(table);
            
            // 清空表格中除表头外的所有行（假设前2行是表头）
            while (table.getRows().size() > 2) {
                table.removeRow(2);
            }

            int processedRows = 0;
            for (RoadCheckSRI record : dataList) {
                XWPFTableRow dataRow = table.createRow();
                int colIndex = 0;

                // A列：起始桩号
                safeSetSRICellText(dataRow, colIndex++, record.getStartCode() != null ? record.getStartCode() : "");

                // B列：~符号
                com.tunnel.common.utils.WordDocumentUtils.setCellTextWithVerticalCenter(dataRow, colIndex++, "~");

                // C列：结束桩号
                safeSetSRICellText(dataRow, colIndex++, record.getEndCode() != null ? record.getEndCode() : "");

                // D列：测试速度
                safeSetSRICellText(dataRow, colIndex++, formatSRIDecimalValue(record.getTestSpeed()));

                // E列：测试温度
                safeSetSRICellText(dataRow, colIndex++, formatSRIDecimalValue(record.getTestTemp()));

                // F列：SFC实测值
                safeSetSRICellText(dataRow, colIndex++, formatSRIDecimalValue(record.getSfcValue()));

                // G列：SFC(50km/h)
                safeSetSRICellText(dataRow, colIndex++, formatSRIDecimalValue(record.getSfc50()));

                // H列：SFC(50km/h，20℃)
                safeSetSRICellText(dataRow, colIndex++, formatSRIDecimalValue(record.getSfc50At20()));

                // I列：备注
                safeSetSRICellText(dataRow, colIndex, record.getRemark() != null ? record.getRemark() : "");

                processedRows++;
            }

            log.info("完成填充 {} 行数据到SRI详细表格", processedRows);
        } catch (Exception e) {
            log.error("填充SRI详细数据表格失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 安全设置SRI表格单元格文本
     */
    private void safeSetSRICellText(XWPFTableRow row, int cellIndex, String text) {
        try {
            // 确保有足够的单元格
            while (row.getTableCells().size() <= cellIndex) {
                row.createCell();
            }

            XWPFTableCell cell = row.getCell(cellIndex);
            if (cell != null) {
                // 设置单元格边框
                setSRICellBorders(cell);
                
                // 清除现有段落，创建新的居中段落（参考RQI方法）
                if (cell.getParagraphs().size() > 0) {
                    cell.removeParagraph(0);
                }
                XWPFParagraph paragraph = cell.addParagraph();
                paragraph.setAlignment(ParagraphAlignment.CENTER);
                XWPFRun run = paragraph.createRun();
                String safeText = text != null ? text : "";
                run.setText(safeText);
                run.setFontSize(9); // 小五字体对应9号
                // 根据内容选择字体：包含字母或数字用Times New Roman，否则用宋体
                if (containsLatinOrDigit(safeText)) {
                    run.setFontFamily("Times New Roman");
                } else {
                    run.setFontFamily("宋体");
                }

                // 设置单元格垂直居中对齐（参考RQI方法）
                cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            }
        } catch (Exception e) {
            log.warn("设置SRI单元格文本失败 [行:{}, 列:{}]: {}",
                    row != null ? row.getTableCells().size() : "null", cellIndex, e.getMessage());
        }
    }

    /**
     * 合并SRI汇总表格行的前三列
     */
    private void mergeSRISummaryRowCells(XWPFTable table, int rowIndex) {
        try {
            if (table.getRows().size() > rowIndex) {
                XWPFTableRow row = table.getRow(rowIndex);
                if (row.getTableCells().size() >= 3) {
                    // 获取前三个单元格
                    XWPFTableCell cell0 = row.getCell(0);
                    XWPFTableCell cell1 = row.getCell(1);
                    XWPFTableCell cell2 = row.getCell(2);

                    // 设置单元格合并属性
                    if (cell0.getCTTc().getTcPr() == null) {
                        cell0.getCTTc().addNewTcPr();
                    }
                    if (cell0.getCTTc().getTcPr().getHMerge() == null) {
                        cell0.getCTTc().getTcPr().addNewHMerge().setVal(STMerge.RESTART);
                    }

                    // 第二个和第三个单元格设置为继续合并
                    if (cell1.getCTTc().getTcPr() == null) {
                        cell1.getCTTc().addNewTcPr();
                    }
                    if (cell1.getCTTc().getTcPr().getHMerge() == null) {
                        cell1.getCTTc().getTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
                    }

                    if (cell2.getCTTc().getTcPr() == null) {
                        cell2.getCTTc().addNewTcPr();
                    }
                    if (cell2.getCTTc().getTcPr().getHMerge() == null) {
                        cell2.getCTTc().getTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
                    }

                    // 清空第二个和第三个单元格的内容
                    cell1.getParagraphs().clear();
                    cell1.addParagraph();
                    cell2.getParagraphs().clear();
                    cell2.addParagraph();

                    log.debug("成功合并第{}行的前三列", rowIndex + 1);
                }
            }
        } catch (Exception e) {
            log.warn("合并第{}行前三列失败: {}", rowIndex + 1, e.getMessage());
        }
    }

    /**
     * 合并SRI等级比率单元格
     */
    private void mergeSRIGradeRatioCells(XWPFTable table, int rowIndex) {
        try {
            if (table.getRows().size() > rowIndex) {
                XWPFTableRow row = table.getRow(rowIndex);
                if (row.getTableCells().size() >= 10) {
                    // 合并F-G列（上行比率）
                    XWPFTableCell cellF = row.getCell(5);
                    XWPFTableCell cellG = row.getCell(6);
                    if (cellF.getCTTc().getTcPr() == null) {
                        cellF.getCTTc().addNewTcPr();
                    }
                    if (cellF.getCTTc().getTcPr().getHMerge() == null) {
                        cellF.getCTTc().getTcPr().addNewHMerge().setVal(STMerge.RESTART);
                    }
                    if (cellG.getCTTc().getTcPr() == null) {
                        cellG.getCTTc().addNewTcPr();
                    }
                    if (cellG.getCTTc().getTcPr().getHMerge() == null) {
                        cellG.getCTTc().getTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
                    }
                    cellG.getParagraphs().clear();
                    cellG.addParagraph();

                    // 合并I-J列（下行比率）
                    XWPFTableCell cellI = row.getCell(8);
                    XWPFTableCell cellJ = row.getCell(9);
                    if (cellI.getCTTc().getTcPr() == null) {
                        cellI.getCTTc().addNewTcPr();
                    }
                    if (cellI.getCTTc().getTcPr().getHMerge() == null) {
                        cellI.getCTTc().getTcPr().addNewHMerge().setVal(STMerge.RESTART);
                    }
                    if (cellJ.getCTTc().getTcPr() == null) {
                        cellJ.getCTTc().addNewTcPr();
                    }
                    if (cellJ.getCTTc().getTcPr().getHMerge() == null) {
                        cellJ.getCTTc().getTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
                    }
                    cellJ.getParagraphs().clear();
                    cellJ.addParagraph();

                    log.debug("成功合并第{}行的等级比率单元格", rowIndex + 1);
                }
            }
        } catch (Exception e) {
            log.warn("合并第{}行等级比率单元格失败: {}", rowIndex + 1, e.getMessage());
        }
    }

    /**
     * 替换SRI文档中的占位符
     */
    /**
     * 在段落中替换文本，完全保持原有格式
     * 这个方法逐个run进行处理，确保格式不被破坏
     * 特别针对页眉标题${roadNameTitle}进行优化
     */
    private void replaceTextInParagraphPreservingFormat(XWPFParagraph paragraph, String placeholder, String replacement) {
        try {
            List<XWPFRun> runs = paragraph.getRuns();
            if (runs == null || runs.isEmpty()) {
                return;
            }

            // 构建完整的段落文本，同时记录每个字符对应的run索引
            StringBuilder fullText = new StringBuilder();
            List<Integer> charToRunMap = new ArrayList<>();

            for (int runIndex = 0; runIndex < runs.size(); runIndex++) {
                XWPFRun run = runs.get(runIndex);
                String runText = run.getText(0);
                if (runText != null) {
                    fullText.append(runText);
                    for (int i = 0; i < runText.length(); i++) {
                        charToRunMap.add(runIndex);
                    }
                }
            }

            String originalText = fullText.toString();
            if (!originalText.contains(placeholder)) {
                return;
            }

            // 找到占位符的位置
            int placeholderStart = originalText.indexOf(placeholder);
            int placeholderEnd = placeholderStart + placeholder.length();

            // 确定占位符所在的run范围
            if (placeholderStart >= 0 && placeholderStart < charToRunMap.size()) {
                int targetRunIndex = charToRunMap.get(placeholderStart);
                XWPFRun targetRun = runs.get(targetRunIndex);

                // 保存目标run的所有格式信息（增强版）
                String fontFamily = targetRun.getFontFamily();
                Integer fontSize = targetRun.getFontSize();
                Boolean isBold = targetRun.isBold();
                Boolean isItalic = targetRun.isItalic();
                String color = targetRun.getColor();
                Boolean isUnderlined = targetRun.getUnderline() != null;
                
                // 保存更多格式信息
                String fontFamilyTheme = null;
                try {
                    if (targetRun.getCTR() != null && targetRun.getCTR().getRPr() != null) {
                        if (targetRun.getCTR().getRPr().getRFonts() != null) {
                            fontFamilyTheme = targetRun.getCTR().getRPr().getRFonts().getAsciiTheme() != null ?
                                targetRun.getCTR().getRPr().getRFonts().getAsciiTheme().toString() : null;
                        }
                    }
                } catch (Exception ignored) {
                    // 忽略主题字体获取错误
                }

                // 替换文本
                String newText = originalText.replace(placeholder, replacement);

                // 更安全的文本替换方式：逐个处理run
                boolean foundAndReplaced = false;
                for (int i = 0; i < runs.size(); i++) {
                    XWPFRun run = runs.get(i);
                    String runText = run.getText(0);
                    if (runText != null && runText.contains(placeholder)) {
                        // 在包含占位符的run中进行替换
                        String replacedText = runText.replace(placeholder, replacement);
                        run.setText(replacedText, 0);
                        
                        // 确保格式完全保持（特别针对页眉优化）
                        if (fontFamily != null && !fontFamily.isEmpty()) {
                            run.setFontFamily(fontFamily);
                        }
                        if (fontSize != null && fontSize > 0) {
                            run.setFontSize(fontSize);
                        }
                        if (isBold != null) {
                            run.setBold(isBold);
                        }
                        if (isItalic != null) {
                            run.setItalic(isItalic);
                        }
                        if (color != null && !color.isEmpty()) {
                            run.setColor(color);
                        }
                        if (isUnderlined != null && isUnderlined && targetRun.getUnderline() != null) {
                            run.setUnderline(targetRun.getUnderline());
                        }
                        
                        foundAndReplaced = true;
                        log.debug("SRI页眉标题替换成功：{} -> {}，保持原格式（字体: {}, 大小: {}, 粗体: {}, 颜色: {}）",
                                placeholder, replacement, fontFamily, fontSize, isBold, color);
                        break;
                    }
                }
                
                // 如果上述方法没有成功，使用备用方法
                if (!foundAndReplaced) {
                    // 清除所有run的文本
                    for (XWPFRun run : runs) {
                        run.setText("", 0);
                    }

                    // 将新文本设置到目标run中，保持其格式
                    targetRun.setText(newText, 0);

                    // 确保格式完全保持
                    if (fontFamily != null && !fontFamily.isEmpty()) {
                        targetRun.setFontFamily(fontFamily);
                    }
                    if (fontSize != null && fontSize > 0) {
                        targetRun.setFontSize(fontSize);
                    }
                    if (isBold != null) {
                        targetRun.setBold(isBold);
                    }
                    if (isItalic != null) {
                        targetRun.setItalic(isItalic);
                    }
                    if (color != null && !color.isEmpty()) {
                        targetRun.setColor(color);
                    }
                    if (isUnderlined != null && isUnderlined && targetRun.getUnderline() != null) {
                        targetRun.setUnderline(targetRun.getUnderline());
                    }
                    
                    log.debug("SRI页眉标题备用替换成功：{} -> {}，保持原格式（字体: {}, 大小: {}）",
                            placeholder, replacement, fontFamily, fontSize);
                }
            }
        } catch (Exception e) {
            log.warn("SRI保持格式的文本替换失败: {}", e.getMessage());
            // 最后的备用方案：简单替换但尽量保持格式
            try {
                for (XWPFRun run : paragraph.getRuns()) {
                    String runText = run.getText(0);
                    if (runText != null && runText.contains(placeholder)) {
                        // 保存格式
                        String fontFamily = run.getFontFamily();
                        Integer fontSize = run.getFontSize();
                        Boolean isBold = run.isBold();
                        Boolean isItalic = run.isItalic();
                        String color = run.getColor();
                        
                        // 替换文本
                        run.setText(runText.replace(placeholder, replacement), 0);
                        
                        // 恢复格式
                        if (fontFamily != null) run.setFontFamily(fontFamily);
                        if (fontSize != null && fontSize > 0) run.setFontSize(fontSize);
                        if (isBold != null) run.setBold(isBold);
                        if (isItalic != null) run.setItalic(isItalic);
                        if (color != null && !color.isEmpty()) run.setColor(color);
                        
                        log.debug("SRI最终备用替换成功：{} -> {}", placeholder, replacement);
                        break;
                    }
                }
            } catch (Exception fallbackException) {
                log.warn("SRI备用文本替换也失败: {}", fallbackException.getMessage());
            }
        }
    }

    public void replaceSRITextInDocument(XWPFDocument document, String placeholder, String value) {
        try {
            // 替换段落中的文本
            for (XWPFParagraph paragraph : document.getParagraphs()) {
                // 对所有占位符都使用格式保持的替换方法
                replaceTextInParagraphPreservingFormat(paragraph, placeholder, value != null ? value : "");
            }

            // 替换表格中的文本
            for (XWPFTable table : document.getTables()) {
                for (XWPFTableRow row : table.getRows()) {
                    for (XWPFTableCell cell : row.getTableCells()) {
                        for (XWPFParagraph paragraph : cell.getParagraphs()) {
                            // 对所有占位符都使用格式保持的替换方法
                            replaceTextInParagraphPreservingFormat(paragraph, placeholder, value != null ? value : "");
                        }
                    }
                }
            }

            // 替换页眉中的文本
            for (XWPFHeader header : document.getHeaderList()) {
                for (XWPFParagraph paragraph : header.getParagraphs()) {
                    // 对所有占位符都使用格式保持的替换方法
                    replaceTextInParagraphPreservingFormat(paragraph, placeholder, value != null ? value : "");
                }
                for (XWPFTable table : header.getTables()) {
                    for (XWPFTableRow row : table.getRows()) {
                        for (XWPFTableCell cell : row.getTableCells()) {
                            for (XWPFParagraph paragraph : cell.getParagraphs()) {
                                // 对所有占位符都使用格式保持的替换方法
                                replaceTextInParagraphPreservingFormat(paragraph, placeholder, value != null ? value : "");
                            }
                        }
                    }
                }
            }

            // 替换页脚中的文本
            for (XWPFFooter footer : document.getFooterList()) {
                for (XWPFParagraph paragraph : footer.getParagraphs()) {
                    // 对所有占位符都使用格式保持的替换方法
                    replaceTextInParagraphPreservingFormat(paragraph, placeholder, value != null ? value : "");
                }
                for (XWPFTable table : footer.getTables()) {
                    for (XWPFTableRow row : table.getRows()) {
                        for (XWPFTableCell cell : row.getTableCells()) {
                            for (XWPFParagraph paragraph : cell.getParagraphs()) {
                                // 对所有占位符都使用格式保持的替换方法
                                replaceTextInParagraphPreservingFormat(paragraph, placeholder, value != null ? value : "");
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("替换SRI文档占位符失败 [{}]: {}", placeholder, e.getMessage());
        }
    }

    /**
     * 替换SRI段落中的文本
     */
    private void replaceSRIParagraphText(XWPFParagraph paragraph, String placeholder, String value) {
        try {
            String text = paragraph.getText();
            if (text != null && text.contains(placeholder)) {
                String newText = text.replace(placeholder, value != null ? value : "");

                // 保存第一个run的字体格式信息（如果存在的话）
                XWPFRun firstRun = null;
                String fontFamily = null;
                Integer fontSize = null;
                Boolean isBold = null;
                Boolean isItalic = null;
                String color = null;
                
                if (paragraph.getRuns().size() > 0) {
                    firstRun = paragraph.getRuns().get(0);
                    fontFamily = firstRun.getFontFamily();
                    fontSize = firstRun.getFontSize();
                    isBold = firstRun.isBold();
                    isItalic = firstRun.isItalic();
                    color = firstRun.getColor();
                }

                // 清除所有现有的runs
                int runs = paragraph.getRuns().size();
                for (int i = runs - 1; i >= 0; i--) {
                    paragraph.removeRun(i);
                }

                // 创建新的run并设置替换后的文本
                XWPFRun run = paragraph.createRun();
                run.setText(newText);
                
                // 应用保存的字体格式
                if (fontFamily != null) {
                    run.setFontFamily(fontFamily);
                }
                if (fontSize != null && fontSize > 0) {
                    run.setFontSize(fontSize);
                }
                if (isBold != null) {
                    run.setBold(isBold);
                }
                if (isItalic != null) {
                    run.setItalic(isItalic);
                }
                if (color != null && !color.isEmpty()) {
                    run.setColor(color);
                }
            }
        } catch (Exception e) {
            log.warn("替换SRI段落文本失败: {}", e.getMessage());
        }
    }

    /**
     * 格式化SRI小数值
     */
    private String formatSRIDecimalValue(BigDecimal value) {
        if (value == null) {
            return "0.000";
        }
        return String.format("%.3f", value.doubleValue());
    }

    /**
     * 获取SRI等级对应的索引
     */
    private int getSRIGradeIndex(String grade) {
        switch (grade) {
            case "优": return 0;
            case "良": return 1;
            case "中": return 2;
            case "次": return 3;
            case "差": return 4;
            default: return 4;
        }
    }
    
    /**
     * 设置SRI表格整体边框
     */
    private void setSRITableBorders(XWPFTable table) {
        try {
            // 获取或创建表格属性
            if (table.getCTTbl().getTblPr() == null) {
                table.getCTTbl().addNewTblPr();
            }

            // 获取或创建表格边框属性
            if (table.getCTTbl().getTblPr().getTblBorders() == null) {
                table.getCTTbl().getTblPr().addNewTblBorders();
            }

            // 创建边框样式
            org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder border = 
                org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder.Factory.newInstance();
            border.setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STBorder.SINGLE);
            border.setSz(java.math.BigInteger.valueOf(6)); // 外边框稍微粗一点
            border.setSpace(java.math.BigInteger.valueOf(0));
            border.setColor("000000"); // 黑色边框

            // 设置表格外边框
            table.getCTTbl().getTblPr().getTblBorders().setTop(border);
            table.getCTTbl().getTblPr().getTblBorders().setBottom((org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder) border.copy());
            table.getCTTbl().getTblPr().getTblBorders().setLeft((org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder) border.copy());
            table.getCTTbl().getTblPr().getTblBorders().setRight((org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder) border.copy());

            // 创建内边框样式（稍微细一点）
            org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder insideBorder = 
                org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder.Factory.newInstance();
            insideBorder.setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STBorder.SINGLE);
            insideBorder.setSz(java.math.BigInteger.valueOf(4));
            insideBorder.setSpace(java.math.BigInteger.valueOf(0));
            insideBorder.setColor("000000");

            // 设置表格内边框
            table.getCTTbl().getTblPr().getTblBorders().setInsideH(insideBorder);
            table.getCTTbl().getTblPr().getTblBorders().setInsideV((org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder) insideBorder.copy());

        } catch (Exception e) {
            log.warn("设置SRI表格边框失败: {}", e.getMessage());
        }
    }

    /**
     * 设置SRI单元格边框
     */
    private void setSRICellBorders(XWPFTableCell cell) {
        try {
            // 获取或创建单元格属性
            if (cell.getCTTc().getTcPr() == null) {
                cell.getCTTc().addNewTcPr();
            }

            // 获取或创建边框属性
            if (cell.getCTTc().getTcPr().getTcBorders() == null) {
                cell.getCTTc().getTcPr().addNewTcBorders();
            }

            // 设置所有边框（上、下、左、右）
            org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder border = 
                org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder.Factory.newInstance();
            border.setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STBorder.SINGLE);
            border.setSz(java.math.BigInteger.valueOf(4)); // 边框宽度
            border.setSpace(java.math.BigInteger.valueOf(0));
            border.setColor("000000"); // 黑色边框

            // 设置四个边的边框
            cell.getCTTc().getTcPr().getTcBorders().setTop(border);
            cell.getCTTc().getTcPr().getTcBorders().setBottom((org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder) border.copy());
            cell.getCTTc().getTcPr().getTcBorders().setLeft((org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder) border.copy());
            cell.getCTTc().getTcPr().getTcBorders().setRight((org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder) border.copy());

        } catch (Exception e) {
            log.warn("设置SRI单元格边框失败: {}", e.getMessage());
        }
    }

    @Override
    public int deleteRoadCheckSFCByRoadId(Long roadId) {
        return roadCheckSRIMapper.deleteRoadCheckSFCByRoadId(roadId);
    }

    /**
     * 计算SRI Word文档的页数（详细版本，包含pageOne和pageTwo计算）
     * 考虑第一页的特殊行数限制：
     * - 汇总表格第一页只有38行数据，后续页41行
     * - 上/下行表格第一页只有40行数据，后续页42行
     * 
     * @param summaryData 汇总数据
     * @param upData 上行数据
     * @param downData 下行数据
     * @param direction 当前方向
     * @return int数组：[总页数, pageOne, pageTwo]
     */
    private int[] calculateSRIDocumentPagesWithDetails(List<Map<String, Object>> summaryData, List<RoadCheckSRI> upData, 
                                                      List<RoadCheckSRI> downData, String direction) {
        try {
            // 基础页数
            int basePages = 0;
            
            // 计算汇总表格页数
            int summaryRows = summaryData != null ? summaryData.size() : 0;
            int summaryPages = calculateTablePages(summaryRows + 6, 37, 40); // +6是固定的合计行和等级行
            
            // 根据方向确定当前数据行数
            List<RoadCheckSRI> currentDirectionData = "上行".equals(direction) ? upData : downData;
            int dataRows = currentDirectionData != null ? currentDirectionData.size() : 0;
            
            // 计算数据表格页数
            int dataPages = calculateTablePages(dataRows, 39, 41);
            
            // 计算 pageOne = 汇总表格的总页码 + 6
            int pageOne = summaryPages + basePages;
            
            // 计算 pageTwo = 检测数据(上/下行)表格的总页码 + pageOne + 1
            int pageTwo = pageOne + 1;
            
            // 总页数计算
            int totalPages = basePages + summaryPages + dataPages;
            
            log.info("SRI页数计算详情：基础页数={}, 汇总行数={}, 汇总页数={}, {}数据行数={}, 数据页数={}, 总页数={}, pageOne={}, pageTwo={}", 
                     basePages, summaryRows, summaryPages, direction, dataRows, dataPages, totalPages, pageOne, pageTwo);
            
            return new int[]{totalPages, pageOne, pageTwo};
            
        } catch (Exception e) {
            log.warn("计算SRI文档页数失败，使用默认值: {}", e.getMessage());
            return new int[]{10, 12, 25}; // 默认返回[总页数, pageOne, pageTwo]
        }
    }

    /**
     * 计算SRI Word文档的页数
     * 考虑第一页的特殊行数限制：
     * - 汇总表格第一页只有38行数据，后续页41行
     * - 上/下行表格第一页只有40行数据，后续页42行
     * 
     * @param summaryData 汇总数据
     * @param upData 上行数据
     * @param downData 下行数据
     * @param direction 当前方向
     * @return 文档页数
     */
    private int calculateSRIDocumentPages(List<Map<String, Object>> summaryData, List<RoadCheckSRI> upData, 
                                         List<RoadCheckSRI> downData, String direction) {
        int[] results = calculateSRIDocumentPagesWithDetails(summaryData, upData, downData, direction);
        return results[0]; // 返回总页数
    }

    /**
     * 计算表格页数，考虑第一页和后续页的不同行数限制
     * 
     * @param totalRows 总行数
     * @param firstPageRows 第一页可容纳的行数
     * @param otherPageRows 后续页可容纳的行数
     * @return 所需页数
     */
    private int calculateTablePages(int totalRows, int firstPageRows, int otherPageRows) {
        if (totalRows <= 0) {
            return 0;
        }
        
        if (totalRows <= firstPageRows) {
            // 第一页就能容纳所有数据
            return 1;
        } else {
            // 需要多页
            int remainingRows = totalRows - firstPageRows; // 除第一页外的剩余行数
            int additionalPages = (int) Math.ceil((double) remainingRows / otherPageRows); // 额外需要的页数
            return 1 + additionalPages; // 第一页 + 额外页数
        }
    }

    /**
     * 判断文本是否包含字母或数字
     */
    private boolean containsLatinOrDigit(String text) {
        if (text == null || text.isEmpty()) {
            return false;
        }
        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);
            if (Character.isDigit(c) || (c >= 'A' && c <= 'Z') || (c >= 'a' && c <= 'z')) {
                return true;
            }
        }
        return false;
    }
} 